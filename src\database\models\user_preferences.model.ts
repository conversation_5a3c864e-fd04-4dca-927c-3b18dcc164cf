import {
    Table,
    Column,
    Model,
    DataType,
    <PERSON><PERSON><PERSON>,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    Index,
} from "sequelize-typescript";
import User from "./user.model";
import Country from "./country.model";
import City from "./city.model";

interface UserPreferenceI {
    id: number;
    user_id: number;

    // BASIC DETAILS
    country_of_citizenship: string;
    religion: string;
    caste: string;
    surname?: string;
    gotra?: string;
    marital_status?: string;

    // LOCATION
    country_living_in: string;
    city: string;
    residency_status: string;

    // EDUCATION AND CAREER
    education: string;
    employment_status: string;
    profession: string;
    working_for: string;

    // LIFESTYLE
    age_range?: string;
    height_range?: string;
    body_type: string;
    complexion: string;
    diet: string;
    smoke?: string;
    drink?: string;
    disability?: string;

    // ASTRO DETAILS
    countryOfBirth: string;
    birthCity: string;

    // FAMILY DETAILS
    family_type: string;
    father_occupation: string;
    mother_occupation: string;
    number_of_siblings?: string;
    maternal_surname?: string;
    hisGotra?: string;

    // HOBBIES AND INTERESTS
    hobbies: string;
    interests: string;
}

@Table({
    tableName: "user_preferences",
    timestamps: false,
})
class UserPreference extends Model<UserPreferenceI> implements UserPreferenceI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    // BASIC DETAILS
    @AllowNull(true)
    @Column(DataType.JSON)
    country_of_citizenship: string;

    get country_of_citizenship_names(): Promise<string[]> {
        return Country.findAll({
            where: { id: this.country_of_citizenship || [] },
            attributes: ['name']
        }).then(item => item.map(s => s.name));
    }

    @AllowNull(true)
    @Column(DataType.JSON)
    religion: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    caste: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    surname: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    gotra: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    marital_status: string; // "never_married" | "divorced" | "widowed" | "separated"

    // LOCATION
    @AllowNull(true)
    @Column(DataType.JSON)
    country_living_in: string;

    get country_living_in_names(): Promise<string[]> {
        return Country.findAll({
            where: { id: this.country_living_in || [] },
            attributes: ['name']
        }).then(item => item.map(s => s.name));
    }

    @AllowNull(true)
    @Column(DataType.JSON)
    city: string;

    get city_names(): Promise<string[]> {
        return City.findAll({
            where: { id: this.city || [] },
            attributes: ['name']
        }).then(item => item.map(s => s.name));
    }

    @AllowNull(true)
    @Column(DataType.JSON)
    residency_status: string;

    // EDUCATION AND CAREER
    @AllowNull(true)
    @Column(DataType.JSON)
    education: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    employment_status: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    profession: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    working_for: string;

    // LIFESTYLE
    @AllowNull(true)
    @Column(DataType.JSON)
    age_range: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    height_range: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    body_type: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    complexion: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    diet: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    smoke: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    drink: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    disability: string;

    // ASTRO DETAILS
    @AllowNull(true)
    @Column(DataType.JSON)
    countryOfBirth: string;

    get countryOfBirth_names(): Promise<string[]> {
        return Country.findAll({
            where: { id: this.countryOfBirth || [] },
            attributes: ['name']
        }).then(item => item.map(s => s.name));
    }


    @AllowNull(true)
    @Column(DataType.JSON)
    birthCity: string;

    get birthCity_names(): Promise<string[]> {
        return City.findAll({
            where: { id: this.birthCity || [] },
            attributes: ['name']
        }).then(item => item.map(s => s.name));
    }

    // FAMILY DETAILS
    @AllowNull(true)
    @Column(DataType.JSON)
    family_type: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    father_occupation: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    mother_occupation: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    number_of_siblings: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    maternal_surname: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    hisGotra: string;

    // HOBBIES AND INTERESTS
    @AllowNull(true)
    @Column(DataType.JSON)
    hobbies: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    interests: string;
}

export default UserPreference;

