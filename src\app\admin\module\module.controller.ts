import { Request, Response } from "express";
import httpStatus from "http-status";
import jwt from "jsonwebtoken";
import ModuleService from "./module.service";
import catchAsync from "../../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../../utils/response";
import httpMessages from "../../../config/httpMessages";
import Module from "../../../database/models/modules.model";
import ApiError from "../../../utils/ApiError";

export default class ModuleController {
  static moduleService = ModuleService;
  constructor() {}

  static getAll = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search } = request.query;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
      };
      const modules = await this.moduleService.getModules(option);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.MODULES.SUCCESS,
        data: modules,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const moduleData = { ...request.body };
      const module: Module = await this.moduleService.createModule(moduleData);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.MODULES.ADD_SUCCESS,
        data: module,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static showById = catchAsync(async (request: Request, response: Response) => {
    try {
      const moduleId: number = parseInt(request.params.id, 10);
      const module = await this.moduleService.getModuleById(moduleId);
      if (!module) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          httpMessages.MODULES.NOT_FOUND
        );
      }
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.MODULES.DETAILS.SUCCESS,
        data: module,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static update = catchAsync(async (request: Request, response: Response) => {
    try {
      const moduleId: number = parseInt(request.params.id, 10);
      const moduleData = { ...request.body };

      const module = await this.moduleService.updateModuleById(
        moduleId,
        moduleData
      );
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.MODULES.UPDATE_SUCCESS,
        data: module,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static delete = catchAsync(async (request: Request, response: Response) => {
    try {
      const moduleId: number = parseInt(request.params.id, 10);
      await this.moduleService.deleteModuleById(moduleId);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.MODULES.DELETE,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });
}
