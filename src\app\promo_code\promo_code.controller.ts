import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";
import PromoCodeService from "./promo_code.service";
import SubscriptionPlan from "../../database/models/subscription_plans.mode";

export default class PromoCodeController {
    static promoCodeService = PromoCodeService;
    constructor() { }

    /**
     * Validate a promo code
     */
    static validatePromoCode = catchAsync(async (request: Request, response: Response) => {
        try {
            const { code, plan_id } = request.body;
            const user_id = request.decoded;

            // Get plan details to validate promo code
            const plan = await SubscriptionPlan.findByPk(plan_id);
            
            if (!plan || !plan.is_active) {
                return errorResponse(response, {
                    statusCode: httpStatus.NOT_FOUND,
                    message: "Subscription plan not found or inactive"
                });
            }

            const result = await this.promoCodeService.validatePromoCode({
                code,
                user_id,
                plan_id,
                plan_price: plan.price
            });

            if (!result.isValid) {
                return sentResponse(response, {
                    statusCode: httpStatus.BAD_REQUEST,
                    message: result.message || "Invalid promo code",
                    data: { isValid: false }
                });
            }

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Promo code is valid",
                data: {
                    isValid: true,
                    promoCode: {
                        id: result.promoCode?.id,
                        code: result.promoCode?.code,
                        description: result.promoCode?.description,
                        discount_type: result.promoCode?.discount_type,
                        discount_value: result.promoCode?.discount_value
                    },
                    originalAmount: plan.price,
                    discountAmount: result.discountAmount,
                    finalAmount: result.finalAmount
                }
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    /**
     * Get user's promo code usage history
     */
    static getUserPromoCodeUsage = catchAsync(async (request: Request, response: Response) => {
        try {
            const user_id = request.decoded;

            const usageHistory = await this.promoCodeService.getUserPromoCodeUsage(user_id);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Promo code usage history retrieved successfully",
                data: usageHistory
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    /**
     * Get promo code by code (for admin or public info)
     */
    static getPromoCodeByCode = catchAsync(async (request: Request, response: Response) => {
        try {
            const { code } = request.params;

            const promoCode = await this.promoCodeService.getPromoCodeByCode(code);

            if (!promoCode) {
                return sentResponse(response, {
                    statusCode: httpStatus.NOT_FOUND,
                    message: "Promo code not found",
                    data: null
                });
            }

            // Return limited information for security
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Promo code found",
                data: {
                    code: promoCode.code,
                    description: promoCode.description,
                    discount_type: promoCode.discount_type,
                    discount_value: promoCode.discount_value,
                    minimum_purchase_amount: promoCode.minimum_purchase_amount,
                    maximum_discount_amount: promoCode.maximum_discount_amount,
                    expiry_date: promoCode.expiry_date,
                    is_active: promoCode.is_active
                }
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });
}
