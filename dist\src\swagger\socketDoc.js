"use strict";
/**
 * @swagger
 * tags:
 *   name: Socket.io
 *   description: Real-time communication using Socket.io
 */
/**
 * @swagger
 * components:
 *   schemas:
 *     SocketAuthentication:
 *       type: object
 *       required:
 *         - token
 *       properties:
 *         token:
 *           type: string
 *           description: JWT token for authentication
 *           example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *
 *     PrivateMessage:
 *       type: object
 *       required:
 *         - chatId
 *         - receiverId
 *         - content
 *       properties:
 *         chatId:
 *           type: integer
 *           description: ID of the chat
 *           example: 1
 *         receiverId:
 *           type: integer
 *           description: ID of the message recipient
 *           example: 2
 *         content:
 *           type: string
 *           description: Message content (will be encrypted)
 *           example: "Hello, how are you?"
 *
 *     MessageRead:
 *       type: object
 *       required:
 *         - messageId
 *       properties:
 *         messageId:
 *           type: integer
 *           description: ID of the message to mark as read
 *           example: 42
 *
 *     NewMessageEvent:
 *       type: object
 *       properties:
 *         messageId:
 *           type: integer
 *           description: ID of the new message
 *           example: 42
 *         chatId:
 *           type: integer
 *           description: ID of the chat
 *           example: 1
 *         senderId:
 *           type: integer
 *           description: ID of the message sender
 *           example: 3
 *         content:
 *           type: string
 *           description: Encrypted message content
 *           example: "U2FsdGVkX1+A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0="
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the message was created
 *           example: "2023-06-15T14:30:00.000Z"
 *
 *     MessageDeliveredEvent:
 *       type: object
 *       properties:
 *         messageId:
 *           type: integer
 *           description: ID of the delivered message
 *           example: 42
 *
 *     MessageReadEvent:
 *       type: object
 *       properties:
 *         messageId:
 *           type: integer
 *           description: ID of the read message
 *           example: 42
 *
 *     UserStatusEvent:
 *       type: object
 *       properties:
 *         userId:
 *           type: integer
 *           description: ID of the user whose status changed
 *           example: 3
 *         status:
 *           type: string
 *           enum: [online, offline]
 *           description: New status of the user
 *           example: "online"
 *
 *     ErrorEvent:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *           description: Error message
 *           example: "Failed to send message"
 */
/**
 * @swagger
 * /socket.io:
 *   get:
 *     tags:
 *       - Socket.io
 *     summary: Socket.io Connection Endpoint
 *     description: |
 *       This is the WebSocket endpoint for Socket.io connections.
 *
 *       **Note:** This endpoint is not meant to be accessed directly via HTTP.
 *       It's used by Socket.io clients to establish WebSocket connections.
 *     responses:
 *       101:
 *         description: Switching Protocols - WebSocket connection established
 */
/**
 * @swagger
 * /socket.io/client-implementation:
 *   get:
 *     tags:
 *       - Socket.io
 *     summary: Socket.io Client Implementation Guide
 *     description: |
 *       # Socket.io Client Implementation Guide
 *
 *       ## Connection and Authentication
 *
 *       ```javascript
 *       // Connect to Socket.io server with authentication
 *       const socket = io('http://your-server-url', {
 *         auth: {
 *           token: 'your-jwt-token'
 *         }
 *       });
 *
 *       // Handle connection errors
 *       socket.on('connect_error', (error) => {
 *         console.error('Connection error:', error.message);
 *       });
 *       ```
 *
 *       ## Sending Messages
 *
 *       ```javascript
 *       // Send a private message
 *       socket.emit('private_message', {
 *         chatId: 1,
 *         receiverId: 2,
 *         content: 'Hello, how are you?'
 *       });
 *
 *       // Mark a message as read
 *       socket.emit('message_read', {
 *         messageId: 42
 *       });
 *       ```
 *
 *       ## Receiving Events
 *
 *       ```javascript
 *       // Receive a new message
 *       socket.on('new_message', (data) => {
 *         console.log('New message received:', data);
 *         // data contains: messageId, chatId, senderId, content, createdAt
 *       });
 *
 *       // Message delivered notification
 *       socket.on('message_delivered', (data) => {
 *         console.log('Message delivered:', data.messageId);
 *       });
 *
 *       // Message read notification
 *       socket.on('message_read', (data) => {
 *         console.log('Message read:', data.messageId);
 *       });
 *
 *       // User status changes (online/offline)
 *       socket.on('user_status', (data) => {
 *         console.log(`User ${data.userId} is now ${data.status}`);
 *       });
 *
 *       // Error handling
 *       socket.on('error', (data) => {
 *         console.error('Socket error:', data.message);
 *       });
 *       ```
 *
 *       ## Disconnection
 *
 *       ```javascript
 *       // Disconnect from the server
 *       socket.disconnect();
 *       ```
 *     responses:
 *       200:
 *         description: Implementation guide for Socket.io client
 */
/**
 * @swagger
 * /socket.io/events:
 *   get:
 *     tags:
 *       - Socket.io
 *     summary: Socket.io Events Documentation
 *     description: |
 *       # Socket.io Events Documentation
 *
 *       ## Client-to-Server Events
 *
 *       | Event Name | Description | Payload |
 *       |------------|-------------|---------|
 *       | `private_message` | Send a private message to another user | `{ chatId: number, receiverId: number, content: string }` |
 *       | `message_read` | Mark a message as read | `{ messageId: number }` |
 *
 *       ## Server-to-Client Events
 *
 *       | Event Name | Description | Payload |
 *       |------------|-------------|---------|
 *       | `new_message` | Receive a new message | `{ messageId: number, chatId: number, senderId: number, content: string, createdAt: string }` |
 *       | `message_delivered` | Notification that a message was delivered | `{ messageId: number }` |
 *       | `message_read` | Notification that a message was read | `{ messageId: number }` |
 *       | `user_status` | User status change notification | `{ userId: number, status: 'online' \| 'offline' }` |
 *       | `error` | Error notification | `{ message: string }` |
 *
 *       ## Authentication
 *
 *       Socket.io connections require authentication using a JWT token. The token should be provided in the `auth` object when establishing the connection:
 *
 *       ```javascript
 *       const socket = io('http://your-server-url', {
 *         auth: {
 *           token: 'your-jwt-token'
 *         }
 *       });
 *       ```
 *
 *       ## End-to-End Encryption
 *
 *       Message content is encrypted on the server side before being stored in the database and sent to recipients. The encryption uses AES-256-CBC with a server-side key and IV.
 *     responses:
 *       200:
 *         description: Documentation of Socket.io events
 */
