"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validate = void 0;
const joi_1 = __importDefault(require("joi"));
const http_status_1 = __importDefault(require("http-status"));
const response_1 = __importDefault(require("../utils/response"));
const ApiError_1 = __importDefault(require("../utils/ApiError"));
const pick_1 = require("../utils/pick");
const validate = (schema) => (request, response, next) => {
    try {
        const validSchema = (0, pick_1.pick)(schema, ["params", "query", "body"]);
        const object = (0, pick_1.pick)(request, Object.keys(validSchema));
        const { value, error } = joi_1.default.compile(validSchema)
            .prefs({ errors: { label: "key" }, abortEarly: false })
            .validate(object);
        if (error) {
            const errorMessage = error.details.map((details) => details.message);
            return (0, response_1.default)(response, new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage));
        }
        Object.assign(request, value);
        return next();
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
};
exports.validate = validate;
