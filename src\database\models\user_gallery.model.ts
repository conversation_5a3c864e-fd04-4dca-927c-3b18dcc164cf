import {
    Table,
    Column,
    Model,
    DataType,
    PrimaryKey,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    Index,
} from "sequelize-typescript";
import User from "./user.model";

interface UserGalleryI {
    id: number;
    user_id: number;
    gallery_image?: string;
}

@Table({
    tableName: "user_gallery",
    timestamps: false,
})
class UserGallery extends Model<UserGalleryI> implements UserGalleryI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User,  { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    @AllowNull(true)
    @Column(DataType.TEXT)
    profile_bio?: string;

    @AllowNull(true)
    @Column(DataType.STRING(255))
    profile_image?: string;

    @AllowNull(true)
    @Column(DataType.STRING(255))
    gallery_image?: string;
}

export default UserGallery;