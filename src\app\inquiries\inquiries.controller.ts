import { Request, Response } from "express";
import httpStatus from "http-status";
import InquiryService from "./inquiries.service";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";

export default class InquiryController {
  static inquiryService = InquiryService;

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const body = request.body;
      const inquiry = await this.inquiryService.createInquiry(body);
      return sentResponse(response, {
        statusCode: httpStatus.CREATED,
        message: "Inquiry submitted successfully",
        data: inquiry,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static getAll = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search, status } = request.query;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
        status: status ? (status as string) : undefined,
      };
      const inquiries = await this.inquiryService.getInquiries(option);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Inquiries retrieved successfully",
        data: inquiries,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static showById = catchAsync(async (request: Request, response: Response) => {
    try {
      const { id } = request.params;
      const inquiry = await this.inquiryService.getInquiryById(parseInt(id));
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Inquiry retrieved successfully",
        data: inquiry,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static updateStatus = catchAsync(async (request: Request, response: Response) => {
    try {
      const { id } = request.params;
      const { status, admin_notes } = request.body;
      const changed_by_admin_id = request.decoded;
      const inquiry = await this.inquiryService.updateInquiryStatus(parseInt(id), status, admin_notes, changed_by_admin_id);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Inquiry status updated successfully",
        data: inquiry,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static delete = catchAsync(async (request: Request, response: Response) => {
    try {
      const { id } = request.params;
      await this.inquiryService.deleteInquiry(parseInt(id));
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Inquiry deleted successfully",
        data: null,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });
}





