"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.roleValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.roleValidation = {
    body: joi_1.default.object().keys({
        role_name: joi_1.default.string().trim().required().messages({
            "string.empty": "Role name is required.",
            "string.base": "Role name must be a string.",
        }),
        description: joi_1.default.string().trim().optional().allow(null, "").messages({
            "string.base": "Description must be a string.",
        }),
    }),
};
