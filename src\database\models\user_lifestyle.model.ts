import {
    Table,
    Column,
    Model,
    DataType,
    PrimaryKey,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    Index,
} from "sequelize-typescript";
import User from "./user.model";

interface UserLifestyleI {
    id: number;
    user_id: number;
    age:number;
    height_cm: number;
    body_type: string;
    complexion: string;
    diet: string;
    smoking_habit?: string;
    drinking_habit?: string;
    any_disability?: string;
}

@Table({
    tableName: "user_lifestyle",
    timestamps: false,
})
class UserLifestyle extends Model<UserLifestyleI> implements UserLifestyleI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    @AllowNull(true)
    @Column(DataType.INTEGER)
    age: number;

    @AllowNull(true)
    @Column(DataType.INTEGER)
    height_cm: number;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    body_type: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    complexion: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    diet: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    smoking_habit?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    drinking_habit?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    any_disability?: string;
}

export default UserLifestyle