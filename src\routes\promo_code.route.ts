import express from "express";
import PromoCodeController from "../app/promo_code/promo_code.controller";
import { auth } from "../middlewares/auth";
import { validate } from "../middlewares/middleware";
import {
    validatePromoCodeValidation,
    getPromoCodeByCodeValidation
} from "../validations/promo_code.validation";

const router = express.Router();

// All routes require authentication
router.use(auth);

// Validate promo code
router.post("/validate", validate(validatePromoCodeValidation), PromoCodeController.validatePromoCode);

// Get user's promo code usage history
router.get("/usage-history", PromoCodeController.getUserPromoCodeUsage);

// Get promo code by code (public info)
router.get("/:code", validate(getPromoCodeByCodeValidation), PromoCodeController.getPromoCodeByCode);

export default router;
