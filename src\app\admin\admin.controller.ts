import { Request, Response } from "express";
import httpStatus from "http-status";
import jwt from "jsonwebtoken";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";
import ApiError from "../../utils/ApiError";
import httpMessages from "../../config/httpMessages";

import { config } from "../../config/config";
import AdminService from "./admin.service";
import Admin, { IAdmin } from "../../database/models/admin.model";
import TokenService from "../../common/services/token.service";

export default class AdminController {
  static adminService = AdminService;
  static tokenService = TokenService;
  constructor() {}

  static login = catchAsync(async (request: Request, response: Response) => {
    try {
      const { username, password } = request.body;
      const admin: IAdmin | any =
        await this.adminService.loginUserWithUsernameAndPassword(
          username,
          password
        );
      const tokens = await this.tokenService.generateAuthTokens(admin);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.LOGIN.SUCCESS,
        data: { ...admin, token: tokens },
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static getAll = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search } = request.query;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
      };
      const admins = await this.adminService.getAdmins(option);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.SUCCESS,
        data: admins,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const adminData = { ...request.body };
      const admin: Admin = await this.adminService.createAdmin(adminData);
      // admin.permissions
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.REGISTER.SUCCESS,
        data: admin,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static showById = catchAsync(async (request: Request, response: Response) => {
    try {
      const { adminId } = request.params;
      if (adminId === "me") {
        let decoded = request.decoded;
        console.log("decoded: ===", decoded);
        if (!decoded) {
          throw new ApiError(
            httpStatus.UNAUTHORIZED,
            httpMessages.USER.AUTH.UNAUTHORIZED
          );
        }
        // Set the id to the decoded id
        const admin: Admin | null = await this.adminService.getAdminById(
          decoded
        );
        console.log("admin: ", admin);
        if (!admin) {
          throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER.NOT_FOUND);
        }
        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: httpMessages.USER.DETAILS.SUCCESS,
          data: admin,
        });
      }

      const admin = await this.adminService.getAdminById(parseInt(adminId));
      if (!admin) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER.NOT_FOUND);
      }
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.DETAILS.SUCCESS,
        data: admin,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static update = catchAsync(async (request: Request, response: Response) => {
    try {
      const adminId: number = parseInt(request.params.adminId, 10);
      const adminData = { ...request.body };
      console.log("adminData: ", adminData);

      const admin = await this.adminService.updateAdminById(adminId, adminData);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.UPDATE_SUCCESS,
        data: admin,
      });
    } catch (error) {
      console.log("error: ", error);
      return errorResponse(response, error);
    }
  });

  static delete = catchAsync(async (request: Request, response: Response) => {
    try {
      const adminId: number = parseInt(request.params.adminId, 10);
      await this.adminService.deleteAdminById(adminId);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.DELETE,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });
}
