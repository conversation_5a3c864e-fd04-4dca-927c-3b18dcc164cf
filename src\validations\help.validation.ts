import Joi from "joi";

export const createHelpQuestionValidation = {
  body: Joi.object().keys({
    question: Joi.string().required().max(255).messages({
      "string.base": "Question must be a string.",
      "string.max": "Question cannot exceed 255 characters.",
      "any.required": "Question is required.",
    }),
    description: Joi.string().required().messages({
      "string.base": "Description must be a string.",
      "any.required": "Description is required.",
    }),
    category_id: Joi.number().required().messages({
      "number.base": "Category ID must be a number.",
      "any.required": "Category ID is required.",
    }),
  }),
};

export const updateHelpQuestionValidation = {
  body: Joi.object().keys({
    question: Joi.string().max(255).messages({
      "string.base": "Question must be a string.",
      "string.max": "Question cannot exceed 255 characters.",
    }),
    answer: Joi.string().messages({
      "string.base": "Answer must be a string.",
    }),
    category_id: Joi.number().messages({
      "number.base": "Category ID must be a number.",
    }),
  }),
};

export const getHelpQuestionByIdValidation = {
  params: Joi.object().keys({
    id: Joi.number().required().messages({
      "number.base": "ID must be a number.",
      "any.required": "ID is required.",
    }),
  }),
};

export const deleteHelpQuestionValidation = {
  params: Joi.object().keys({
    id: Joi.number().required().messages({
      "number.base": "ID must be a number.",
      "any.required": "ID is required.",
    }),
  }),
};

export const createHelpCategoryValidation = {
  body: Joi.object().keys({
    title: Joi.string().required().max(255).messages({
      "string.base": "Title must be a string.",
      "string.max": "Title cannot exceed 255 characters.",
      "any.required": "Title is required.",
    }),
    icon: Joi.string().max(255).messages({
      "string.base": "Icon must be a string.",
      "string.max": "Icon cannot exceed 255 characters.",
    }),
  }),
};



