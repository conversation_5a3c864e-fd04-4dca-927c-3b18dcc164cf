import {
    AllowNull,
    AutoIncrement,
    Column,
    DataType,
    Default,
    Model,
    PrimaryKey,
    Table,
    Index,
} from "sequelize-typescript";

export interface KhaltiWebhookEventI {
    id?: number;
    event_id: string;
    event_type: string;
    resource_type: string;
    resource_id?: string;
    summary: string;
    event_data: any;
    processed: boolean;
    processed_at?: Date;
    error_message?: string;
    created_at?: Date;
    updated_at?: Date;
}

@Table({
    tableName: "khalti_webhook_events",
    timestamps: true,
    underscored: true,
})
class KhaltiWebhookEvent extends Model<KhaltiWebhookEventI> implements KhaltiWebhookEventI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @AllowNull(false)
    @Column(DataType.STRING(100))
    event_id: string;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    event_type: string;

    @AllowNull(false)
    @Column(DataType.STRING(50))
    resource_type: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    resource_id?: string;

    @AllowNull(false)
    @Column(DataType.STRING(255))
    summary: string;

    @AllowNull(false)
    @Column(DataType.JSON)
    event_data: any;

    @AllowNull(false)
    @Default(false)
    @Column(DataType.BOOLEAN)
    processed: boolean;

    @AllowNull(true)
    @Column(DataType.DATE)
    processed_at?: Date;

    @AllowNull(true)
    @Column(DataType.TEXT)
    error_message?: string;
}

export default KhaltiWebhookEvent;
