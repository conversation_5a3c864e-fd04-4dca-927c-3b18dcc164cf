import { Request, Response } from "express";
import httpStatus from "http-status";
import PrivacySettingService from "./privacy_setting.service";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";
import httpMessages from "../../config/httpMessages";
import PrivacySetting from "../../database/models/privacy_settings.model";
import ApiError from "../../utils/ApiError";
import UserService from "../user/user.service";
import EmailService from "../../common/services/email.service";
import TokenService from "../../common/services/token.service";
import { EmailTemplate } from "../../config/mailMessages";
import axios from "axios";
import UserVerification from "../../database/models/user_verifications.model";
import User from "../../database/models/user.model";

export default class PrivacySettingController {
    static privacySettingService = PrivacySettingService;
    static userService = UserService;
    static emailService = EmailService;
    static tokenService = TokenService;
    constructor() { }

    static getAll = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page, limit, search } = request.query;
            const userId = request.decoded;
            const option = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
                search: search ? (search as string) : "",
                userId: userId,
            };
            const list = await this.privacySettingService.getPrivacySettings(option);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Privacy settings retrieved successfully',
                data: list,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static create = catchAsync(async (request: Request, response: Response) => {
        try {
            const body = { ...request.body };
            const userId = request.decoded;
            body['user_id'] = userId;
            const craetedData: PrivacySetting = await this.privacySettingService.createPrivacySetting(body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Privacy setting created successfully',
                data: craetedData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static showById = catchAsync(async (request: Request, response: Response) => {
        try {
            const Id: number = parseInt(request.params.id, 10);
            const details = await this.privacySettingService.getPrivacySettingById(Id);
            if (!details) {
                throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER_SUBSCRIPTION.NOT_FOUND);
            }
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Privacy setting retrieved successfully',
                data: details,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static update = catchAsync(async (request: Request, response: Response) => {
        try {
            const Id: number = parseInt(request.params.id, 10);
            const body = { ...request.body };

            const updatedData = await this.privacySettingService.updatePrivacySettingById(Id, body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Privacy setting updated successfully',
                data: updatedData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static delete = catchAsync(async (request: Request, response: Response) => {
        try {
            const id: number = parseInt(request.params.id, 10);
            await this.privacySettingService.deletePrivacySettingById(id);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Privacy setting deleted successfully',
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static hideProfile = catchAsync(async (request: Request, response: Response) => {
        try {
            const body = { ...request.body };
            const userId = request.decoded;
            body['user_id'] = userId;
            const craetedData: PrivacySetting = await this.privacySettingService.hideProfile(body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Profile hidden successfully',
                data: craetedData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static deleteProfile = catchAsync(async (request: Request, response: Response) => {
        try {
            const body = { ...request.body };
            const userId = request.decoded;
            body['user_id'] = userId;
            const craetedData: PrivacySetting = await this.privacySettingService.deleteProfile(body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Profile deleted successfully',
                data: craetedData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static verifyEmailOtp = catchAsync(async (request: Request, response: Response) => {
        try {
            const { email, otp } = request.body;
            const userId = request.decoded;

            const user = await UserVerification.findOne({
                where: { user_id: userId },
            });

            if (!user || !user.email_otp || !user.email_otp_expiry) {
                throw new ApiError(httpStatus.NOT_FOUND, httpMessages.OTP.INVALID_OTP_NOT_FOUND);
            };

            if (user.email_otp !== otp) {
                throw new ApiError(httpStatus.BAD_REQUEST, httpMessages.OTP.INVALID_OTP);
            };


            if (new Date() > new Date(user.email_otp_expiry)) {
                throw new ApiError(httpStatus.GONE, httpMessages.OTP.INVALID_OTP_EXPIRED);
            };

            user.is_email_verified = true;
            user.email_otp = '';
            user.email_otp_expiry = null;
            user.email = email;
            await user.save();
            const userTable = await User.update({ email: email }, { where: { id: userId } });
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.OTP.EMAIL_OTP_VERIFIED,
                data: { ...userTable },
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static resendEmailOtp = catchAsync(async (request: Request, response: Response) => {
        try {
            const { email } = request.body;
            const checkDublicate = await this.userService.getUserByEmail(email);
            if (checkDublicate) {
                throw new ApiError(httpStatus.BAD_REQUEST, httpMessages.REGISTER.EMAIL_ALREADY_TAKEN);
            }
            let userId = request.decoded;
            const otp = Math.floor(100000 + Math.random() * 900000).toString();
            const expiryMinutes = 15;
            const otpExpiry = new Date(Date.now() + 15 * 60 * 1000);

            let html: any = await EmailTemplate({
                type: "SENT_OTP",
                data: {
                    userName: email,
                    otp: otp,
                    expiryMinutes: expiryMinutes
                },
            });
            const mailData = {
                to_addresses: [email],
                from_address: "<EMAIL>",
                from_name: "Bar Badhu",
                subject: "Bar Badhu - OTP Verification!",
                body: html,
                body_type: "html",
            };
            const apiUrl: string | undefined = process.env.MAIL_API_URL || 'https://www.tangomycp.com/amazon-ses/public/api/send-mail';
            await axios.post(apiUrl, mailData);

            // 2. Save OTP to user table (you should update your model accordingly)
            const verification = await UserVerification.findOne({
                where: { user_id: userId },
            });
            if (!verification) {
                throw new ApiError(httpStatus.NOT_FOUND, "User not found");
            }
            const userVerificationBody: any = {
                user_id: userId,
                email_otp: otp,
                email_otp_expiry: otpExpiry,
            };
            const user = await verification.update({ ...userVerificationBody });
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.OTP.RESENT_OTP,
                data: null,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });


      static sendPhoneOtp = catchAsync(async (request: Request, response: Response) => {
        try {
            const { phone,phone_code } = request.body;
            const userId = request.decoded;
            const verification = await this.privacySettingService.sendPhoneOtp(userId, phone,phone_code);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "OTP sent successfully",
                data: verification,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static verifyPhoneOtp = catchAsync(async (request: Request, response: Response) => {
        try {
            const { phone,phone_code, otp } = request.body;
            const userId = request.decoded;
            const verification = await this.privacySettingService.verifyPhoneOtp(userId, phone, otp,phone_code);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Phone verified successfully",
                data: verification,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });



    
}
