import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import ChatService from "./chat.service";
import MessageService from "../message/message.service";
import errorResponse, { sentResponse } from "../../utils/response";

export default class ChatController {
  static chatService = ChatService;
  static messageService = MessageService;

  /**
   * Create a new chat
   * @route POST /api/chats
   */
  static createChat = catchAsync(async (request: Request, response: Response) => {
    try {
      const { user_id } = request.body;
      const user1Id = request.decoded;
      
      const chat = await this.chatService.createChat(user1Id, user_id);
      
      return sentResponse(response, {
        statusCode: httpStatus.CREATED,
        message: "Chat created successfully",
        data: chat,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  /**
   * Get chat by ID
   * @route GET /api/chats/:id
   */
  static getChatById = catchAsync(async (request: Request, response: Response) => {
    try {
      const chatId = parseInt(request.params.id);
      const userId = request.decoded;
      
      const chat = await this.chatService.getChatById(chatId,userId);
      
      // Check if user is part of the chat
      if (chat.user1_id !== userId && chat.user2_id !== userId) {
        return errorResponse(response, {
          statusCode: httpStatus.FORBIDDEN,
          message: "You do not have permission to access this chat",
        });
      }
      
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Chat retrieved successfully",
        data: chat,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  /**
   * Get all chats for the authenticated user
   * @route GET /api/chats
   */
  static getUserChats = catchAsync(async (request: Request, response: Response) => {
    try {
      const userId = request.decoded;
      
      const chats = await this.chatService.getUserChats(userId);
      
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Chats retrieved successfully",
        data: chats,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  /**
   * Get chat messages
   * @route GET /api/chats/:id/messages
   */
  static getChatMessages = catchAsync(async (request: Request, response: Response) => {
    try {
      const chatId = parseInt(request.params.id);
      const userId = request.decoded;
      
      // Verify user has access to this chat
      const chat = await this.chatService.getChatById(chatId,userId);
      if (chat.user1_id !== userId && chat.user2_id !== userId) {
        return errorResponse(response, {
          statusCode: httpStatus.FORBIDDEN,
          message: "You do not have permission to access this chat",
        });
      }
      
      const page = parseInt(request.query.page as string) || 1;
      const limit = parseInt(request.query.limit as string) || 20;
      
      const messages = await this.messageService.getChatMessages(chatId, page, limit);
      
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Messages retrieved successfully",
        data: messages,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  /**
   * Set auto-delete days for a chat
   * @route PUT /api/chats/:id/auto-delete
   */
  static setAutoDeleteDays = catchAsync(async (request: Request, response: Response) => {
    try {
      const chatId = parseInt(request.params.id);
      const userId = request.decoded;
      const { days } = request.body;
      
      // Verify user has access to this chat
      const chat = await this.chatService.getChatById(chatId, userId);
      if (chat.user1_id !== userId && chat.user2_id !== userId) {
        return errorResponse(response, {
          statusCode: httpStatus.FORBIDDEN,
          message: "You do not have permission to modify this chat",
        });
      }
      
      const updatedChat = await this.chatService.setAutoDeleteDays(chatId, days);
      
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Auto-delete days set successfully",
        data: updatedChat,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  /**
   * Delete a chat
   * @route DELETE /api/chats/:id
   */
  static deleteChat = catchAsync(async (request: Request, response: Response) => {
    try {
      const chatId = parseInt(request.params.id);
      const userId = request.decoded.id;
      
      await this.chatService.deleteChat(chatId, userId);
      
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Chat deleted successfully",
        data: null,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });
}
