import express, { Request, Response } from "express";
import cors from "cors";
import fileUpload from "express-fileupload";
import path from "path";
import http from "http";
import routes from "../routes";
import { swaggerAuth, swaggerSpec, swaggerUiOptions } from "../swagger";
import swaggerUi from "swagger-ui-express";
import _ from "../types/express";
import { encryptResponseMiddleware } from "../middlewares/EncryptionResponse";
import { initializeSocket } from "../socket";
import { initScheduler } from "../config/scheduler";
const app = express();
const corsConfig = { origin: "*" };

const server = http.createServer(app);

// Initialize Socket.io
initializeSocket(server);

// Initialize Cron Jobs
// const cronService = CronJobService.getInstance();
// cronService.initializeJobs();
initScheduler();


app.use("/uploads", express.static(path.join(__dirname, "../../uploads")));

app.get("/health-check", (req: Request, res: Response) => {
  console.log("A healthy result.");
  res.send("A healthy result.");
});

app.use(
  "/api-docs",
  swaggerAuth,
  swaggerUi.serve,
  swaggerUi.setup(swaggerSpec, swaggerUiOptions)
);

app.use(express.json());
app.use(cors(corsConfig));
app.use(fileUpload());

app.use(require("cookie-parser")());
app.use(require("body-parser").urlencoded({ extended: true }));
app.use(
  require("express-session")({
    secret: "keyboard cat",
    resave: true,
    saveUninitialized: true,
  })
);

// app.use(encryptResponseMiddleware);

app.use("/", routes);

export default server;
