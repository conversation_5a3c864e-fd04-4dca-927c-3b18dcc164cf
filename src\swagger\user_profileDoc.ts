/**
 * @swagger
 * /user_profile:
 *   get:
 *     tags:
 *       - User Profile
 *     summary: Get all user profiles
 *     description: Retrieve a list of user profiles with pagination, search, and advanced filters.
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           example: 1
 *         description: Page number for pagination.
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Number of profiles per page.
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           example: "ravi"
 *         description: Search by first name.
 *       - in: query
 *         name: filters
 *         schema:
 *           type: object
 *           properties:
 *             religion:
 *               type: string
 *               example: Hindu
 *             caste:
 *               type: string
 *               example: Brahmin
 *             gotra:
 *               type: string
 *               example: Bharadwaj
 *             marital_status:
 *               type: string
 *               example: Single
 *             country_of_citizenship:
 *               type: string
 *               example: India
 *             city:
 *               type: string
 *               example: Mumbai
 *             country_living_in:
 *               type: string
 *               example: India
 *             education:
 *               type: string
 *               example: B.Tech
 *             profession:
 *               type: string
 *               example: Engineer
 *             height_cm:
 *               type: integer
 *               example: 170
 *             body_type:
 *               type: string
 *               example: Athletic
 *             complexion:
 *               type: string
 *               example: Fair
 *             diet:
 *               type: string
 *               example: Vegetarian
 *             smoke:
 *               type: string
 *               example: No
 *             drink:
 *               type: string
 *               example: Occasionally
 *             disability:
 *               type: string
 *               example: None
 *             city_of_birth:
 *               type: string
 *               example: Delhi
 *             country_of_birth:
 *               type: string
 *               example: India
 *             family_type:
 *               type: string
 *               example: Nuclear
 *             father_occupation:
 *               type: string
 *               example: Businessman
 *             hobbies:
 *               type: string
 *               example: Reading
 *             interests:
 *               type: string
 *               example: Travelling
 *     responses:
 *       200:
 *         description: Successfully retrieved the user profiles.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalItems:
 *                   type: integer
 *                   example: 100
 *                 totalPages:
 *                   type: integer
 *                   example: 10
 *                 currentPage:
 *                   type: integer
 *                   example: 1
 *                 user:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       firstName:
 *                         type: string
 *                         example: Ravi
 *                       lastName:
 *                         type: string
 *                         example: Sharma
 *                       email:
 *                         type: string
 *                         example: <EMAIL>
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         example: 2025-05-01T10:00:00Z
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: 2025-05-05T10:00:00Z
 *                       basicDetails:
 *                         type: object
 *                         properties:
 *                           religion:
 *                             type: string
 *                             example: Hindu
 *                           caste:
 *                             type: string
 *                             example: Brahmin
 *                           gotra:
 *                             type: string
 *                             example: Bharadwaj
 *                           marital_status:
 *                             type: string
 *                             example: Single
 *                       locationDetails:
 *                         type: object
 *                         properties:
 *                           city:
 *                             type: string
 *                             example: Mumbai
 *                           country_living_in:
 *                             type: string
 *                             example: India
 *                       educationCareer:
 *                         type: object
 *                         properties:
 *                           education:
 *                             type: string
 *                             example: B.Tech
 *                           profession:
 *                             type: string
 *                             example: Engineer
 *                       lifestyle:
 *                         type: object
 *                         properties:
 *                           age:
 *                             type: integer
 *                             example: 28
 *                           height_cm:
 *                             type: integer
 *                             example: 170
 *                       familyDetails:
 *                         type: object
 *                         properties:
 *                           family_type:
 *                             type: string
 *                             example: Nuclear
 *                           father_occupation:
 *                             type: string
 *                             example: Businessman
 *                           mother_occupation:
 *                             type: string
 *                             example: Homemaker
 *                       hobbies:
 *                         type: object
 *                         properties:
 *                           hobbies:
 *                             type: string
 *                             example: Reading
 *                           interests:
 *                             type: string
 *                             example: Travelling
 *       500:
 *         description: Server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Unable to retrieve user profiles due to a server error.
 */

/**
 * @swagger
 * /user_profile:
 *   post:
 *     tags:
 *       - User Profile
 *     summary: Create or update user profile details
 *     description: Create or update profile details including basic info, education, lifestyle, astro details, etc., for a specific user.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               basicDetails:
 *                 type: object
 *                 properties:
 *                   religion:
 *                     type: string
 *                     example: Hindu
 *                   caste:
 *                     type: string
 *                     example: Brahmin
 *                   gotra:
 *                     type: string
 *                     example: Vashishtha
 *                   marital_status:
 *                     type: string
 *                     example: Never Married
 *               locationDetails:
 *                 type: object
 *                 properties:
 *                   city:
 *                     type: string
 *                     example: Mumbai
 *                   country_living_in:
 *                     type: string
 *                     example: India
 *               educationCareer:
 *                 type: object
 *                 properties:
 *                   education:
 *                     type: string
 *                     example: MBA
 *                   profession:
 *                     type: string
 *                     example: Software Engineer
 *               lifestyle:
 *                 type: object
 *                 properties:
 *                   age:
 *                     type: integer
 *                     example: 29
 *                   height_cm:
 *                     type: integer
 *                     example: 170
 *                   body_type:
 *                     type: string
 *                     example: Athletic
 *                   diet:
 *                     type: string
 *                     example: Vegetarian
 *               astroDetails:
 *                 type: object
 *                 properties:
 *                   city:
 *                     type: string
 *                     example: Pune
 *                   country_of_birth:
 *                     type: string
 *                     example: India
 *               familyDetails:
 *                 type: object
 *                 properties:
 *                   family_type:
 *                     type: string
 *                     example: Nuclear
 *                   father_occupation:
 *                     type: string
 *                     example: Businessman
 *                   mother_occupation:
 *                     type: string
 *                     example: Homemaker
 *               hobbies:
 *                 type: object
 *                 properties:
 *                   hobbies:
 *                     type: array
 *                     items:
 *                       type: string
 *                     example: ["Reading", "Traveling"]
 *                   interests:
 *                     type: array
 *                     items:
 *                       type: string
 *                     example: ["Technology", "Fitness"]
 *               verfication:
 *                 type: object
 *                 properties:
 *                   aadhar_verified:
 *                     type: boolean
 *                     example: true
 *                   email_verified:
 *                     type: boolean
 *                     example: true
 *     responses:
 *       200:
 *         description: User profile details created or updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User profile updated successfully.
 *                 data:
 *       400:
 *         description: Bad request due to invalid input or processing error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Failed to update user profile due to invalid input.
 */

/**
 * @swagger
 * /user_profile/{userId}:
 *   get:
 *     tags:
 *       - User Profile
 *     summary: Get user profile by ID
 *     description: Retrieve all profile details (basic info, location, education, lifestyle, astro, etc.) for the given user ID.
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the user
 *         example: 101
 *     responses:
 *       200:
 *         description: Successfully retrieved user profile.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User profile fetched successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     firstName:
 *                       type: string
 *                       example: Ravi
 *                     lastName:
 *                       type: string
 *                       example: Sharma
 *                     email:
 *                       type: string
 *                       example: <EMAIL>
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-01T10:00:00Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-05T10:00:00Z
 *                     basicDetails:
 *                       type: object
 *                       properties:
 *                         religion:
 *                           type: string
 *                           example: Hindu
 *                         caste:
 *                           type: string
 *                           example: Brahmin
 *                         gotra:
 *                           type: string
 *                           example: Bharadwaj
 *                         marital_status:
 *                           type: string
 *                           example: Single
 *                     locationDetails:
 *                       type: object
 *                       properties:
 *                         city:
 *                           type: string
 *                           example: Mumbai
 *                         country_living_in:
 *                           type: string
 *                           example: India
 *                     educationCareer:
 *                       type: object
 *                       properties:
 *                         education:
 *                           type: string
 *                           example: B.Tech
 *                         profession:
 *                           type: string
 *                           example: Engineer
 *                     lifestyle:
 *                       type: object
 *                       properties:
 *                         age:
 *                           type: integer
 *                           example: 28
 *                         height_cm:
 *                           type: integer
 *                           example: 170
 *                     familyDetails:
 *                       type: object
 *                       properties:
 *                         family_type:
 *                           type: string
 *                           example: Nuclear
 *                         father_occupation:
 *                           type: string
 *                           example: Businessman
 *                         mother_occupation:
 *                           type: string
 *                           example: Homemaker
 *                     hobbies:
 *                       type: object
 *                       properties:
 *                         hobbies:
 *                           type: string
 *                           example: Reading
 *                         interests:
 *                           type: string
 *                           example: Travelling
 *       404:
 *         description: User profile not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: User not found.
 *       500:
 *         description: Internal server error while fetching profile.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Internal server error.
 */
