import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";
import SubscriptionService from "./subscription.service";

export default class SubscriptionController {
    static subscriptionService = SubscriptionService;
    constructor() { }

    // Purchase subscription
    static purchaseSubscription = catchAsync(async (request: Request, response: Response) => {
        try {
            const { plan_id, payment_id } = request.body;
            const user_id = request.decoded;

            const result = await this.subscriptionService.purchaseSubscription({
                user_id,
                plan_id,
                payment_id
            });

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: result.message,
                data: result,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Track usage
    static trackUsage = catchAsync(async (request: Request, response: Response) => {
        try {
            const { action_type, target_user_id } = request.body;
            const user_id = request.decoded.id;

            const result = await this.subscriptionService.trackUsage({
                user_id,
                action_type,
                target_user_id
            });

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: result.message,
                data: result,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Get subscription status
    static getSubscriptionStatus = catchAsync(async (request: Request, response: Response) => {
        try {
            const user_id = request.decoded.id;

            const status = await this.subscriptionService.getActiveSubscription(user_id);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: status.message,
                data: status,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Check if user can perform action
    static canPerformAction = catchAsync(async (request: Request, response: Response) => {
        try {
            const { action_type, target_user_id } = request.query;
            const user_id = request.decoded.id;

            const result = await this.subscriptionService.canPerformAction(
                user_id,
                action_type as string,
                target_user_id ? Number(target_user_id) : undefined
            );
            console.log('result: ', result);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: result.message,
                data: result,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Get subscription history
    static getSubscriptionHistory = catchAsync(async (request: Request, response: Response) => {
        try {
            const user_id = request.decoded.id;

            const history = await this.subscriptionService.getUserSubscriptionHistory(user_id);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Subscription history retrieved successfully",
                data: { subscriptions: history },
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Admin: Check expired subscriptions
    static checkExpiredSubscriptions = catchAsync(async (request: Request, response: Response) => {
        try {
            const result = await this.subscriptionService.checkAndUpdateExpiredSubscriptions();

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: `Updated ${result.updatedCount} expired subscriptions`,
                data: result,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Get user subscription details by ID
    static getSubscriptionById = catchAsync(async (request: Request, response: Response) => {
        try {
            const { subscription_id } = request.params;
            const user_id = request.decoded.id;

            // This would need to be implemented in the service
            // For now, just return the active subscription
            const status = await this.subscriptionService.getActiveSubscription(user_id);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Subscription details retrieved successfully",
                data: status,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Cancel subscription
    static cancelSubscription = catchAsync(async (request: Request, response: Response) => {
        try {
            const user_id = request.decoded.id;
            const { reason } = request.body;

            // This would need to be implemented in the service
            // For now, just deactivate the subscription
            const status = await this.subscriptionService.getActiveSubscription(user_id);
            
            if (!status.isActive || !status.subscription) {
                return sentResponse(response, {
                    statusCode: httpStatus.BAD_REQUEST,
                    message: "No active subscription to cancel",
                    data: null,
                });
            }

            await status.subscription.update({
                is_active: false,
                revoked_at: new Date()
            });

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Subscription cancelled successfully",
                data: { cancelled: true, reason },
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });
}
