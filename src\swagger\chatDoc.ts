/**
 * @swagger
 * components:
 *   schemas:
 *     Chat:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Unique identifier for the chat
 *           example: 1
 *         user1_id:
 *           type: integer
 *           description: ID of the first user in the chat
 *           example: 101
 *         user2_id:
 *           type: integer
 *           description: ID of the second user in the chat
 *           example: 102
 *         is_active:
 *           type: boolean
 *           description: Whether the chat is active or deleted
 *           example: true
 *         last_message_at:
 *           type: string
 *           format: date-time
 *           description: Timestamp of the last message in the chat
 *           example: "2023-06-15T14:30:00.000Z"
 *         auto_delete_days:
 *           type: integer
 *           nullable: true
 *           description: Number of days after which messages are automatically deleted
 *           example: 30
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the chat was created
 *           example: "2023-06-01T10:00:00.000Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the chat was last updated
 *           example: "2023-06-15T14:30:00.000Z"
 *         user1:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *               example: 101
 *             first_name:
 *               type: string
 *               example: "<PERSON>"
 *             last_name:
 *               type: string
 *               example: "Do<PERSON>"
 *             email:
 *               type: string
 *               example: "<EMAIL>"
 *         user2:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *               example: 102
 *             first_name:
 *               type: string
 *               example: "Jane"
 *             last_name:
 *               type: string
 *               example: "Smith"
 *             email:
 *               type: string
 *               example: "<EMAIL>"
 */

/**
 * @swagger
 * /chats:
 *   post:
 *     tags:
 *       - Chat
 *     summary: Create a new chat
 *     description: Create a new chat between the authenticated user and another user.
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user2Id
 *             properties:
 *               user2Id:
 *                 type: integer
 *                 description: ID of the user to chat with
 *                 example: 102
 *     responses:
 *       201:
 *         description: Chat created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: "Chat created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Chat'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User does not have permission
 *       500:
 *         description: Internal server error
 * 
 *   get:
 *     tags:
 *       - Chat
 *     summary: Get all chats for the authenticated user
 *     description: Retrieve a list of all chats for the authenticated user.
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Chats retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Chats retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Chat'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /chats/{id}:
 *   get:
 *     tags:
 *       - Chat
 *     summary: Get chat by ID
 *     description: Retrieve a specific chat by its ID.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Chat ID
 *         example: 1
 *     responses:
 *       200:
 *         description: Chat retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Chat retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Chat'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User does not have permission to access this chat
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 * 
 *   delete:
 *     tags:
 *       - Chat
 *     summary: Delete a chat
 *     description: Soft delete a chat by setting is_active to false.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Chat ID
 *         example: 1
 *     responses:
 *       200:
 *         description: Chat deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Chat deleted successfully"
 *                 data:
 *                   type: null
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User does not have permission to delete this chat
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /chats/{id}/auto-delete:
 *   put:
 *     tags:
 *       - Chat
 *     summary: Set auto-delete days for a chat
 *     description: Set the number of days after which messages in the chat will be automatically deleted.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Chat ID
 *         example: 1
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - days
 *             properties:
 *               days:
 *                 type: integer
 *                 description: Number of days after which messages will be auto-deleted
 *                 example: 30
 *     responses:
 *       200:
 *         description: Auto-delete days set successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Auto-delete days set successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Chat'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User does not have permission to modify this chat
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /chats/{id}/read-all:
 *   put:
 *     tags:
 *       - Chat
 *     summary: Mark all messages in a chat as read
 *     description: Mark all unread messages in a chat as read for the authenticated user.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Chat ID
 *         example: 1
 *     responses:
 *       200:
 *         description: Messages marked as read successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "5 messages marked as read"
 *                 data:
 *                   type: object
 *                   properties:
 *                     count:
 *                       type: integer
 *                       example: 5
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User does not have permission to access this chat
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 */
