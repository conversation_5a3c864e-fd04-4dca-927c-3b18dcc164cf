import Joi from "joi";

const sendMessage = {
  body: Joi.object().keys({
    chatId: Joi.number().required(),
    receiverId: Joi.number().required(),
    content: Joi.string().required().max(5000),
  }),
};

const markAsDelivered = {
  params: Joi.object().keys({
    id: Joi.number().required(),
  }),
};

const markAsRead = {
  params: Joi.object().keys({
    id: Joi.number().required(),
  }),
};

const markAllAsRead = {
  params: Joi.object().keys({
    id: Joi.number().required(),
  }),
};

export default {
  sendMessage,
  markAsDelivered,
  markAsRead,
  markAllAsRead,
};
