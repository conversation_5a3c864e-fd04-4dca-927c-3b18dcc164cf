"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.moduleValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.moduleValidation = {
    body: joi_1.default.object().keys({
        name: joi_1.default.string().trim().required().messages({
            "string.empty": "Name is required.",
            "string.base": "Name must be a string.",
        }),
        route: joi_1.default.string().trim().required().messages({
            "string.empty": "Route is required.",
            "string.base": "Route must be a string.",
        }),
        parent_id: joi_1.default.number().optional().allow(null, ""),
        icon: joi_1.default.string().trim().optional().allow(null, ""),
        sort_order: joi_1.default.number().optional().allow(null, ""),
        is_active: joi_1.default.boolean().optional().allow(null, ""),
    }),
};
