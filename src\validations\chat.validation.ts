import Joi from "joi";

const createChat = {
  body: Joi.object().keys({
    user2Id: Joi.number().required(),
  }),
};

const getChatById = {
  params: Joi.object().keys({
    id: Joi.number().required(),
  }),
};

const getChatMessages = {
  params: Joi.object().keys({
    id: Joi.number().required(),
  }),
  query: Joi.object().keys({
    page: Joi.number().integer().min(1),
    limit: Joi.number().integer().min(1).max(100),
  }),
};

const setAutoDeleteDays = {
  params: Joi.object().keys({
    id: Joi.number().required(),
  }),
  body: Joi.object().keys({
    days: Joi.number().integer().min(1).required(),
  }),
};

const deleteChat = {
  params: Joi.object().keys({
    id: Joi.number().required(),
  }),
};

export default {
  createChat,
  getChatById,
  getChatMessages,
  setAutoDeleteDays,
  deleteChat,
};
