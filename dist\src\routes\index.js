"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const router = express_1.default.Router();
const user_route_1 = __importDefault(require("./user.route"));
const auth_route_1 = __importDefault(require("./auth.route"));
const admin_route_1 = __importDefault(require("./admin.route"));
const user_profile_route_1 = __importDefault(require("./user_profile.route"));
const user_shortlist_route_1 = __importDefault(require("./user_shortlist.route"));
const geolocation_route_1 = __importDefault(require("./geolocation.route"));
const user_preferences_route_1 = __importDefault(require("./user_preferences.route"));
const user_gallery_route_1 = __importDefault(require("./user_gallery.route"));
const subscription_plans_route_1 = __importDefault(require("./subscription_plans.route"));
const user_subscription_route_1 = __importDefault(require("./user_subscription.route"));
const privacy_setting_route_1 = __importDefault(require("./privacy_setting.route"));
const user_invitation_route_1 = __importDefault(require("./user_invitation.route"));
const chat_route_1 = __importDefault(require("./chat.route"));
const message_route_1 = __importDefault(require("./message.route"));
const user_verifications_route_1 = __importDefault(require("./user_verifications.route"));
const success_story_route_1 = __importDefault(require("./success_story.route"));
const role_route_1 = __importDefault(require("./role.route"));
const paypal_route_1 = __importDefault(require("./paypal.route"));
const subscription_route_1 = __importDefault(require("./subscription.route"));
const khalti_route_1 = __importDefault(require("./khalti.route"));
const help_route_1 = __importDefault(require("./help.route"));
const inquiries_route_1 = __importDefault(require("./inquiries.route"));
const dashboard_route_1 = __importDefault(require("./dashboard.route"));
const user_nearby_route_1 = __importDefault(require("./user_nearby.route"));
const module_route_1 = __importDefault(require("./module.route"));
const defaultRoutes = [
    {
        path: "/roles",
        route: role_route_1.default,
    },
    {
        path: "/admin",
        route: admin_route_1.default,
    },
    {
        path: "/modules",
        route: module_route_1.default,
    },
    {
        path: "/auth",
        route: auth_route_1.default,
    },
    {
        path: "/user_verifications",
        route: user_verifications_route_1.default,
    },
    {
        path: "/user",
        route: user_route_1.default,
    },
    {
        path: "/geolocation",
        route: geolocation_route_1.default,
    },
    {
        path: "/privacy_setting",
        route: privacy_setting_route_1.default,
    },
    {
        path: "/user_profile",
        route: user_profile_route_1.default,
    },
    {
        path: "/user_shortlist",
        route: user_shortlist_route_1.default,
    },
    {
        path: "/user_preference",
        route: user_preferences_route_1.default,
    },
    {
        path: "/user_gallery",
        route: user_gallery_route_1.default,
    },
    {
        path: "/subscription_plan",
        route: subscription_plans_route_1.default,
    },
    {
        path: "/user_subscription",
        route: user_subscription_route_1.default,
    },
    {
        path: "/paypal",
        route: paypal_route_1.default,
    },
    {
        path: "/user_invitation",
        route: user_invitation_route_1.default,
    },
    {
        path: "/chats",
        route: chat_route_1.default,
    },
    {
        path: "/messages",
        route: message_route_1.default,
    },
    {
        path: "/success_story",
        route: success_story_route_1.default,
    },
    {
        path: "/help",
        route: help_route_1.default,
    },
    {
        path: "/inquiries",
        route: inquiries_route_1.default,
    },
    {
        path: "/dashboard",
        route: dashboard_route_1.default,
    },
    {
        path: "/user_nearby",
        route: user_nearby_route_1.default,
    },
    {
        path: "/subscription",
        route: subscription_route_1.default,
    },
    {
        path: "/khalti",
        route: khalti_route_1.default,
    },
];
defaultRoutes.forEach((route) => {
    router.use(route.path, route.route);
});
exports.default = router;
