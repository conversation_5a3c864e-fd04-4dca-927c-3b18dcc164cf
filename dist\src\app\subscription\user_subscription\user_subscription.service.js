"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const sequelize_1 = require("sequelize");
const user_subscriptions_model_1 = __importDefault(require("../../../database/models/user_subscriptions.model"));
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../../config/httpMessages"));
const subscription_plans_mode_1 = __importDefault(require("../../../database/models/subscription_plans.mode"));
const user_model_1 = __importDefault(require("../../../database/models/user.model"));
class UserSubscriptionService {
    constructor() { }
}
_a = UserSubscriptionService;
/**
 * Create a UserSubscription
 * @param {Object} body
 * @returns {Promise<UserSubscription>}
 */
UserSubscriptionService.createUserSubscription = (body) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const details = yield user_subscriptions_model_1.default.create(body);
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Return UserSubscriptions
 * @param {Object} options
 * @param {number} [options.page] - Current page number (optional)
 * @param {number} [options.limit] - Number of items per page (optional)
 * @param {string} [options.search] - Search term for filtering (optional)
 * @returns {Promise<UserSubscription[]>}
 */
UserSubscriptionService.getUserSubscriptions = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, is_active } = options;
        const whereCondition = {};
        if (is_active !== undefined) {
            whereCondition.is_active = is_active;
        }
        const queryOption = {
            where: whereCondition,
            order: [["createdAt", "DESC"]],
            include: [
                {
                    model: subscription_plans_mode_1.default,
                    as: "plan",
                },
                {
                    model: user_model_1.default,
                    as: "user",
                }
            ],
        };
        if (search) {
            const escapedSearch = `%${search.toLowerCase()}%`;
            queryOption.where = Object.assign(Object.assign({}, whereCondition), { [sequelize_1.Op.or]: [
                    sequelize_1.Sequelize.literal(`LOWER(\`user\`.\`first_name\`) LIKE '${escapedSearch}'`),
                    sequelize_1.Sequelize.literal(`LOWER(\`user\`.\`last_name\`) LIKE '${escapedSearch}'`),
                    sequelize_1.Sequelize.literal(`LOWER(\`plan\`.\`name\`) LIKE '${escapedSearch}'`),
                ] });
        }
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const data = yield user_subscriptions_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: data.count,
                totalPages: Math.ceil(data.count / limit),
                currentPage: page,
                user_subscriptions: data.rows,
            };
        }
        else {
            return data.rows;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get UserSubscription by id
 * @param {Number} id
 * @returns {Promise<UserSubscription>}
 */
UserSubscriptionService.getUserSubscriptionById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return user_subscriptions_model_1.default.findOne({
            where: { id },
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update role by id
 * @param {Number} Id
 * @param {Object} updateBody
 * @returns {Promise<Role>}
 */
UserSubscriptionService.updateUserSubscriptionById = (Id, updateBody) => __awaiter(void 0, void 0, void 0, function* () {
    const details = yield user_subscriptions_model_1.default.findByPk(Id);
    if (!details) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER_SUBSCRIPTION.NOT_FOUND);
    }
    Object.assign(details, updateBody);
    yield details.save();
    return details;
});
/**
 * Delete role by id
 * @param {Number} Id
 * @returns {Promise<Role>}
 */
UserSubscriptionService.deleteUserSubscriptionById = (Id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const details = yield user_subscriptions_model_1.default.findByPk(Id);
        if (!details) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.ROLES.NOT_FOUND);
        }
        yield details.destroy();
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting Role.");
    }
});
exports.default = UserSubscriptionService;
