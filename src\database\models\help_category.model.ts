import {
    Table,
    Column,
    Model,
    <PERSON>Type,
    PrimaryKey,
    AutoIncrement,
    AllowNull,
    HasMany,
    CreatedAt,
    UpdatedAt
} from "sequelize-typescript";
import HelpQuestion from "./help_question.model";

export interface HelpCategoryI {
    id: number;
    title: string;
    icon: string;
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: "help_categories",
    timestamps: true,
})
class HelpCategory extends Model<HelpCategoryI> implements HelpCategoryI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @AllowNull(false)
    @Column(DataType.STRING(255))
    title: string;

    @AllowNull(true)
    @Column(DataType.STRING(255))
    icon: string;

    @HasMany(() => HelpQuestion, { foreignKey: "category_id", onDelete: "CASCADE" })
    questions: HelpQuestion[];

    @CreatedAt
    createdAt?: Date;

    @UpdatedAt
    updatedAt?: Date;
}

export default HelpCategory;