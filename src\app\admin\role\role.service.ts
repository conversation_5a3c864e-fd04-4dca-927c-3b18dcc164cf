import httpStatus from "http-status";
import bcrypt from "bcrypt";
import { Op } from "sequelize";
import ApiError from "../../../utils/ApiError";
import httpMessages from "../../../config/httpMessages";
import Role from "../../../database/models/role.model";
import Module from "../../../database/models/modules.model";
import RolePermission from "../../../database/models/role_permissions.model";

export default class RoleService {
  constructor() {}

  static isPasswordMatch = async (user: any, password: string) =>
    await bcrypt.compare(password, user.password);

  /**
   * Get role by rolename
   * @param {string} role_name
   * @param {any} options
   * @returns {Promise<Role>}
   */
  static getRoleByRoleName = async (role_name: string) => {
    return Role.findOne({
      where: { role_name },
    });
  };

  /**
   * Create a Role
   * @param {Object} roleBody
   * @returns {Promise<Role>}
   */
  static createRole = async (roleBody: any) => {
    try {
      if (await this.getRoleByRoleName(roleBody.role_name)) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.ROLES.NAME_ALREADY_TAKEN
        );
      }

      const role: Role = await Role.create(roleBody);
      const modules: any = await Module.findAll();
      const rolePermissionsData: any = modules.map((module: any) => {
        return {
          role_id: role.id,
          module_id: module.id,
        };
      });
      await RolePermission.bulkCreate(rolePermissionsData);

      return await this.getRoleById(role.id);
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Return Roles
   * @param {Object} options
   * @param {number} [options.page] - Current page number (optional)
   * @param {number} [options.limit] - Number of items per page (optional)
   * @param {string} [options.search] - Search term for filtering (optional)
   * @returns {Promise<Role[]>}
   */
  static getRoles = async (options: {
    page?: number;
    limit?: number;
    search?: string;
  }) => {
    try {
      const { page, limit, search } = options;
      const whereCondition = search
        ? {
            [Op.or]: [
              { role_name: { [Op.like]: `%${search.toLowerCase()}%` } },
            ],
          }
        : {};

      const queryOption: any = {
        where: whereCondition,
        order: [["createdAt", "DESC"]],
      };
      // If pagination is provided, apply pagination
      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }
      const roles = await Role.findAndCountAll(queryOption);
      if (page && limit) {
        return {
          totalItems: roles.count,
          totalPages: Math.ceil(roles.count / limit),
          currentPage: page,
          roles: roles.rows,
        };
      } else {
        return roles.rows;
      }
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get role by id
   * @param {Number} id
   * @returns {Promise<Role>}
   */
  static getRoleById = async (id: number) => {
    try {
      return Role.findOne({
        where: { id },
      }).then((data: any) => data?.toJSON());
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Update role by id
   * @param {Number} roleId
   * @param {Object} updateBody
   * @returns {Promise<Role>}
   */
  static updateRoleById = async (roleId: number, updateBody: any) => {
    const role = await Role.findByPk(roleId);
    if (!role) {
      throw new ApiError(httpStatus.NOT_FOUND, httpMessages.ROLES.NOT_FOUND);
    }

    Object.assign(role, updateBody);
    await role.save();
    return role;
  };

  /**
   * Delete role by id
   * @param {Number} roleId
   * @returns {Promise<Role>}
   */
  static deleteUserById = async (roleId: number) => {
    try {
      const role: any = await Role.findByPk(roleId);
      if (!role) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.ROLES.NOT_FOUND);
      }
      await role.destroy();
      return role;
    } catch (error: any) {
      if (error.name === "SequelizeForeignKeyConstraintError") {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          "Cannot delete this Record as it is referenced in another table."
        );
      } else {
        throw new ApiError(
          error.status || httpStatus.BAD_REQUEST,
          error.message || "Error deleting Role."
        );
      }
    }
  };
}
