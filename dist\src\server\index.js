"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const express_fileupload_1 = __importDefault(require("express-fileupload"));
const path_1 = __importDefault(require("path"));
const http_1 = __importDefault(require("http"));
const routes_1 = __importDefault(require("../routes"));
const swagger_1 = require("../swagger");
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const socket_1 = require("../socket");
const scheduler_1 = require("../config/scheduler");
const app = (0, express_1.default)();
const corsConfig = { origin: "*" };
const server = http_1.default.createServer(app);
// Initialize Socket.io
(0, socket_1.initializeSocket)(server);
// Initialize Cron Jobs
// const cronService = CronJobService.getInstance();
// cronService.initializeJobs();
(0, scheduler_1.initScheduler)();
app.use("/uploads", express_1.default.static(path_1.default.join(__dirname, "../../uploads")));
app.get("/health-check", (req, res) => {
    console.log("A healthy result.");
    res.send("A healthy result.");
});
app.use("/api-docs", swagger_1.swaggerAuth, swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swagger_1.swaggerSpec, swagger_1.swaggerUiOptions));
app.use(express_1.default.json());
app.use((0, cors_1.default)(corsConfig));
app.use((0, express_fileupload_1.default)());
app.use(require("cookie-parser")());
app.use(require("body-parser").urlencoded({ extended: true }));
app.use(require("express-session")({
    secret: "keyboard cat",
    resave: true,
    saveUninitialized: true,
}));
// app.use(encryptResponseMiddleware);
app.use("/", routes_1.default);
exports.default = server;
