"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../../utils/catchAsync"));
const response_1 = __importStar(require("../../../utils/response"));
const user_invitation_service_1 = __importDefault(require("./user_invitation.service"));
const user_service_1 = __importDefault(require("../user.service"));
const subscription_service_1 = __importDefault(require("../../subscription/subscription.service"));
class UserInvitationController {
    constructor() { }
}
_a = UserInvitationController;
UserInvitationController.userInvitationService = user_invitation_service_1.default;
UserInvitationController.userService = user_service_1.default;
UserInvitationController.create = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const invitationData = Object.assign({}, request.body);
        invitationData.sender_id = request.decoded; // Current user ID
        const invitation = yield _a.userInvitationService.createInvitation(invitationData);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.CREATED,
            message: "Invitation sent successfully",
            data: invitation,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserInvitationController.getInvitations = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const status = request.query.status;
        const invitations = yield _a.userInvitationService.getUserInvitations(userId, status);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Invitations retrieved successfully",
            data: invitations,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserInvitationController.sentInvitations = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const status = "sent";
        console.log("status: ", status);
        const invitations = yield _a.userInvitationService.getUserInvitations(userId, status);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Sent invitations retrieved successfully",
            data: invitations,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserInvitationController.acceptedInvitations = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const status = "accepted";
        const invitations = yield _a.userInvitationService.getUserInvitations(userId, status);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Accepted invitations retrieved successfully",
            data: invitations,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserInvitationController.declinedInvitations = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const status = "declined";
        const invitations = yield _a.userInvitationService.getUserInvitations(userId, status);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Declined invitations retrieved successfully",
            data: invitations,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserInvitationController.updateStatus = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = request.params;
        const { status } = request.body;
        const userId = request.decoded;
        if (!["accepted", "declined"].includes(status)) {
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.BAD_REQUEST,
                message: "Invalid status. Must be 'accepted' or 'declined'",
                data: null,
            });
        }
        const invitation = yield _a.userInvitationService.updateInvitationStatus(parseInt(id), status, userId, "receiver");
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: `Invitation ${status} successfully`,
            data: invitation,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserInvitationController.getUnreadCount = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const count = yield _a.userInvitationService.getUnreadCount(userId);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Unread count retrieved successfully",
            data: { count },
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserInvitationController.markAsRead = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = request.params;
        const userId = request.decoded;
        const invitation = yield _a.userInvitationService.markAsRead(parseInt(id), userId);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Invitation marked as read",
            data: invitation,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Get user profile details for invitation view
 */
UserInvitationController.getUserProfileForInvitation = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId } = request.params;
        const currentUserId = request.decoded;
        // Check if there's an existing invitation between these users
        const invitationExists = yield _a.userInvitationService.checkInvitationExists(currentUserId, parseInt(userId));
        // Get user profile details
        const userProfile = yield _a.userService.getUserById(parseInt(userId));
        if (!userProfile) {
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.NOT_FOUND,
                message: "User profile not found",
                data: null,
            });
        }
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "User profile retrieved successfully",
            data: {
                profile: userProfile,
                invitation_status: invitationExists
                    ? invitationExists.status
                    : null,
                can_send_invitation: !invitationExists || invitationExists.status === "declined",
            },
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Request contact number
 */
UserInvitationController.requestContactNumber = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = request.params; // invitation id
        const userId = request.decoded;
        // Check if the invitation exists and is accepted
        const invitation = yield _a.userInvitationService.getInvitationById(parseInt(id));
        yield subscription_service_1.default.trackUsage({
            user_id: userId,
            action_type: "contact_viewed",
            target_user_id: parseInt(id),
        });
        if (!invitation) {
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.NOT_FOUND,
                message: "Invitation not found",
                data: null,
            });
        }
        if (invitation.status !== "accepted") {
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.BAD_REQUEST,
                message: "Cannot request contact number for non-accepted invitation",
                data: null,
            });
        }
        // Check if the current user is part of this invitation
        if (invitation.sender_id !== userId &&
            invitation.receiver_id !== userId) {
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.FORBIDDEN,
                message: "You are not authorized to access this invitation",
                data: null,
            });
        }
        // Get the other user's ID
        const otherUserId = invitation.sender_id === userId
            ? invitation.receiver_id
            : invitation.sender_id;
        // Get the other user's contact details
        const otherUser = yield _a.userService.getUserById(otherUserId);
        if (!otherUser) {
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.NOT_FOUND,
                message: "User not found",
                data: null,
            });
        }
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Contact number retrieved successfully",
            data: {
                phone: otherUser.phone,
                email: otherUser.email,
                phone_code: otherUser.phone_code,
            },
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserInvitationController.decline = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = request.params; // invitation id
        const userId = request.decoded;
        const result = yield _a.userInvitationService.updateInvitationStatus(parseInt(id), "declined", userId, "sender");
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Invitation declined successfully",
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Remove friend (accepted invitation)
 */
UserInvitationController.removeFriend = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = request.params; // invitation id
        const userId = request.decoded;
        const result = yield _a.userInvitationService.removeFriend(parseInt(id), userId);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Friend removed successfully",
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = UserInvitationController;
