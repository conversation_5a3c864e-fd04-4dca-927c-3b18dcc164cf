import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../../utils/response";
import httpMessages from "../../../config/httpMessages";
import ApiError from "../../../utils/ApiError";
import UserGalleryService from "./user_gallery.service";
import UserGallery from "../../../database/models/user_gallery.model";

export default class UserGalleryController {
  static userGalleryService = UserGalleryService;
  constructor() { }

  static getAll = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search } = request.query;
      const decoded = request.decoded;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
        currentUserId: decoded,
      };
      const list = await this.userGalleryService.getUserGalleryImages(option);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: 'User gallery images retrieved successfully',
        data: list,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const body = { ...request.body };
      const userId = request.decoded;
       await this.userGalleryService.createUserGallery(body, userId);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: 'User gallery image created successfully',
        data: null,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static showById = catchAsync(async (request: Request, response: Response) => {
    try {
      const Id: number = parseInt(request.params.id, 10);
      const details = await this.userGalleryService.getUserGalleryById(Id);
      if (!details) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER_GALLERY.NOT_FOUND);
      }
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: 'User gallery image retrieved successfully',
        data: details,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static update = catchAsync(async (request: Request, response: Response) => {
    try {
      const Id: number = parseInt(request.params.id, 10);
      const body = { ...request.body };

      const updatedData = await this.userGalleryService.updateUserGalleryById(Id, body);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: 'User gallery image updated successfully',
        data: updatedData,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static delete = catchAsync(async (request: Request, response: Response) => {
    try {
      const id: number = parseInt(request.params.id, 10);
      await this.userGalleryService.deleteUserGalleryById(id);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: 'User gallery image deleted successfully',
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });
}
