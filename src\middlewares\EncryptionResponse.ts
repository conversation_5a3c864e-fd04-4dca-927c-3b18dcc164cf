import CryptoJS from "crypto-js";
import { NextFunction, Request, Response } from "express";
import errorResponse from "../utils/response";
import ApiError from "../utils/ApiError";
import httpMessages from "../config/httpMessages";

const iv = "v@XI!kaW3BK,@8ki";
const secretKey = "Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn";

function insertSubstring(original: string, substring: string, position: number): string {
  return original.slice(0, position) + substring + original.slice(position);
}
export function encryptData(data: string, key: string, iv: string): string {
  try {
    if (key.length !== 32) {
      throw new Error('Key must be 32 characters for AES-256-CBC');
    }

    if (iv.length !== 16) {
      throw new Error('IV must be 16 characters for AES-CBC');
    }

    const keyWordArray = CryptoJS.enc.Utf8.parse(key);
    const ivWordArray = CryptoJS.enc.Utf8.parse(iv);

    const encrypted = CryptoJS.AES.encrypt(data, keyWordArray, { iv: ivWordArray, mode: CryptoJS.mode.CBC });

    let finalData = encrypted.toString(); // Base64 encoded ciphertext

    const first_fifteen = 'h&EKZdsBUkRaG%3';
    const second_seven = 'xejTbcK';
    const third_eight = 'j#pEcft4';
    const fourth_six = 't^FusM';
    const fifth_seven = 'rD9$b#Y';
    const last_fifteen = 'aez964DZn%$BhF^';

    if (finalData.length > 29) {
      finalData = insertSubstring(finalData, last_fifteen, finalData.length - 3);
      finalData = insertSubstring(finalData, fifth_seven, 28);
      finalData = insertSubstring(finalData, fourth_six, 22);
      finalData = insertSubstring(finalData, third_eight, 14);
      finalData = insertSubstring(finalData, second_seven, 8);
      finalData = insertSubstring(finalData, first_fifteen, 0);
    }

    return finalData;
  } catch (error) {
    throw new Error(`Encryption failed: ${(error as Error).message}`);
  }
}




export const encryptResponseMiddleware = async (
    request: any,
    response: Response,
    next: NextFunction
): Promise<void> => {
    return new Promise<void>((resolve, reject) => {
        (async () => {
            try {
                const originalSend = response.send;

                response.send = function (body: any): any {
                    try {
                        if (typeof body === 'string') {
                            const iv = 'v@XI!kaW3BK,@8ki';  // Replace with your secret key
                            const secretKey = 'Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn';    // Replace with your IV
                            body = encryptData(body, secretKey, iv);
                        }

                        return originalSend.call(this, body);
                    } catch (error) {
                        reject(error);
                    }
                };

                resolve();
            } catch (e) {
                reject(
                    errorResponse(
                        response,
                        new ApiError(
                            httpStatus.UNAUTHORIZED,
                            httpMessages.USER.AUTH.UNAUTHORIZED
                        )
                    )
                );
            }
        })();
    })
        .then(() => next())
        .catch((err) => next(err));
};

