import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../../utils/response";
import UserInvitationService from "./user_invitation.service";
import UserService from "../user.service";
import SubscriptionService from "../../subscription/subscription.service";

export default class UserInvitationController {
  constructor() {}

  static userInvitationService = UserInvitationService;
  static userService = UserService;

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const invitationData = { ...request.body };
      invitationData.sender_id = request.decoded; // Current user ID

      const invitation = await this.userInvitationService.createInvitation(
        invitationData
      );

      return sentResponse(response, {
        statusCode: httpStatus.CREATED,
        message: "Invitation sent successfully",
        data: invitation,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static getInvitations = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const userId = request.decoded;
        const status = request.query.status as string;

        const invitations = await this.userInvitationService.getUserInvitations(
          userId,
          status
        );

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: "Invitations retrieved successfully",
          data: invitations,
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  static sentInvitations = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const userId = request.decoded;
        const status = "sent" as string;
        console.log("status: ", status);

        const invitations = await this.userInvitationService.getUserInvitations(
          userId,
          status
        );

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: "Sent invitations retrieved successfully",
          data: invitations,
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  static acceptedInvitations = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const userId = request.decoded;
        const status = "accepted" as string;

        const invitations = await this.userInvitationService.getUserInvitations(
          userId,
          status
        );

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: "Accepted invitations retrieved successfully",
          data: invitations,
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  static declinedInvitations = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const userId = request.decoded;
        const status = "declined" as string;

        const invitations = await this.userInvitationService.getUserInvitations(
          userId,
          status
        );

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: "Declined invitations retrieved successfully",
          data: invitations,
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  static updateStatus = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const { id } = request.params;
        const { status } = request.body;
        const userId = request.decoded;

        if (!["accepted", "declined"].includes(status)) {
          return sentResponse(response, {
            statusCode: httpStatus.BAD_REQUEST,
            message: "Invalid status. Must be 'accepted' or 'declined'",
            data: null,
          });
        }

        const invitation =
          await this.userInvitationService.updateInvitationStatus(
            parseInt(id),
            status,
            userId,
            "receiver"
          );

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: `Invitation ${status} successfully`,
          data: invitation,
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  static getUnreadCount = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const userId = request.decoded;

        const count = await this.userInvitationService.getUnreadCount(userId);

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: "Unread count retrieved successfully",
          data: { count },
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  static markAsRead = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const { id } = request.params;
        const userId = request.decoded;

        const invitation = await this.userInvitationService.markAsRead(
          parseInt(id),
          userId
        );

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: "Invitation marked as read",
          data: invitation,
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  /**
   * Get user profile details for invitation view
   */
  static getUserProfileForInvitation = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const { userId } = request.params;
        const currentUserId = request.decoded;

        // Check if there's an existing invitation between these users
        const invitationExists =
          await this.userInvitationService.checkInvitationExists(
            currentUserId,
            parseInt(userId)
          );

        // Get user profile details
        const userProfile = await this.userService.getUserById(
          parseInt(userId)
        );

        if (!userProfile) {
          return sentResponse(response, {
            statusCode: httpStatus.NOT_FOUND,
            message: "User profile not found",
            data: null,
          });
        }

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: "User profile retrieved successfully",
          data: {
            profile: userProfile,
            invitation_status: invitationExists
              ? invitationExists.status
              : null,
            can_send_invitation:
              !invitationExists || invitationExists.status === "declined",
          },
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  /**
   * Request contact number
   */
  static requestContactNumber = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const { id } = request.params; // invitation id
        const userId = request.decoded;

        // Check if the invitation exists and is accepted
        const invitation = await this.userInvitationService.getInvitationById(
          parseInt(id)
        );
        await SubscriptionService.trackUsage({
          user_id: userId,
          action_type: "contact_viewed",
          target_user_id: parseInt(id),
        });
        if (!invitation) {
          return sentResponse(response, {
            statusCode: httpStatus.NOT_FOUND,
            message: "Invitation not found",
            data: null,
          });
        }

        if (invitation.status !== "accepted") {
          return sentResponse(response, {
            statusCode: httpStatus.BAD_REQUEST,
            message:
              "Cannot request contact number for non-accepted invitation",
            data: null,
          });
        }

        // Check if the current user is part of this invitation
        if (
          invitation.sender_id !== userId &&
          invitation.receiver_id !== userId
        ) {
          return sentResponse(response, {
            statusCode: httpStatus.FORBIDDEN,
            message: "You are not authorized to access this invitation",
            data: null,
          });
        }

        // Get the other user's ID
        const otherUserId =
          invitation.sender_id === userId
            ? invitation.receiver_id
            : invitation.sender_id;

        // Get the other user's contact details
        const otherUser = await this.userService.getUserById(otherUserId);

        if (!otherUser) {
          return sentResponse(response, {
            statusCode: httpStatus.NOT_FOUND,
            message: "User not found",
            data: null,
          });
        }

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: "Contact number retrieved successfully",
          data: {
            phone: otherUser.phone,
            email: otherUser.email,
            phone_code: otherUser.phone_code,
          },
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  static decline = catchAsync(async (request: Request, response: Response) => {
    try {
      const { id } = request.params; // invitation id
      const userId = request.decoded;

      const result = await this.userInvitationService.updateInvitationStatus(
        parseInt(id),
        "declined",
        userId,
        "sender"
      );

      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Invitation declined successfully",
        data: result,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  /**
   * Remove friend (accepted invitation)
   */
  static removeFriend = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const { id } = request.params; // invitation id
        const userId = request.decoded;

        const result = await this.userInvitationService.removeFriend(
          parseInt(id),
          userId
        );

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: "Friend removed successfully",
          data: result,
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );
}
