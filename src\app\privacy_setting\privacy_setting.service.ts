import httpStatus from "http-status";
import { Op } from "sequelize";
import PrivacySetting from "../../database/models/privacy_settings.model";
import ApiError from "../../utils/ApiError";
import httpMessages from "../../config/httpMessages";
import UserService from "../user/user.service";
import UserVerification from "../../database/models/user_verifications.model";
import axios from "axios";

export default class PrivacySettingService {
    static userService = UserService;
    constructor() { }

    /**
     * Create a PrivacySetting
     * @param {Object} body
     * @returns {Promise<PrivacySetting>}
     */
    static createPrivacySetting = async (body: any) => {
        try {
            const existingSetting = await PrivacySetting.findOne({
                where: { user_id: body.user_id },
            });
            if (existingSetting) {
                await existingSetting.update(body);
                return existingSetting;
            }
            const details: PrivacySetting = await PrivacySetting.create(body);
            return details;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Return PrivacySettings
     * @param {Object} options
     * @param {number} [options.page] - Current page number (optional)
     * @param {number} [options.limit] - Number of items per page (optional)
     * @param {string} [options.search] - Search term for filtering (optional)
     * @returns {Promise<PrivacySetting[]>}
     */
    static getPrivacySettings = async (options: {
        page?: number;
        limit?: number;
        search?: string;
        userId?: number;
    }) => {
        try {
            const { page, limit, search } = options;
            const whereCondition: any = search
                ? {
                    [Op.or]: [
                        { role_name: { [Op.like]: `%${search.toLowerCase()}%` } },
                    ],
                }
                : {};

            if (options.userId) {
                whereCondition.user_id = options.userId;
            }

            const queryOption: any = {
                where: whereCondition,
                order: [["createdAt", "DESC"]],
            };
            // If pagination is provided, apply pagination
            if (page && limit) {
                const offset = (page - 1) * limit;
                queryOption.limit = limit;
                queryOption.offset = offset;
            }
            const data = await PrivacySetting.findAndCountAll(queryOption);
            if (page && limit) {
                return {
                    totalItems: data.count,
                    totalPages: Math.ceil(data.count / limit),
                    currentPage: page,
                    subscription_plans: data.rows,
                };
            } else {
                return data.rows[0];
            }
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Get PrivacySetting by id
     * @param {Number} id
     * @returns {Promise<PrivacySetting>}
     */
    static getPrivacySettingById = async (id: number) => {
        try {
            return PrivacySetting.findOne({
                where: { id },
            }).then((data: any) => data?.toJSON());
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Update role by id
     * @param {Number} Id
     * @param {Object} updateBody
     * @returns {Promise<Role>}
     */
    static updatePrivacySettingById = async (Id: number, updateBody: any) => {
        const details = await PrivacySetting.findByPk(Id);
        if (!details) {
            throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER_SUBSCRIPTION.NOT_FOUND);
        }

        Object.assign(details, updateBody);
        await details.save();
        return details;
    };

    /**
     * Delete role by id
     * @param {Number} Id
     * @returns {Promise<Role>}
     */
    static deletePrivacySettingById = async (Id: number) => {
        try {
            const details: any = await PrivacySetting.findByPk(Id);
            if (!details) {
                throw new ApiError(httpStatus.NOT_FOUND, httpMessages.ROLES.NOT_FOUND);
            }
            await details.destroy();
            return details;
        } catch (error: any) {
            throw new ApiError(
                error.status || httpStatus.BAD_REQUEST,
                error.message || "Error deleting Role."
            );
        }
    };

    static hideProfile = async (body: any) => {
        try {
            const existingSetting = await PrivacySetting.findOne({
                where: { user_id: body.user_id },
            });
            if (body.profile_visibility) {
                body['profile_start_date'] = new Date();
                let endDate = new Date();
                endDate.setDate(endDate.getDate() + parseInt(body.profile_visibility));
                console.log('endDate: ', endDate);
                body['profile_end_date'] = endDate;
            }
            if (existingSetting) {
                await existingSetting.update(body);
                this.userService.updateUserById(body.user_id, { is_hide_profile: true });
                return existingSetting;
            }
            const details: PrivacySetting = await PrivacySetting.create(body);
            this.userService.updateUserById(body.user_id, { is_hide_profile: true });
            return details;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    static deleteProfile = async (body: any) => {
        try {
            const existingSetting = await PrivacySetting.findOne({
                where: { user_id: body.user_id },
            });
            if (existingSetting) {
                await existingSetting.update(body);
                this.userService.deleteUserById(body.user_id);
                return existingSetting;
            }
            const details: PrivacySetting = await PrivacySetting.create(body);
            this.userService.deleteUserById(body.user_id);
            return details;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
   * Send phone otp
   * @param {Number} userId
   * @param {String} phone
   * @returns {Promise<UserVerification>}
   */
    static sendPhoneOtp = async (userId: number, phone: string, phone_code: string) => {
        try {
            const checkDublicate = await this.userService.getUserByPhone(phone);
            if (checkDublicate) {
                throw new ApiError(httpStatus.BAD_REQUEST, httpMessages.REGISTER.PHONE_ALREADY_TAKEN);
            }
            const verification = await UserVerification.findOne({
                where: { user_id: userId },
            });
            if (!verification) {
                throw new ApiError(httpStatus.NOT_FOUND, "User not found");
            }
            const otp = Math.floor(100000 + Math.random() * 900000).toString();
            const otpExpiry = new Date(Date.now() + 5 * 60 * 1000);
            if (phone_code === "+977") {
                const url = 'https://api.sparrowsms.com/v2/sms/';
                const fullNumber = phone;
                const localNumber = fullNumber.replace("+977", "");
                const response = await axios.post(url, {
                    token: process.env.SPARROW_TOKEN,
                    from: process.env.SPARROW_FROM,
                    to: localNumber,
                    text: `Your verification code is ${otp}. Please enter the code to verify your mobile. Thank you Barbadhu`,
                });
                console.log('response: ', response.status);
            } else {
                const client = require('twilio')(
                    process.env.TWILIO_ACCOUNT_SID,
                    process.env.TWILIO_AUTH_TOKEN
                );

                // Send OTP via Twilio
                const message = await client.messages.create({
                    body: `Your verification code is ${otp}. Please enter the code to verify your mobile. Thank you Barbadhu`,
                    to: phone_code + phone,
                    from: process.env.TWILIO_PHONE_NUMBER,
                });
                console.log('message: ', message);
            }

            await verification.update({ phone_otp: otp, phone_otp_expiry: otpExpiry });
            return verification;
        } catch (error: any) {
            console.log('error: ', error);
            if (error.status === 400) {
                throw new ApiError(httpStatus.BAD_REQUEST, "Invalid phone number");
            } else if(error.status === 403){
                throw new ApiError(httpStatus.FORBIDDEN, "Internal server error");
            } else{
                throw new ApiError(httpStatus.BAD_REQUEST, error.message);
            }
        }
    };

    static verifyPhoneOtp = async (userId: number, phone: string, otp: string, phone_code: string) => {
        try {
            const verification: any = await UserVerification.findOne({
                where: { user_id: userId },
            });
            if (!verification) {
                throw new ApiError(httpStatus.BAD_REQUEST, "Invalid OTP");
            }

            if (verification.phone_otp !== otp) {
                throw new ApiError(httpStatus.BAD_REQUEST, httpMessages.OTP.INVALID_OTP);
            };

            if (new Date() > new Date(verification.phone_otp_expiry)) {
                throw new ApiError(httpStatus.GONE, httpMessages.OTP.INVALID_OTP_EXPIRED);
            };

            await verification.update({ phone: phone,phone_code: phone_code,phone_otp: '', phone_otp_expiry: null, is_phone_verified: true });
            await this.userService.updateUserById(userId, { phone: phone,phone_code: phone_code });
            return verification;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

}
