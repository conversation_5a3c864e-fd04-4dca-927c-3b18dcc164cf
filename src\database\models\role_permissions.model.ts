import {
  AllowNull,
  AutoIncrement,
  BelongsTo,
  Column,
  <PERSON>Type,
  <PERSON><PERSON>ult,
  <PERSON><PERSON>ey,
  Model,
  NotEmpty,
  PrimaryKey,
  Table,
} from "sequelize-typescript";
import Role from "./role.model";
import Module from "./modules.model";

export interface RolePermissionI {
  id: number;
  role_id: number;
  module_id: number;
  menu_show?: boolean;
  list?: boolean;
  add?: boolean;
  edit?: boolean;
  delete?: boolean;
  show?: boolean;
}

@Table({
  tableName: "role_permissions",
  timestamps: true,
})
class RolePermission extends Model<RolePermissionI> implements RolePermissionI {
  @AutoIncrement
  @PrimaryKey
  @Column(DataType.INTEGER)
  id: number;

  @ForeignKey(() => Role)
  @AllowNull(false)
  @Column(DataType.INTEGER)
  role_id: number;

  @BelongsTo(() => Role, { foreignKey: "role_id", onDelete: "CASCADE" })
  role: Role;

  @ForeignKey(() => Module)
  @AllowNull(false)
  @Column(DataType.INTEGER)
  module_id: number;

  @BelongsTo(() => Module, { foreignKey: "module_id", onDelete: "CASCADE" })
  module: Module;

  @AllowNull(false)
  @Default(false)
  @Column(DataType.BOOLEAN)
  menu_show?: boolean;

  @AllowNull(false)
  @Default(false)
  @Column(DataType.BOOLEAN)
  list?: boolean;

  @AllowNull(false)
  @Default(false)
  @Column(DataType.BOOLEAN)
  add?: boolean;

  @AllowNull(false)
  @Default(false)
  @Column(DataType.BOOLEAN)
  edit?: boolean;

  @AllowNull(false)
  @Default(false)
  @Column(DataType.BOOLEAN)
  delete?: boolean;

  @AllowNull(false)
  @Default(false)
  @Column(DataType.BOOLEAN)
  show?: boolean;
}

export default RolePermission;
