import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";
import KhaltiService from "./khalti.service";

export default class KhaltiController {
    static khaltiService = KhaltiService;
    constructor() { }

    // Initiate payment
    static initiatePayment = catchAsync(async (request: Request, response: Response) => {
        try {
            const { 
                plan_id, 
                amount, 
                mobile,
                product_identity,
                product_name,
                product_url,
                return_url,
                website_url
            } = request.body;
            const user_id = request.decoded;

            const result = await this.khaltiService.initiatePayment({
                user_id,
                plan_id,
                amount,
                mobile,
                product_identity,
                product_name,
                product_url,
                return_url,
                website_url
            });

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Payment initiated successfully",
                data: result,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Verify payment
    static verifyPayment = catchAsync(async (request: Request, response: Response) => {
        try {
            const { pidx } = request.body;
            const user_id = request.decoded;

            const result = await this.khaltiService.verifyPayment(pidx, user_id);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Payment verification completed",
                data: result,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Handle webhook
    static handleWebhook = catchAsync(async (request: Request, response: Response) => {
        try {
            const webhookData = request.body;
            const signature = request.headers['khalti-signature'] as string;

            const result = await this.khaltiService.processWebhook(webhookData, signature);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Webhook processed successfully",
                data: result,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Get payment by ID
    static getPaymentById = catchAsync(async (request: Request, response: Response) => {
        try {
            const { payment_id } = request.params;
            const user_id = request.decoded;

            const payment = await this.khaltiService.getPaymentById(
                parseInt(payment_id), 
                user_id
            );

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Payment details retrieved successfully",
                data: payment,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Get user payments
    static getUserPayments = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page = 1, limit = 10, status } = request.query;
            const user_id = request.decoded.id;

            const result = await this.khaltiService.getUserPayments(user_id, {
                page: parseInt(page as string),
                limit: parseInt(limit as string),
                status: status as string,
            });

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "User payments retrieved successfully",
                data: result,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Get all transactions (Admin)
    static getTransactions = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page = 1, limit = 10, status, search } = request.query;

            const result = await this.khaltiService.getTransactions({
                page: parseInt(page as string),
                limit: parseInt(limit as string),
                status: status as string,
                search: search as string,
            });

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Transactions retrieved successfully",
                data: result,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Get payment analytics (Admin)
    static getAnalytics = catchAsync(async (request: Request, response: Response) => {
        try {
            const { start_date, end_date, user_id } = request.query;

            const result = await this.khaltiService.getAnalytics({
                start_date: start_date as string,
                end_date: end_date as string,
                user_id: user_id ? parseInt(user_id as string) : undefined,
            });

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Payment analytics retrieved successfully",
                data: result,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Check payment status
    static checkPaymentStatus = catchAsync(async (request: Request, response: Response) => {
        try {
            const { pidx } = request.params;
            const user_id = request.decoded.id;

            const result = await this.khaltiService.verifyPayment(pidx, user_id);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Payment status checked successfully",
                data: {
                    status: result.local_status,
                    khalti_status: result.status,
                    transaction_id: result.transaction_id,
                    payment_id: result.payment_id,
                },
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Get payment configuration (for frontend)
    static getPaymentConfig = catchAsync(async (request: Request, response: Response) => {
        try {
            const config = {
                public_key: process.env.KHALTI_PUBLIC_KEY,
                return_url: `${process.env.FRONTEND_URL}/payment/khalti/callback`,
                website_url: process.env.FRONTEND_URL,
                currency: "NPR",
                supported_payment_methods: ["khalti", "ebanking", "mobile_banking", "connect_ips", "sct"]
            };

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Payment configuration retrieved successfully",
                data: config,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });
}
