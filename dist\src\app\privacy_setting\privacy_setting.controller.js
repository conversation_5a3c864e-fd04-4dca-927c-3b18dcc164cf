"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const privacy_setting_service_1 = __importDefault(require("./privacy_setting.service"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const response_1 = __importStar(require("../../utils/response"));
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const user_service_1 = __importDefault(require("../user/user.service"));
const email_service_1 = __importDefault(require("../../common/services/email.service"));
const token_service_1 = __importDefault(require("../../common/services/token.service"));
const mailMessages_1 = require("../../config/mailMessages");
const axios_1 = __importDefault(require("axios"));
const user_verifications_model_1 = __importDefault(require("../../database/models/user_verifications.model"));
const user_model_1 = __importDefault(require("../../database/models/user.model"));
class PrivacySettingController {
    constructor() { }
}
_a = PrivacySettingController;
PrivacySettingController.privacySettingService = privacy_setting_service_1.default;
PrivacySettingController.userService = user_service_1.default;
PrivacySettingController.emailService = email_service_1.default;
PrivacySettingController.tokenService = token_service_1.default;
PrivacySettingController.getAll = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search } = request.query;
        const userId = request.decoded;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search ? search : "",
            userId: userId,
        };
        const list = yield _a.privacySettingService.getPrivacySettings(option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: 'Privacy settings retrieved successfully',
            data: list,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
PrivacySettingController.create = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const body = Object.assign({}, request.body);
        const userId = request.decoded;
        body['user_id'] = userId;
        const craetedData = yield _a.privacySettingService.createPrivacySetting(body);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: 'Privacy setting created successfully',
            data: craetedData,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
PrivacySettingController.showById = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const Id = parseInt(request.params.id, 10);
        const details = yield _a.privacySettingService.getPrivacySettingById(Id);
        if (!details) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER_SUBSCRIPTION.NOT_FOUND);
        }
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: 'Privacy setting retrieved successfully',
            data: details,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
PrivacySettingController.update = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const Id = parseInt(request.params.id, 10);
        const body = Object.assign({}, request.body);
        const updatedData = yield _a.privacySettingService.updatePrivacySettingById(Id, body);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: 'Privacy setting updated successfully',
            data: updatedData,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
PrivacySettingController.delete = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = parseInt(request.params.id, 10);
        yield _a.privacySettingService.deletePrivacySettingById(id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: 'Privacy setting deleted successfully',
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
PrivacySettingController.hideProfile = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const body = Object.assign({}, request.body);
        const userId = request.decoded;
        body['user_id'] = userId;
        const craetedData = yield _a.privacySettingService.hideProfile(body);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: 'Profile hidden successfully',
            data: craetedData,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
PrivacySettingController.deleteProfile = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const body = Object.assign({}, request.body);
        const userId = request.decoded;
        body['user_id'] = userId;
        const craetedData = yield _a.privacySettingService.deleteProfile(body);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: 'Profile deleted successfully',
            data: craetedData,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
PrivacySettingController.verifyEmailOtp = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, otp } = request.body;
        const userId = request.decoded;
        const user = yield user_verifications_model_1.default.findOne({
            where: { user_id: userId },
        });
        if (!user || !user.email_otp || !user.email_otp_expiry) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.OTP.INVALID_OTP_NOT_FOUND);
        }
        ;
        if (user.email_otp !== otp) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.OTP.INVALID_OTP);
        }
        ;
        if (new Date() > new Date(user.email_otp_expiry)) {
            throw new ApiError_1.default(http_status_1.default.GONE, httpMessages_1.default.OTP.INVALID_OTP_EXPIRED);
        }
        ;
        user.is_email_verified = true;
        user.email_otp = '';
        user.email_otp_expiry = null;
        user.email = email;
        yield user.save();
        const userTable = yield user_model_1.default.update({ email: email }, { where: { id: userId } });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.OTP.EMAIL_OTP_VERIFIED,
            data: Object.assign({}, userTable),
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
PrivacySettingController.resendEmailOtp = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = request.body;
        const checkDublicate = yield _a.userService.getUserByEmail(email);
        if (checkDublicate) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.REGISTER.EMAIL_ALREADY_TAKEN);
        }
        let userId = request.decoded;
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        const expiryMinutes = 15;
        const otpExpiry = new Date(Date.now() + 15 * 60 * 1000);
        let html = yield (0, mailMessages_1.EmailTemplate)({
            type: "SENT_OTP",
            data: {
                userName: email,
                otp: otp,
                expiryMinutes: expiryMinutes
            },
        });
        const mailData = {
            to_addresses: [email],
            from_address: "<EMAIL>",
            from_name: "Bar Badhu",
            subject: "Bar Badhu - OTP Verification!",
            body: html,
            body_type: "html",
        };
        const apiUrl = process.env.MAIL_API_URL || 'https://www.tangomycp.com/amazon-ses/public/api/send-mail';
        yield axios_1.default.post(apiUrl, mailData);
        // 2. Save OTP to user table (you should update your model accordingly)
        const verification = yield user_verifications_model_1.default.findOne({
            where: { user_id: userId },
        });
        if (!verification) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "User not found");
        }
        const userVerificationBody = {
            user_id: userId,
            email_otp: otp,
            email_otp_expiry: otpExpiry,
        };
        const user = yield verification.update(Object.assign({}, userVerificationBody));
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.OTP.RESENT_OTP,
            data: null,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
PrivacySettingController.sendPhoneOtp = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { phone, phone_code } = request.body;
        const userId = request.decoded;
        const verification = yield _a.privacySettingService.sendPhoneOtp(userId, phone, phone_code);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "OTP sent successfully",
            data: verification,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
PrivacySettingController.verifyPhoneOtp = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { phone, phone_code, otp } = request.body;
        const userId = request.decoded;
        const verification = yield _a.privacySettingService.verifyPhoneOtp(userId, phone, otp, phone_code);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Phone verified successfully",
            data: verification,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = PrivacySettingController;
