"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    LOGIN: {
        SUCCESS: "Login successfully.",
        INCORRECT_EMAIL: "Incorrect email!",
        INCORRECT_PASS: "Incorrect password!",
    },
    REGISTER: {
        SUCCESS: "You have successfully registered.",
        INVALID_EMAIL: "",
        INVALID_PHONE: "Invalid phone number!, Please enter valid format (XXX)XXX-XXXX",
        SHORT_PASSWORD: "Password must be at least 8 characters!",
        PASSWORD_MISMATCH: "Confirm password does not match!",
        EMAIL_ALREADY_TAKEN: "Email already taken!",
        PHONE_ALREADY_TAKEN: "Phone already taken!",
    },
    PAYPAL: {
        ACCESS_TOKEN_GENERATED: "Access token generated successfully.",
    },
    OTP: {
        INVALID_OTP: "Invalid OTP",
        EMAIL_OTP_VERIFIED: "Email verified successfully.",
        INVALID_OTP_NOT_FOUND: "OTP not found. Please request a new one.",
        RESENT_OTP: "OTP resent successfully.",
        INVALID_OTP_EXPIRED: "OTP not found. Please request a new one.",
    },
    MODULES: {
        SUCCESS: "Get modules successfully.",
        ADD_SUCCESS: "Module added successfully.",
        UPDATE_SUCCESS: "Module updated successfully.",
        NOT_FOUND: "Module not found!",
        DELETE: "Module deleted successfully.",
        DETAILS: {
            SUCCESS: "Get module details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
        ROUTE_ALREADY_TAKEN: "Route already taken!",
    },
    GEOLOCATION: {
        COUNTRY: {
            SUCCESS: "Get countries successfully.",
            ADD_SUCCESS: "country added successfully.",
            UPDATE_SUCCESS: "country updated successfully.",
            NOT_FOUND: "country not found!",
            DELETE: "country deleted successfully.",
            DETAILS: {
                SUCCESS: "Get country details successfully.",
            },
            NAME_ALREADY_TAKEN: "country Name already taken!",
        },
        CITY: {
            SUCCESS: "Get city successfully.",
            ADD_SUCCESS: "city added successfully.",
            UPDATE_SUCCESS: "city updated successfully.",
            NOT_FOUND: "city not found!",
            DELETE: "city deleted successfully.",
            DETAILS: {
                SUCCESS: "Get city details successfully.",
            },
            NAME_ALREADY_TAKEN: "Name already taken!",
        },
    },
    ENCRYPT: "Invalid encrypted data",
    INVALID_DATE: "Invalid date format.",
    REFRESH_TOKEN_SUCCESS: "Refresh token fetch successfully.",
    REFRESH_TOKEN_ERROR: "Invalid refresh token",
    CUSTOMER: {
        SUCCESS: "Customer retrieved successfully.",
        NOT_FOUND: "Customer not found!",
    },
    USER: {
        AUTH: {
            UNAUTHORIZED: "Unauthorized",
            FORBIDDEN: "Forbidden",
            DEACTIVE: "The user is not active. Please activate the user to proceed.",
            NOT_ASSIGND_HOTEL: "The user is not assigned to any hotel. Please assign the user to a hotel first.",
            TOKEN: {
                NOT_FOUND: "Token not found",
                EXPIRED: "Token expired",
            },
        },
        SUCCESS: "Users retrieved successfully.",
        DELETE: "User deleted successfully.",
        EMAIL_NOT_FOUND: "No users found with this email!",
        NOT_FOUND: "User not found!",
        NOT_UPDATE: "User can not updated!",
        ADD_SUCCESS: "User added successfully.",
        UPDATE_SUCCESS: "User updated successfully.",
        DETAILS: {
            SUCCESS: "Get User details successfully.",
        },
        PROFILE: {
            SUCCESS: "User profile retrieved successfully.",
            UPDATE_SUCCESS: "User profile updated successfully.",
            PASSWORD_DOES_NOT_MATCH: "Your current passwords do not match!",
            PASSWORD_UPDATE_SUCCESS: "password updated successfully.",
            RESET_PASSWORD_SUCCESS: "Reset password successfully.",
            RESET_PASSWORD_FAILURE: "Password reset failed!",
            AGE_RANGE_FAILURE: "Age should be in range 25-60 !",
            EMAIL_CHANGE_SUCCESS: "Email changed successfully.",
            RESET_PASSWORD_EMAIL_SENT: "Reset password email sent successfully.",
        },
        ADDRESS: {
            NOT_FOUND: "Address not found.",
            GET_ALL_SUCCESS: "Addresses retrieved successfully.",
            GET_SUCCESS: "Address retrieved successfully.",
            ADD_SUCCESS: "Address added successfully.",
            UPDATE_SUCCESS: "Address updated successfully.",
            DELETE_SUCCESS: "Address deleted successfully.",
        },
    },
    STAFF: {
        SUCCESS: "Get Staff successfully.",
        ADD_SUCCESS: "Staff added successfully.",
        UPDATE_SUCCESS: "Staff updated successfully.",
        DELETE: "Staff deleted successfully.",
        NOT_FOUND: "Staff not found!",
        DETAILS: {
            SUCCESS: "Get Staff details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NUMBER_ALREADY_TAKEN: "Number already taken!",
        EMAIL_ALREADY_TAKEN: "Email already taken!",
    },
    ROLES: {
        SUCCESS: "Get role successfully.",
        ADD_SUCCESS: "role added successfully.",
        UPDATE_SUCCESS: "role updated successfully.",
        NOT_FOUND: "role not found!",
        DELETE: "role deleted successfully.",
        DETAILS: {
            SUCCESS: "Get role details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    USERPRE: {
        SUCCESS: "Get User Preference successfully.",
        ADD_SUCCESS: "User Preference added successfully.",
        UPDATE_SUCCESS: "User Preference updated successfully.",
        NOT_FOUND: "User Preference not found!",
        DELETE: "User Preference deleted successfully.",
        DETAILS: {
            SUCCESS: "Get User Preference details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    USER_GALLERY: {
        SUCCESS: "Get User Gallery successfully.",
        ADD_SUCCESS: "User Gallery added successfully.",
        UPDATE_SUCCESS: "User Gallery updated successfully.",
        NOT_FOUND: "User Gallery not found!",
        DELETE: "User Gallery deleted successfully.",
        DETAILS: {
            SUCCESS: "Get User Gallery details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    SUBSCRIPTION_PLAN: {
        SUCCESS: "Get subscription plan successfully.",
        ADD_SUCCESS: "subscription plan added successfully.",
        UPDATE_SUCCESS: "subscription plan updated successfully.",
        NOT_FOUND: "subscription plan not found!",
        DELETE: "subscription plan deleted successfully.",
        DETAILS: {
            SUCCESS: "Get subscription plan details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    USER_VERIFICATION: {
        SUCCESS: "Get User Verification successfully.",
        ADD_SUCCESS: "User Verification added successfully.",
        UPDATE_SUCCESS: "User Verification updated successfully.",
        NOT_FOUND: "User Verification not found!",
        DELETE: "User Verification deleted successfully.",
        DETAILS: {
            SUCCESS: "Get User Verification details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    USER_SUBSCRIPTION: {
        SUCCESS: "Get user subscription successfully.",
        ADD_SUCCESS: "user subscription added successfully.",
        UPDATE_SUCCESS: "user subscription updated successfully.",
        NOT_FOUND: "user subscription not found!",
        DELETE: "user subscription deleted successfully.",
        DETAILS: {
            SUCCESS: "Get user subscription details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    COUNTRY: {
        SUCCESS: "Get country successfully.",
        ADD_SUCCESS: "country added successfully.",
        UPDATE_SUCCESS: "country updated successfully.",
        NOT_FOUND: "country not found!",
        DELETE: "country deleted successfully.",
        DETAILS: {
            SUCCESS: "Get country details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    CITY: {
        SUCCESS: "Get city successfully.",
        ADD_SUCCESS: "city added successfully.",
        UPDATE_SUCCESS: "city updated successfully.",
        NOT_FOUND: "city not found!",
        DELETE: "city deleted successfully.",
        DETAILS: {
            SUCCESS: "Get city details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    HELP: {
        SUCCESS: "Get help category successfully.",
        ADD_SUCCESS: "help category added successfully.",
        UPDATE_SUCCESS: "help category updated successfully.",
        NOT_FOUND: "help category not found!",
        DELETE: "help category deleted successfully.",
        DETAILS: {
            SUCCESS: "Get help category details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    HELP_QUESTION: {
        SUCCESS: "Get help question successfully.",
        ADD_SUCCESS: "help question added successfully.",
        UPDATE_SUCCESS: "help question updated successfully.",
        NOT_FOUND: "help question not found!",
        DELETE: "help question deleted successfully.",
        DETAILS: {
            SUCCESS: "Get help question details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    PAYPAL_PAYMENT: {
        SUCCESS: "Paypal payment retrieved successfully.",
        ADD_SUCCESS: "Paypal payment added successfully.",
        UPDATE_SUCCESS: "Paypal payment updated successfully.",
        NOT_FOUND: "Paypal payment not found!",
        DELETE: "Paypal payment deleted successfully.",
        DETAILS: {
            SUCCESS: "Get Paypal payment details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    PAYPAL_WEBHOOK: {
        SUCCESS: "Paypal webhook retrieved successfully.",
        ADD_SUCCESS: "Paypal webhook added successfully.",
        UPDATE_SUCCESS: "Paypal webhook updated successfully.",
        NOT_FOUND: "Paypal webhook not found!",
        DELETE: "Paypal webhook deleted successfully.",
        DETAILS: {
            SUCCESS: "Get Paypal webhook details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    PAYMENT: {
        SUCCESS: "Payment retrieved successfully.",
        ADD_SUCCESS: "Payment added successfully.",
        UPDATE_SUCCESS: "Payment updated successfully.",
        NOT_FOUND: "Payment not found!",
        DELETE: "Payment deleted successfully.",
        DETAILS: {
            SUCCESS: "Get Payment details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    PAYMENT_STATUS: {
        SUCCESS: "Payment status retrieved successfully.",
        ADD_SUCCESS: "Payment status added successfully.",
        UPDATE_SUCCESS: "Payment status updated successfully.",
        NOT_FOUND: "Payment status not found!",
        DELETE: "Payment status deleted successfully.",
        DETAILS: {
            SUCCESS: "Get Payment status details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    PAYMENT_METHOD: {
        SUCCESS: "Payment method retrieved successfully.",
        ADD_SUCCESS: "Payment method added successfully.",
        UPDATE_SUCCESS: "Payment method updated successfully.",
        NOT_FOUND: "Payment method not found!",
        DELETE: "Payment method deleted successfully.",
        DETAILS: {
            SUCCESS: "Get Payment method details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    PAYMENT_TRANSACTION: {
        SUCCESS: "Payment transaction retrieved successfully.",
        ADD_SUCCESS: "Payment transaction added successfully.",
        UPDATE_SUCCESS: "Payment transaction updated successfully.",
        NOT_FOUND: "Payment transaction not found!",
        DELETE: "Payment transaction deleted successfully.",
        DETAILS: {
            SUCCESS: "Get Payment transaction details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    PAYMENT_REFUND: {
        SUCCESS: "Payment refund retrieved successfully.",
        ADD_SUCCESS: "Payment refund added successfully.",
        UPDATE_SUCCESS: "Payment refund updated successfully.",
        NOT_FOUND: "Payment refund not found!",
        DELETE: "Payment refund deleted successfully.",
        DETAILS: {
            SUCCESS: "Get Payment refund details successfully.",
        },
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    DOCUMENT: {
        SUCCESS: "Document retrieved successfully.",
        ADD_SUCCESS: "Document added successfully.",
        UPDATE_SUCCESS: "Document updated successfully.",
        NOT_FOUND: "Document not found!",
        DELETE: "Document deleted successfully.",
        DETAILS: {
            SUCCESS: "Get Document details successfully.",
        },
        ERROR: "Error processing file uploads.",
        PASSWORD_MISMATCH: "Confirm password does not match!",
        NAME_ALREADY_TAKEN: "Name already taken!",
    },
    SUCCESS_STORY: {
        SUCCESS: "Success stories retrieved successfully.",
        ADD_SUCCESS: "Success story submitted successfully.",
        UPDATE_SUCCESS: "Success story updated successfully.",
        STATUS_UPDATE_SUCCESS: "Success story status updated successfully.",
        NOT_FOUND: "Success story not found!",
        DELETE: "Success story deleted successfully.",
        DETAILS: {
            SUCCESS: "Success story details retrieved successfully.",
        },
        APPROVED: "Success story approved successfully.",
        REJECTED: "Success story rejected successfully.",
        PENDING: "Success story is pending for review.",
    },
};
