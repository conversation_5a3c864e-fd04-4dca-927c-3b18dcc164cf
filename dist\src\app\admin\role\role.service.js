"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const bcrypt_1 = __importDefault(require("bcrypt"));
const sequelize_1 = require("sequelize");
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../../config/httpMessages"));
const role_model_1 = __importDefault(require("../../../database/models/role.model"));
const modules_model_1 = __importDefault(require("../../../database/models/modules.model"));
const role_permissions_model_1 = __importDefault(require("../../../database/models/role_permissions.model"));
class RoleService {
    constructor() { }
}
_a = RoleService;
RoleService.isPasswordMatch = (user, password) => __awaiter(void 0, void 0, void 0, function* () { return yield bcrypt_1.default.compare(password, user.password); });
/**
 * Get role by rolename
 * @param {string} role_name
 * @param {any} options
 * @returns {Promise<Role>}
 */
RoleService.getRoleByRoleName = (role_name) => __awaiter(void 0, void 0, void 0, function* () {
    return role_model_1.default.findOne({
        where: { role_name },
    });
});
/**
 * Create a Role
 * @param {Object} roleBody
 * @returns {Promise<Role>}
 */
RoleService.createRole = (roleBody) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (yield _a.getRoleByRoleName(roleBody.role_name)) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.ROLES.NAME_ALREADY_TAKEN);
        }
        const role = yield role_model_1.default.create(roleBody);
        const modules = yield modules_model_1.default.findAll();
        const rolePermissionsData = modules.map((module) => {
            return {
                role_id: role.id,
                module_id: module.id,
            };
        });
        yield role_permissions_model_1.default.bulkCreate(rolePermissionsData);
        return yield _a.getRoleById(role.id);
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Return Roles
 * @param {Object} options
 * @param {number} [options.page] - Current page number (optional)
 * @param {number} [options.limit] - Number of items per page (optional)
 * @param {string} [options.search] - Search term for filtering (optional)
 * @returns {Promise<Role[]>}
 */
RoleService.getRoles = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search } = options;
        const whereCondition = search
            ? {
                [sequelize_1.Op.or]: [
                    { role_name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                ],
            }
            : {};
        const queryOption = {
            where: whereCondition,
            order: [["createdAt", "DESC"]],
        };
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const roles = yield role_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: roles.count,
                totalPages: Math.ceil(roles.count / limit),
                currentPage: page,
                roles: roles.rows,
            };
        }
        else {
            return roles.rows;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get role by id
 * @param {Number} id
 * @returns {Promise<Role>}
 */
RoleService.getRoleById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return role_model_1.default.findOne({
            where: { id },
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update role by id
 * @param {Number} roleId
 * @param {Object} updateBody
 * @returns {Promise<Role>}
 */
RoleService.updateRoleById = (roleId, updateBody) => __awaiter(void 0, void 0, void 0, function* () {
    const role = yield role_model_1.default.findByPk(roleId);
    if (!role) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.ROLES.NOT_FOUND);
    }
    Object.assign(role, updateBody);
    yield role.save();
    return role;
});
/**
 * Delete role by id
 * @param {Number} roleId
 * @returns {Promise<Role>}
 */
RoleService.deleteUserById = (roleId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const role = yield role_model_1.default.findByPk(roleId);
        if (!role) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.ROLES.NOT_FOUND);
        }
        yield role.destroy();
        return role;
    }
    catch (error) {
        if (error.name === "SequelizeForeignKeyConstraintError") {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Cannot delete this Record as it is referenced in another table.");
        }
        else {
            throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting Role.");
        }
    }
});
exports.default = RoleService;
