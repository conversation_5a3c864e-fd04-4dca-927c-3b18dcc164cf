 import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../../utils/response";
import httpMessages from "../../../config/httpMessages";
import HelpCategoryService from "./help_category.service";
import HelpCategory from "../../../database/models/help_category.model";

export default class HelpCategoryController {
  static helpCategoryService = HelpCategoryService;
  constructor() { }

  static getAll = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search } = request.query;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
      };
      const categories = await this.helpCategoryService.getHelpCategories(option);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.HELP.SUCCESS,
        data: categories,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const body = request.body;
      const category = await this.helpCategoryService.createHelpCategory(body);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.HELP.ADD_SUCCESS,
        data: category,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static showById = catchAsync(async (request: Request, response: Response) => {
    try {
      const categoryId: number = parseInt(request.params.id, 10);
      const category = await this.helpCategoryService.getHelpCategoryById(categoryId);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.HELP.DETAILS.SUCCESS,
        data: category,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static update = catchAsync(async (request: Request, response: Response) => {
    try {
      const categoryId: number = parseInt(request.params.id, 10);
      const body = request.body;
      const category = await this.helpCategoryService.updateHelpCategoryById(categoryId, body);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.HELP.UPDATE_SUCCESS,
        data: category,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static delete = catchAsync(async (request: Request, response: Response) => {
    try {
      const categoryId: number = parseInt(request.params.id, 10);
      await this.helpCategoryService.deleteHelpCategoryById(categoryId);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.HELP.DELETE,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });
}





