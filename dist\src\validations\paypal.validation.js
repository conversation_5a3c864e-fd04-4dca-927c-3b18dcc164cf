"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.webhookValidation = exports.refundPaymentValidation = exports.getPaymentAnalyticsValidation = exports.getUserPaymentsValidation = exports.getPaymentDetailsValidation = exports.capturePaymentValidation = exports.createOrderValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.createOrderValidation = {
    body: joi_1.default.object().keys({
        plan_id: joi_1.default.number().integer().positive().optional().messages({
            "number.base": "Plan ID must be a number.",
            "number.integer": "Plan ID must be an integer.",
            "number.positive": "Plan ID must be a positive number.",
        }),
        amount: joi_1.default.number().positive().precision(2).required().messages({
            "number.base": "Amount must be a number.",
            "number.positive": "Amount must be a positive number.",
            "any.required": "Amount is required.",
        }),
        currency: joi_1.default.string().length(3).uppercase().default("USD").messages({
            "string.base": "Currency must be a string.",
            "string.length": "Currency must be exactly 3 characters.",
        }),
    }),
};
exports.capturePaymentValidation = {
    params: joi_1.default.object().keys({
        order_id: joi_1.default.string().required().messages({
            "string.base": "Order ID must be a string.",
            "string.empty": "Order ID is required.",
            "any.required": "Order ID is required.",
        }),
    }),
};
exports.getPaymentDetailsValidation = {
    params: joi_1.default.object().keys({
        payment_id: joi_1.default.string().required().messages({
            "string.base": "Payment ID must be a string.",
            "string.empty": "Payment ID is required.",
            "any.required": "Payment ID is required.",
        }),
    }),
};
exports.getUserPaymentsValidation = {
    query: joi_1.default.object().keys({
        page: joi_1.default.number().integer().min(1).default(1).messages({
            "number.base": "Page must be a number.",
            "number.integer": "Page must be an integer.",
            "number.min": "Page must be at least 1.",
        }),
        limit: joi_1.default.number().integer().min(1).max(100).default(10).messages({
            "number.base": "Limit must be a number.",
            "number.integer": "Limit must be an integer.",
            "number.min": "Limit must be at least 1.",
            "number.max": "Limit cannot exceed 100.",
        }),
        status: joi_1.default.string().valid("pending", "completed", "failed", "cancelled", "refunded").optional().messages({
            "string.base": "Status must be a string.",
            "any.only": "Status must be one of: pending, completed, failed, cancelled, refunded.",
        }),
        search: joi_1.default.string().optional().messages({
            "string.base": "Search must be a string.",
        }),
    }),
};
exports.getPaymentAnalyticsValidation = {
    query: joi_1.default.object().keys({
        start_date: joi_1.default.date().iso().optional().messages({
            "date.base": "Start date must be a valid date.",
            "date.format": "Start date must be in ISO format.",
        }),
        end_date: joi_1.default.date().iso().min(joi_1.default.ref('start_date')).optional().messages({
            "date.base": "End date must be a valid date.",
            "date.format": "End date must be in ISO format.",
            "date.min": "End date must be after start date.",
        }),
        user_id: joi_1.default.number().integer().positive().optional().messages({
            "number.base": "User ID must be a number.",
            "number.integer": "User ID must be an integer.",
            "number.positive": "User ID must be a positive number.",
        }),
    }),
};
exports.refundPaymentValidation = {
    params: joi_1.default.object().keys({
        payment_id: joi_1.default.string().required().messages({
            "string.base": "Payment ID must be a string.",
            "string.empty": "Payment ID is required.",
            "any.required": "Payment ID is required.",
        }),
    }),
    body: joi_1.default.object().keys({
        amount: joi_1.default.number().positive().precision(2).optional().messages({
            "number.base": "Amount must be a number.",
            "number.positive": "Amount must be a positive number.",
        }),
        reason: joi_1.default.string().max(255).optional().messages({
            "string.base": "Reason must be a string.",
            "string.max": "Reason cannot exceed 255 characters.",
        }),
    }),
};
exports.webhookValidation = {
    body: joi_1.default.object().required().messages({
        "object.base": "Webhook body must be an object.",
        "any.required": "Webhook body is required.",
    }),
};
