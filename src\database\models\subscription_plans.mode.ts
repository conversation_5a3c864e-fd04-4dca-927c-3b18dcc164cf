import {
    AllowNull,
    AutoIncrement,
    Column,
    <PERSON>T<PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    NotEmpty,
    PrimaryKey,
    Table
} from "sequelize-typescript";
import UserSubscription from "./user_subscriptions.model";

export interface SubscriptionPlanI {
    id: number;
    name: string;
    price: number;
    duration_days: number;
    interest: boolean;
    interest_limit: number;
    contact: boolean;
    contact_limit: number;
    view_profiles: boolean;
    view_profiles_limit: number;
    chat: boolean;
    chat_limit: number;
    has_verified_badge: boolean;
    is_personalized_matchmaking: boolean;
    is_active: boolean;
}


@Table({
    tableName: "subscription_plans",
    timestamps: true
})
class SubscriptionPlan extends Model<SubscriptionPlanI> implements SubscriptionPlanI {
    @AutoIncrement
    @PrimaryKey
    @Column
    id: number;

    @AllowNull(false)
    @NotEmpty
    @Column(DataType.STRING)
    name: string;

    @AllowNull(false)
    @Column(DataType.DECIMAL(10, 2))
    price: number;

    @AllowNull(false)
    @Column(DataType.INTEGER)
    duration_days: number;

    @AllowNull(false)
    @Column(DataType.BOOLEAN)
    interest: boolean;

    @AllowNull(true)
    @Column(DataType.INTEGER)
    interest_limit: number;

    @AllowNull(false)
    @Column(DataType.BOOLEAN)
    contact: boolean;

    @AllowNull(true)
    @Column(DataType.INTEGER)
    contact_limit: number;

    @AllowNull(false)
    @Column(DataType.BOOLEAN)
    view_profiles: boolean;

    @AllowNull(true)
    @Column(DataType.INTEGER)
    view_profiles_limit: number;

    @AllowNull(false)
    @Column(DataType.BOOLEAN)
    chat: boolean;

    @AllowNull(true)
    @Column(DataType.INTEGER)
    chat_limit: number;

    @AllowNull(false)
    @Default(false)
    @Column(DataType.BOOLEAN)
    has_verified_badge: boolean;

    @AllowNull(false)
    @Default(false)
    @Column(DataType.BOOLEAN)
    is_personalized_matchmaking: boolean;

    @AllowNull(false)
    @Default(true)
    @Column(DataType.BOOLEAN)
    is_active: boolean;

    @HasMany(() => UserSubscription, { foreignKey: "plan_id", onDelete: "CASCADE", })
    user_subscription: UserSubscription[];
}

export default SubscriptionPlan;



