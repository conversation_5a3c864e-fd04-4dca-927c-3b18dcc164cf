"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const joi_1 = __importDefault(require("joi"));
const sendMessage = {
    body: joi_1.default.object().keys({
        chatId: joi_1.default.number().required(),
        receiverId: joi_1.default.number().required(),
        content: joi_1.default.string().required().max(5000),
    }),
};
const markAsDelivered = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required(),
    }),
};
const markAsRead = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required(),
    }),
};
const markAllAsRead = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required(),
    }),
};
exports.default = {
    sendMessage,
    markAsDelivered,
    markAsRead,
    markAllAsRead,
};
