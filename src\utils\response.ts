import { Response } from "express";
 
const errorResponse = (response: Response, error: any) => {
  const statusCode = error.statusCode || 500;
  const message = error.message || "Internal server error!";
  response.status(statusCode).json({
    success: false,
    message,
  });
};

export const sentResponse = (response: Response, success: any) => {
  const statusCode = success?.statusCode || 500;
  const message = success?.message || "Internal server error!";
  const data = success?.data || null;
  return response.status(statusCode).send({
    success: true,
    message,
    data,
  });
};

export default errorResponse;
