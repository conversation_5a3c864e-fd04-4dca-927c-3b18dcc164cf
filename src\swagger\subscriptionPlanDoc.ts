/**
 * @swagger
 * /subscription_plan:
 *   get:
 *     tags:
 *       - SubscriptionPlans
 *     summary: Get all subscription plans
 *     description: Retrieve a list of all subscription plans with pagination.
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           example: 1
 *         description: Page number for pagination.
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Number of records per page.
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for filtering plans.
 *     responses:
 *       200:
 *         description: Successfully retrieved subscription plans.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Subscription plans retrieved successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     count:
 *                       type: integer
 *                       example: 5
 *                     rows:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           name:
 *                             type: string
 *                             example: "Premium Plan"
 *                           price:
 *                             type: number
 *                             format: float
 *                             example: 99.99
 *                           duration_days:
 *                             type: integer
 *                             example: 30
 *                           interest:
 *                             type: boolean
 *                             example: true
 *                           interest_limit:
 *                             type: integer
 *                             example: 50
 *                           contact:
 *                             type: boolean
 *                             example: true
 *                           contact_limit:
 *                             type: integer
 *                             example: 20
 *                           view_profiles:
 *                             type: boolean
 *                             example: true
 *                           view_profiles_limit:
 *                             type: integer
 *                             example: 100
 *                           chat:
 *                             type: boolean
 *                             example: true
 *                           chat_limit:
 *                             type: integer
 *                             example: 30
 *                           has_verified_badge:
 *                             type: boolean
 *                             example: true
 *                           is_personalized_matchmaking:
 *                             type: boolean
 *                             example: false
 *                           is_active:
 *                             type: boolean
 *                             example: true
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                           updatedAt:
 *                             type: string
 *                             format: date-time
 *       401:
 *         description: Unauthorized - Authentication required.
 *       500:
 *         description: Internal server error.
 */

/**
 * @swagger
 * /subscription_plan:
 *   post:
 *     tags:
 *       - SubscriptionPlans
 *     summary: Create a new subscription plan
 *     description: Create and store a new subscription plan.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - price
 *               - duration_days
 *               - interest
 *               - contact
 *               - view_profiles
 *               - chat
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Premium Plan"
 *               price:
 *                 type: number
 *                 format: float
 *                 example: 99.99
 *               duration_days:
 *                 type: integer
 *                 example: 30
 *               interest:
 *                 type: boolean
 *                 example: true
 *               interest_limit:
 *                 type: integer
 *                 example: 50
 *               contact:
 *                 type: boolean
 *                 example: true
 *               contact_limit:
 *                 type: integer
 *                 example: 20
 *               view_profiles:
 *                 type: boolean
 *                 example: true
 *               view_profiles_limit:
 *                 type: integer
 *                 example: 100
 *               chat:
 *                 type: boolean
 *                 example: true
 *               chat_limit:
 *                 type: integer
 *                 example: 30
 *               has_verified_badge:
 *                 type: boolean
 *                 example: true
 *               is_personalized_matchmaking:
 *                 type: boolean
 *                 example: false
 *               is_active:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Subscription plan created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Subscription plan created successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     name:
 *                       type: string
 *                       example: "Premium Plan"
 *                     price:
 *                       type: number
 *                       format: float
 *                       example: 99.99
 *                     duration_days:
 *                       type: integer
 *                       example: 30
 *                     interest:
 *                       type: boolean
 *                       example: true
 *                     interest_limit:
 *                       type: integer
 *                       example: 50
 *                     contact:
 *                       type: boolean
 *                       example: true
 *                     contact_limit:
 *                       type: integer
 *                       example: 20
 *                     view_profiles:
 *                       type: boolean
 *                       example: true
 *                     view_profiles_limit:
 *                       type: integer
 *                       example: 100
 *                     chat:
 *                       type: boolean
 *                       example: true
 *                     chat_limit:
 *                       type: integer
 *                       example: 30
 *                     has_verified_badge:
 *                       type: boolean
 *                       example: true
 *                     is_personalized_matchmaking:
 *                       type: boolean
 *                       example: false
 *                     is_active:
 *                       type: boolean
 *                       example: true
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Bad request - Invalid input data.
 *       401:
 *         description: Unauthorized - Authentication required.
 *       500:
 *         description: Internal server error.
 */

/**
 * @swagger
 * /subscription_plan/{id}:
 *   get:
 *     tags:
 *       - SubscriptionPlans
 *     summary: Get a subscription plan by ID
 *     description: Retrieve a specific subscription plan by its ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *         description: The ID of the subscription plan to retrieve.
 *     responses:
 *       200:
 *         description: Successfully retrieved the subscription plan.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Subscription plan retrieved successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     name:
 *                       type: string
 *                       example: "Premium Plan"
 *                     price:
 *                       type: number
 *                       format: float
 *                       example: 99.99
 *                     duration_days:
 *                       type: integer
 *                       example: 30
 *                     interest:
 *                       type: boolean
 *                       example: true
 *                     interest_limit:
 *                       type: integer
 *                       example: 50
 *                     contact:
 *                       type: boolean
 *                       example: true
 *                     contact_limit:
 *                       type: integer
 *                       example: 20
 *                     view_profiles:
 *                       type: boolean
 *                       example: true
 *                     view_profiles_limit:
 *                       type: integer
 *                       example: 100
 *                     chat:
 *                       type: boolean
 *                       example: true
 *                     chat_limit:
 *                       type: integer
 *                       example: 30
 *                     has_verified_badge:
 *                       type: boolean
 *                       example: true
 *                     is_personalized_matchmaking:
 *                       type: boolean
 *                       example: false
 *                     is_active:
 *                       type: boolean
 *                       example: true
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized - Authentication required.
 *       404:
 *         description: Subscription plan not found.
 *       500:
 *         description: Internal server error.
 */

/**
 * @swagger
 * /subscription_plan/{id}:
 *   put:
 *     tags:
 *       - SubscriptionPlans
 *     summary: Update a subscription plan by ID
 *     description: Update an existing subscription plan by its ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *         description: The ID of the subscription plan to update.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - price
 *               - duration_days
 *               - interest
 *               - contact
 *               - view_profiles
 *               - chat
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Premium Plan Updated"
 *               price:
 *                 type: number
 *                 format: float
 *                 example: 129.99
 *               duration_days:
 *                 type: integer
 *                 example: 45
 *               interest:
 *                 type: boolean
 *                 example: true
 *               interest_limit:
 *                 type: integer
 *                 example: 75
 *               contact:
 *                 type: boolean
 *                 example: true
 *               contact_limit:
 *                 type: integer
 *                 example: 30
 *               view_profiles:
 *                 type: boolean
 *                 example: true
 *               view_profiles_limit:
 *                 type: integer
 *                 example: 150
 *               chat:
 *                 type: boolean
 *                 example: true
 *               chat_limit:
 *                 type: integer
 *                 example: 50
 *               has_verified_badge:
 *                 type: boolean
 *                 example: true
 *               is_personalized_matchmaking:
 *                 type: boolean
 *                 example: true
 *               is_active:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Subscription plan updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Subscription plan updated successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     name:
 *                       type: string
 *                       example: "Premium Plan Updated"
 *                     price:
 *                       type: number
 *                       format: float
 *                       example: 129.99
 *                     duration_days:
 *                       type: integer
 *                       example: 45
 *                     interest:
 *                       type: boolean
 *                       example: true
 *                     interest_limit:
 *                       type: integer
 *                       example: 75
 *                     contact:
 *                       type: boolean
 *                       example: true
 *                     contact_limit:
 *                       type: integer
 *                       example: 30
 *                     view_profiles:
 *                       type: boolean
 *                       example: true
 *                     view_profiles_limit:
 *                       type: integer
 *                       example: 150
 *                     chat:
 *                       type: boolean
 *                       example: true
 *                     chat_limit:
 *                       type: integer
 *                       example: 50
 *                     has_verified_badge:
 *                       type: boolean
 *                       example: true
 *                     is_personalized_matchmaking:
 *                       type: boolean
 *                       example: true
 *                     is_active:
 *                       type: boolean
 *                       example: true
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Bad request - Invalid input data.
 *       401:
 *         description: Unauthorized - Authentication required.
 *       404:
 *         description: Subscription plan not found.
 *       500:
 *         description: Internal server error.
 */

/**
 * @swagger
 * /subscription_plan/{id}:
 *   delete:
 *     tags:
 *       - SubscriptionPlans
 *     summary: Delete a subscription plan by ID
 *     description: Remove an existing subscription plan by its ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *         description: The ID of the subscription plan to delete.
 *     responses:
 *       200:
 *         description: Subscription plan deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Subscription plan deleted successfully.
 *                 data:
 *                   type: object
 *                   example: {}
 *       401:
 *         description: Unauthorized - Authentication required.
 *       404:
 *         description: Subscription plan not found.
 *       500:
 *         description: Internal server error.
 */
