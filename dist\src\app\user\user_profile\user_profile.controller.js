"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../../utils/catchAsync"));
const response_1 = __importStar(require("../../../utils/response"));
const httpMessages_1 = __importDefault(require("../../../config/httpMessages"));
const user_profile_service_1 = __importDefault(require("./user_profile.service"));
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const user_service_1 = __importDefault(require("../user.service"));
const token_service_1 = __importDefault(require("../../../common/services/token.service"));
const subscription_service_1 = __importDefault(require("../../subscription/subscription.service"));
class UserProfileController {
    constructor() { }
}
_a = UserProfileController;
UserProfileController.userService = user_service_1.default;
UserProfileController.userProfileService = user_profile_service_1.default;
UserProfileController.tokenService = token_service_1.default;
UserProfileController.subscriptionService = subscription_service_1.default;
UserProfileController.getAll = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, filters } = request.query;
        const decoded = request.decoded;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search ? search : "",
            filters: filters ? JSON.parse(filters) : null,
            currentUserId: decoded,
        };
        const users = yield _a.userProfileService.getUserProfiles(option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.SUCCESS,
            data: users,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserProfileController.getUserSearch = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, filters } = request.query;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search ? search : "",
            filters: filters ? JSON.parse(filters) : null,
        };
        console.log('option: ', option);
        const users = yield _a.userProfileService.getUserSearchProfiles(option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.SUCCESS,
            data: users,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserProfileController.create = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userProfileData = Object.assign({}, request.body);
        const userId = request.decoded;
        let keyCount = 0;
        let message = '';
        for (const key in userProfileData) {
            keyCount++;
            if (userProfileData[key] && typeof userProfileData[key] === 'object') {
                userProfileData[key].user_id = userId;
            }
        }
        let userData = yield _a.userProfileService.createUserDetails(userProfileData, userId);
        if (keyCount === 1)
            message = `${userData.message} saved successfully`;
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: message || "Profile Updated successfully",
            data: userData,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserProfileController.showById = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId } = request.params;
        if (userId === "mefull") {
            let decoded = request.decoded;
            if (!decoded) {
                throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED);
            }
            // Set the id to the decoded id
            const user = yield _a.userProfileService.getUserProfileById(decoded);
            if (!user) {
                throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED);
            }
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.OK,
                message: httpMessages_1.default.USER.DETAILS.SUCCESS,
                data: user,
            });
        }
        const user = yield _a.userProfileService.getUserProfileById(parseInt(userId));
        if (!user) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER.NOT_FOUND);
        }
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.DETAILS.SUCCESS,
            data: user,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserProfileController.viewProfile = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = request.params;
        if (id === "mefull") {
            let decoded = request.decoded;
            if (!decoded) {
                throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED);
            }
            // Set the id to the decoded id
            const user = yield _a.userProfileService.getUserProfileById(decoded);
            if (!user) {
                throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED);
            }
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.OK,
                message: httpMessages_1.default.USER.DETAILS.SUCCESS,
                data: user,
            });
        }
        const user = yield _a.userProfileService.getUserProfileById(parseInt(id));
        if (!user) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER.NOT_FOUND);
        }
        // await SubscriptionService.trackUsage({
        //   user_id: parseInt(request.decoded),
        //   action_type: 'profile_viewed',
        //   target_user_id: parseInt(id)
        // });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.DETAILS.SUCCESS,
            data: user,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserProfileController.createProfile = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const userProfileData = Object.assign(Object.assign({}, request.body), { user_id: userId });
        yield _a.userProfileService.createUserProfile(userProfileData);
        // let user = await this.userService.getUserById(userId);
        // const tokens = await this.tokenService.generateAuthTokens(user);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.REGISTER.SUCCESS,
            data: null,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = UserProfileController;
