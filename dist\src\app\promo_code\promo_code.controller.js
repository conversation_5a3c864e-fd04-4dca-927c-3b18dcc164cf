"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const response_1 = __importStar(require("../../utils/response"));
const promo_code_service_1 = __importDefault(require("./promo_code.service"));
const subscription_plans_mode_1 = __importDefault(require("../../database/models/subscription_plans.mode"));
class PromoCodeController {
    constructor() { }
}
_a = PromoCodeController;
PromoCodeController.promoCodeService = promo_code_service_1.default;
/**
 * Validate a promo code
 */
PromoCodeController.validatePromoCode = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c, _d, _e, _f;
    try {
        const { code, plan_id } = request.body;
        const user_id = request.decoded;
        // Get plan details to validate promo code
        const plan = yield subscription_plans_mode_1.default.findByPk(plan_id);
        if (!plan || !plan.is_active) {
            return (0, response_1.default)(response, {
                statusCode: http_status_1.default.NOT_FOUND,
                message: "Subscription plan not found or inactive"
            });
        }
        const result = yield _a.promoCodeService.validatePromoCode({
            code,
            user_id,
            plan_id,
            plan_price: plan.price
        });
        if (!result.isValid) {
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.BAD_REQUEST,
                message: result.message || "Invalid promo code",
                data: { isValid: false }
            });
        }
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Promo code is valid",
            data: {
                isValid: true,
                promoCode: {
                    id: (_b = result.promoCode) === null || _b === void 0 ? void 0 : _b.id,
                    code: (_c = result.promoCode) === null || _c === void 0 ? void 0 : _c.code,
                    description: (_d = result.promoCode) === null || _d === void 0 ? void 0 : _d.description,
                    discount_type: (_e = result.promoCode) === null || _e === void 0 ? void 0 : _e.discount_type,
                    discount_value: (_f = result.promoCode) === null || _f === void 0 ? void 0 : _f.discount_value
                },
                originalAmount: plan.price,
                discountAmount: result.discountAmount,
                finalAmount: result.finalAmount
            }
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Get user's promo code usage history
 */
PromoCodeController.getUserPromoCodeUsage = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user_id = request.decoded;
        const usageHistory = yield _a.promoCodeService.getUserPromoCodeUsage(user_id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Promo code usage history retrieved successfully",
            data: usageHistory
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Get promo code by code (for admin or public info)
 */
PromoCodeController.getPromoCodeByCode = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { code } = request.params;
        const promoCode = yield _a.promoCodeService.getPromoCodeByCode(code);
        if (!promoCode) {
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.NOT_FOUND,
                message: "Promo code not found",
                data: null
            });
        }
        // Return limited information for security
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Promo code found",
            data: {
                code: promoCode.code,
                description: promoCode.description,
                discount_type: promoCode.discount_type,
                discount_value: promoCode.discount_value,
                minimum_purchase_amount: promoCode.minimum_purchase_amount,
                maximum_discount_amount: promoCode.maximum_discount_amount,
                expiry_date: promoCode.expiry_date,
                is_active: promoCode.is_active
            }
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = PromoCodeController;
