"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const response_1 = __importStar(require("../../utils/response"));
const khalti_service_1 = __importDefault(require("./khalti.service"));
class KhaltiController {
    constructor() { }
}
_a = KhaltiController;
KhaltiController.khaltiService = khalti_service_1.default;
// Initiate payment
KhaltiController.initiatePayment = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { plan_id, amount, mobile, product_identity, product_name, product_url, return_url, website_url } = request.body;
        const user_id = request.decoded;
        const result = yield _a.khaltiService.initiatePayment({
            user_id,
            plan_id,
            amount,
            mobile,
            product_identity,
            product_name,
            product_url,
            return_url,
            website_url
        });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Payment initiated successfully",
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Verify payment
KhaltiController.verifyPayment = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { pidx } = request.body;
        const user_id = request.decoded;
        const result = yield _a.khaltiService.verifyPayment(pidx, user_id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Payment verification completed",
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Handle webhook
KhaltiController.handleWebhook = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const webhookData = request.body;
        const signature = request.headers['khalti-signature'];
        const result = yield _a.khaltiService.processWebhook(webhookData, signature);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Webhook processed successfully",
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Get payment by ID
KhaltiController.getPaymentById = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { payment_id } = request.params;
        const user_id = request.decoded;
        const payment = yield _a.khaltiService.getPaymentById(parseInt(payment_id), user_id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Payment details retrieved successfully",
            data: payment,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Get user payments
KhaltiController.getUserPayments = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, status } = request.query;
        const user_id = request.decoded.id;
        const result = yield _a.khaltiService.getUserPayments(user_id, {
            page: parseInt(page),
            limit: parseInt(limit),
            status: status,
        });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "User payments retrieved successfully",
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Get all transactions (Admin)
KhaltiController.getTransactions = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, status, search } = request.query;
        const result = yield _a.khaltiService.getTransactions({
            page: parseInt(page),
            limit: parseInt(limit),
            status: status,
            search: search,
        });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Transactions retrieved successfully",
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Get payment analytics (Admin)
KhaltiController.getAnalytics = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { start_date, end_date, user_id } = request.query;
        const result = yield _a.khaltiService.getAnalytics({
            start_date: start_date,
            end_date: end_date,
            user_id: user_id ? parseInt(user_id) : undefined,
        });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Payment analytics retrieved successfully",
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Check payment status
KhaltiController.checkPaymentStatus = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { pidx } = request.params;
        const user_id = request.decoded.id;
        const result = yield _a.khaltiService.verifyPayment(pidx, user_id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Payment status checked successfully",
            data: {
                status: result.local_status,
                khalti_status: result.status,
                transaction_id: result.transaction_id,
                payment_id: result.payment_id,
            },
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Get payment configuration (for frontend)
KhaltiController.getPaymentConfig = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const config = {
            public_key: process.env.KHALTI_PUBLIC_KEY,
            return_url: `${process.env.FRONTEND_URL}/payment/khalti/callback`,
            website_url: process.env.FRONTEND_URL,
            currency: "NPR",
            supported_payment_methods: ["khalti", "ebanking", "mobile_banking", "connect_ips", "sct"]
        };
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Payment configuration retrieved successfully",
            data: config,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = KhaltiController;
