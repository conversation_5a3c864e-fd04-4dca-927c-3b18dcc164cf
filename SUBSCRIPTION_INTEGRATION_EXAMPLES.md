# Subscription System Integration Examples

## How to Integrate Subscription Checks in Your Existing Controllers

### 1. Profile Viewing with Usage Tracking

```typescript
// In your user profile controller
import { authWithSubscription, checkActionPermission } from '../middlewares/authWithSubscription';
import SubscriptionService from '../app/subscription/subscription.service';

// Route with automatic permission checking
router.get('/profile/:userId', 
  checkActionPermission('profile_viewed'), 
  async (req, res) => {
    try {
      const { userId } = req.params;
      const currentUserId = req.decoded.id;

      // Get user profile data
      const userProfile = await getUserProfile(userId);

      // Track the usage (this will be automatically handled by the middleware)
      await SubscriptionService.trackUsage({
        user_id: currentUserId,
        action_type: 'profile_viewed',
        target_user_id: parseInt(userId)
      });

      return sentResponse(res, {
        statusCode: 200,
        message: "Profile retrieved successfully",
        data: userProfile
      });
    } catch (error) {
      return errorResponse(res, error);
    }
  }
);
```

### 2. Interest/Like Feature with Subscription Check

```typescript
// Send interest with subscription validation
router.post('/send-interest', 
  checkActionPermission('interest_sent'),
  async (req, res) => {
    try {
      const { target_user_id } = req.body;
      const user_id = req.decoded.id;

      // The middleware already checked permissions, so we can proceed
      
      // Your existing interest logic here
      const interest = await createInterest(user_id, target_user_id);

      // Track the usage
      await SubscriptionService.trackUsage({
        user_id,
        action_type: 'interest_sent',
        target_user_id
      });

      return sentResponse(res, {
        statusCode: 200,
        message: "Interest sent successfully",
        data: interest
      });
    } catch (error) {
      return errorResponse(res, error);
    }
  }
);
```

### 3. Contact Details Viewing

```typescript
// View contact details with usage tracking
router.get('/contact/:userId', 
  checkActionPermission('contact_viewed'),
  async (req, res) => {
    try {
      const { userId } = req.params;
      const currentUserId = req.decoded.id;

      // Get contact details
      const contactDetails = await getContactDetails(userId);

      // Track usage
      await SubscriptionService.trackUsage({
        user_id: currentUserId,
        action_type: 'contact_viewed',
        target_user_id: parseInt(userId)
      });

      return sentResponse(res, {
        statusCode: 200,
        message: "Contact details retrieved successfully",
        data: contactDetails
      });
    } catch (error) {
      return errorResponse(res, error);
    }
  }
);
```

### 4. Chat Initiation with Subscription Check

```typescript
// Start chat with subscription validation
router.post('/chat/initiate', 
  checkActionPermission('chat_initiated'),
  async (req, res) => {
    try {
      const { target_user_id } = req.body;
      const user_id = req.decoded.id;

      // Create chat
      const chat = await createChat(user_id, target_user_id);

      // Track usage
      await SubscriptionService.trackUsage({
        user_id,
        action_type: 'chat_initiated',
        target_user_id
      });

      return sentResponse(res, {
        statusCode: 200,
        message: "Chat initiated successfully",
        data: chat
      });
    } catch (error) {
      return errorResponse(res, error);
    }
  }
);
```

### 5. Manual Subscription Check (Alternative Approach)

```typescript
// Manual subscription checking in controller
router.get('/premium-feature', authWithSubscription, async (req, res) => {
  try {
    const user_id = req.decoded.id;

    // Check subscription status
    const subscriptionStatus = await SubscriptionService.getActiveSubscription(user_id);
    
    if (!subscriptionStatus.isActive) {
      return errorResponse(res, new ApiError(403, "Premium subscription required"));
    }

    // Check if user can perform specific action
    const canPerform = await SubscriptionService.canPerformAction(
      user_id, 
      'profile_viewed'
    );

    if (!canPerform.canPerform) {
      return errorResponse(res, new ApiError(403, canPerform.message));
    }

    // Your premium feature logic here
    const premiumData = await getPremiumFeatureData();

    return sentResponse(res, {
      statusCode: 200,
      message: "Premium feature accessed successfully",
      data: {
        ...premiumData,
        subscription: subscriptionStatus,
        remainingUsage: subscriptionStatus.remainingUsage
      }
    });
  } catch (error) {
    return errorResponse(res, error);
  }
});
```

### 6. Frontend Integration Examples

#### Check Subscription Status on Login
```typescript
// After successful login
const checkSubscriptionStatus = async () => {
  try {
    const response = await fetch('/subscription/status', {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    
    const data = await response.json();
    
    if (!data.data.isActive) {
      // Show subscription upgrade prompt
      showSubscriptionUpgradeModal(data.data.message);
    } else {
      // Show remaining usage
      updateUsageDisplay(data.data.remainingUsage);
    }
  } catch (error) {
    console.error('Subscription check failed:', error);
  }
};
```

#### Check Before Performing Action
```typescript
// Before sending interest
const sendInterest = async (targetUserId) => {
  try {
    // Check if action is allowed
    const checkResponse = await fetch(
      `/subscription/can-perform?action_type=interest_sent&target_user_id=${targetUserId}`,
      {
        headers: { 'Authorization': `Bearer ${userToken}` }
      }
    );
    
    const checkData = await checkResponse.json();
    
    if (!checkData.data.canPerform) {
      if (checkData.data.requiresSubscription) {
        showSubscriptionUpgradeModal();
      } else if (checkData.data.alreadyPerformed) {
        showMessage("You have already sent interest to this user");
      } else {
        showMessage(checkData.data.message);
      }
      return;
    }

    // Proceed with sending interest
    const response = await fetch('/send-interest', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ target_user_id: targetUserId })
    });

    if (response.ok) {
      showMessage("Interest sent successfully!");
      updateUsageDisplay(); // Refresh usage display
    }
  } catch (error) {
    console.error('Send interest failed:', error);
  }
};
```

### 7. Admin Dashboard Integration

```typescript
// Admin route to check expired subscriptions
router.get('/admin/subscriptions/expired', auth, async (req, res) => {
  try {
    // Manual trigger for testing
    const result = await SubscriptionService.checkAndUpdateExpiredSubscriptions();
    
    return sentResponse(res, {
      statusCode: 200,
      message: "Expired subscriptions check completed",
      data: result
    });
  } catch (error) {
    return errorResponse(res, error);
  }
});

// Get subscription analytics
router.get('/admin/subscriptions/analytics', auth, async (req, res) => {
  try {
    const analytics = await getSubscriptionAnalytics();
    
    return sentResponse(res, {
      statusCode: 200,
      message: "Subscription analytics retrieved",
      data: analytics
    });
  } catch (error) {
    return errorResponse(res, error);
  }
});
```

## Key Integration Points

### 1. Replace Basic Auth with Subscription-Aware Auth
```typescript
// Old way
router.get('/profile', auth, controller);

// New way
router.get('/profile', authWithSubscription, controller);
// Now req.subscription contains subscription info
```

### 2. Add Permission Checks for Premium Features
```typescript
// For actions that consume subscription limits
router.post('/premium-action', checkActionPermission('action_type'), controller);
```

### 3. Track Usage in Existing Features
```typescript
// Add this to existing controllers where users consume subscription features
await SubscriptionService.trackUsage({
  user_id,
  action_type: 'interest_sent', // or other action types
  target_user_id
});
```

### 4. Handle Subscription Responses in Frontend
```typescript
// Always check for subscription warnings in API responses
if (response.data.subscriptionWarning) {
  handleSubscriptionWarning(response.data.subscriptionWarning);
}
```

This integration approach ensures that your existing features work seamlessly with the new subscription system while providing clear feedback to users about their subscription status and usage limits.
