
import {
  AllowNull,
  AutoIncrement,
  Column,
  DataType,
  <PERSON><PERSON>ult,
  Model,
  PrimaryKey,
  Table,
  ForeignKey,
  BelongsTo
} from "sequelize-typescript";
import User from "./user.model";

export interface UserReportI {
  id: number;
  reporter_id: number;
  reported_user_id: number;
  reason: "Harassment" | "Fake Profile" | "Inappropriate Content" | "Other";
  details: string;
  status: "Pending" | "Reviewed" | "Resolved" | "Dismissed";
  admin_notes: string;
  admin_action: "None" | "Warning" | "Suspension" | "Account Deletion";
  createdAt?: Date;
  updatedAt?: Date;
}

@Table({
  tableName: "user_reports",
  timestamps: true,
})
class UserReport extends Model<UserReportI> implements UserReportI {
  @PrimaryKey
  @AutoIncrement
  @Column
  id: number;

  @AllowNull(false)
  @ForeignKey(() => User)
  @Column(DataType.INTEGER)
  reporter_id: number;

  @AllowNull(false)
  @ForeignKey(() => User)
  @Column(DataType.INTEGER)
  reported_user_id: number;

  @AllowNull(false)
  @Column(DataType.ENUM("Harassment", "Fake Profile", "Inappropriate Content", "Other"))
  reason: "Harassment" | "Fake Profile" | "Inappropriate Content" | "Other";

  @AllowNull(true)
  @Column(DataType.TEXT)
  details: string;

  @AllowNull(false)
  @Default("Pending")
  @Column(DataType.ENUM("Pending", "Reviewed", "Resolved", "Dismissed"))
  status: "Pending" | "Reviewed" | "Resolved" | "Dismissed";

  @AllowNull(true)
  @Column(DataType.TEXT)
  admin_notes: string;

  @AllowNull(false)
  @Default("None")
  @Column(DataType.ENUM("None", "Warning", "Suspension", "Account Deletion"))
  admin_action: "None" | "Warning" | "Suspension" | "Account Deletion";

  @BelongsTo(() => User, { foreignKey: 'reporter_id', as: 'reporter' })
  reporter: User;

  @BelongsTo(() => User, { foreignKey: 'reported_user_id', as: 'reportedUser' })
  reportedUser: User;
}

export default UserReport;