"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middlewares/auth");
const chat_controller_1 = __importDefault(require("../app/chat/chat.controller"));
const message_controller_1 = __importDefault(require("../app/message/message.controller"));
const router = express_1.default.Router();
// Apply auth middleware to all chat routes
router.use(auth_1.auth);
// Chat routes
router.get("/", auth_1.auth, chat_controller_1.default.getUserChats);
router.post("/", chat_controller_1.default.createChat);
router.get("/:id", auth_1.auth, chat_controller_1.default.getChatById);
router.get("/:id/messages", chat_controller_1.default.getChatMessages);
router.put("/:id/auto-delete", chat_controller_1.default.setAutoDeleteDays);
router.delete("/:id", chat_controller_1.default.deleteChat);
// Message-related routes that operate on chats
router.put("/:id/read-all", message_controller_1.default.markAllAsRead);
exports.default = router;
