"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const khalti_controller_1 = __importDefault(require("../app/khalti/khalti.controller"));
const auth_1 = require("../middlewares/auth");
const middleware_1 = require("../middlewares/middleware");
const khalti_validation_1 = require("../validations/khalti.validation");
const router = express_1.default.Router();
// Public routes
router.get("/config", khalti_controller_1.default.getPaymentConfig);
// router.post("/webhook", validate(webhookValidation), KhaltiController.handleWebhook);
// Protected routes (require authentication)
router.use(auth_1.auth);
// Payment operations
router.post("/initiate", (0, middleware_1.validate)(khalti_validation_1.initiatePaymentValidation), khalti_controller_1.default.initiatePayment);
router.post("/verify", (0, middleware_1.validate)(khalti_validation_1.verifyPaymentValidation), khalti_controller_1.default.verifyPayment);
router.get("/status/:pidx", (0, middleware_1.validate)(khalti_validation_1.checkPaymentStatusValidation), khalti_controller_1.default.checkPaymentStatus);
// User payment management
router.get("/payments", (0, middleware_1.validate)(khalti_validation_1.getUserPaymentsValidation), khalti_controller_1.default.getUserPayments);
router.get("/payments/:payment_id", (0, middleware_1.validate)(khalti_validation_1.getPaymentByIdValidation), khalti_controller_1.default.getPaymentById);
// Admin routes (you may want to add admin role check middleware)
router.get("/admin/transactions", (0, middleware_1.validate)(khalti_validation_1.getTransactionsValidation), khalti_controller_1.default.getTransactions);
router.get("/admin/analytics", (0, middleware_1.validate)(khalti_validation_1.getAnalyticsValidation), khalti_controller_1.default.getAnalytics);
exports.default = router;
