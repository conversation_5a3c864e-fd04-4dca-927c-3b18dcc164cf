"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.emptyValidation = exports.phoneValidation = exports.passwordValidation = void 0;
const httpMessages = require("../config/httpMessages");
const passwordValidation = (value, helpers) => {
    if ((value === null || value === void 0 ? void 0 : value.length) < 8) {
        return helpers.message(httpMessages.REGISTER.SHORT_PASSWORD);
    }
    if (!value.match(/\d/) || !value.match(/[a-zA-Z]/)) {
        return helpers.message("password must contain at least 1 letter and 1 number!");
    }
    return value;
};
exports.passwordValidation = passwordValidation;
const phoneValidation = (value, helpers) => {
    if (!value.match(/^\([0-9]{3}\)[0-9]{3}-[0-9]{4}$/)) {
        return helpers.message(httpMessages.REGISTER.INVALID_PHONE);
    }
    return value;
};
exports.phoneValidation = phoneValidation;
const emptyValidation = (value, helpers) => {
    var _a, _b, _c;
    console.log("helpers: ", (_a = helpers === null || helpers === void 0 ? void 0 : helpers.state) === null || _a === void 0 ? void 0 : _a.path[1]);
    if (value === "null" || value === "undefined") {
        let message = `The "${((_b = helpers === null || helpers === void 0 ? void 0 : helpers.state) === null || _b === void 0 ? void 0 : _b.path[1]) ? (_c = helpers === null || helpers === void 0 ? void 0 : helpers.state) === null || _c === void 0 ? void 0 : _c.path[1] : ""}" parameter cannot be "null" or "undefined" as a string. `;
        return helpers.message(message);
    }
    return value;
};
exports.emptyValidation = emptyValidation;
