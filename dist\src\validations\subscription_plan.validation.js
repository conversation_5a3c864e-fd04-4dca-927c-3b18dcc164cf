"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.subscriptionPlanValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.subscriptionPlanValidation = {
    body: joi_1.default.object().keys({
        name: joi_1.default.string().trim().required().messages({
            "string.base": "Name must be a string.",
            "string.empty": "Name is required.",
            "any.required": "Name is required.",
        }),
        price: joi_1.default.number().precision(2).required().messages({
            "number.base": "Price must be a number.",
            "number.positive": "Price must be a positive number.",
            "any.required": "Price is required.",
        }),
        duration_days: joi_1.default.number().integer().positive().required().messages({
            "number.base": "Duration days must be a number.",
            "number.integer": "Duration days must be an integer.",
            "number.positive": "Duration days must be a positive number.",
            "any.required": "Duration days is required.",
        }),
        interest: joi_1.default.boolean().required().messages({
            "boolean.base": "Interest must be a boolean.",
            "any.required": "Interest is required.",
        }),
        interest_limit: joi_1.default.number().integer().min(0).allow(null).messages({
            "number.base": "Interest limit must be a number.",
            "number.integer": "Interest limit must be an integer.",
            "number.min": "Interest limit must be a non-negative number.",
        }),
        contact: joi_1.default.boolean().required().messages({
            "boolean.base": "Contact must be a boolean.",
            "any.required": "Contact is required.",
        }),
        contact_limit: joi_1.default.number().integer().min(0).allow(null).messages({
            "number.base": "Contact limit must be a number.",
            "number.integer": "Contact limit must be an integer.",
            "number.min": "Contact limit must be a non-negative number.",
        }),
        view_profiles: joi_1.default.boolean().required().messages({
            "boolean.base": "View profiles must be a boolean.",
            "any.required": "View profiles is required.",
        }),
        view_profiles_limit: joi_1.default.number().integer().min(0).allow(null).messages({
            "number.base": "View profiles limit must be a number.",
            "number.integer": "View profiles limit must be an integer.",
            "number.min": "View profiles limit must be a non-negative number.",
        }),
        chat: joi_1.default.boolean().required().messages({
            "boolean.base": "Chat must be a boolean.",
            "any.required": "Chat is required.",
        }),
        chat_limit: joi_1.default.number().integer().min(0).allow(null).messages({
            "number.base": "Chat limit must be a number.",
            "number.integer": "Chat limit must be an integer.",
            "number.min": "Chat limit must be a non-negative number.",
        }),
        has_verified_badge: joi_1.default.boolean().default(false).messages({
            "boolean.base": "Has verified badge must be a boolean.",
        }),
        is_personalized_matchmaking: joi_1.default.boolean().default(false).messages({
            "boolean.base": "Is personalized matchmaking must be a boolean.",
        }),
        is_active: joi_1.default.boolean().default(true).messages({
            "boolean.base": "Is active must be a boolean.",
        }),
    }),
};
