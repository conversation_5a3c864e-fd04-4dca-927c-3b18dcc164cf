"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const admin_promo_code_controller_1 = __importDefault(require("../app/admin/admin_promo_code.controller"));
const auth_1 = require("../middlewares/auth");
const middleware_1 = require("../middlewares/middleware");
const promo_code_validation_1 = require("../validations/promo_code.validation");
const router = express_1.default.Router();
// All routes require authentication (admin only)
router.use(auth_1.auth);
// Create promo code
router.post("/", (0, middleware_1.validate)(promo_code_validation_1.createPromoCodeValidation), admin_promo_code_controller_1.default.createPromoCode);
// Get all promo codes with pagination
router.get("/", admin_promo_code_controller_1.default.getPromoCodes);
// Get promo code by ID
router.get("/:id", (0, middleware_1.validate)(promo_code_validation_1.getPromoCodeValidation), admin_promo_code_controller_1.default.getPromoCodeById);
// Update promo code
router.put("/:id", admin_promo_code_controller_1.default.updatePromoCode);
// Delete promo code
router.delete("/:id", (0, middleware_1.validate)(promo_code_validation_1.deletePromoCodeValidation), admin_promo_code_controller_1.default.deletePromoCode);
// Get promo code statistics
router.get("/:id/stats", (0, middleware_1.validate)(promo_code_validation_1.getPromoCodeValidation), admin_promo_code_controller_1.default.getPromoCodeStats);
exports.default = router;
