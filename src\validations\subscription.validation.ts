import Jo<PERSON> from "joi";

export const purchaseSubscriptionValidation = {
  body: Joi.object().keys({
    plan_id: Joi.number().integer().positive().required().messages({
      "number.base": "Plan ID must be a number.",
      "number.integer": "Plan ID must be an integer.",
      "number.positive": "Plan ID must be a positive number.",
      "any.required": "Plan ID is required.",
    }),
    payment_id: Joi.number().integer().positive().optional().messages({
      "number.base": "Payment ID must be a number.",
      "number.integer": "Payment ID must be an integer.",
      "number.positive": "Payment ID must be a positive number.",
    }),
  }),
};

export const trackUsageValidation = {
  body: Joi.object().keys({
    action_type: Joi.string().valid("interest_sent", "contact_viewed", "profile_viewed", "chat_initiated").required().messages({
      "string.base": "Action type must be a string.",
      "any.only": "Action type must be one of: interest_sent, contact_viewed, profile_viewed, chat_initiated.",
      "any.required": "Action type is required.",
    }),
    target_user_id: Joi.number().integer().positive().required().messages({
      "number.base": "Target user ID must be a number.",
      "number.integer": "Target user ID must be an integer.",
      "number.positive": "Target user ID must be a positive number.",
      "any.required": "Target user ID is required.",
    }),
  }),
};

export const canPerformActionValidation = {
  query: Joi.object().keys({
    action_type: Joi.string().valid("interest_sent", "contact_viewed", "profile_viewed", "chat_initiated").required().messages({
      "string.base": "Action type must be a string.",
      "any.only": "Action type must be one of: interest_sent, contact_viewed, profile_viewed, chat_initiated.",
      "any.required": "Action type is required.",
    }),
    target_user_id: Joi.number().integer().positive().optional().messages({
      "number.base": "Target user ID must be a number.",
      "number.integer": "Target user ID must be an integer.",
      "number.positive": "Target user ID must be a positive number.",
    }),
  }),
};

export const getSubscriptionByIdValidation = {
  params: Joi.object().keys({
    subscription_id: Joi.number().integer().positive().required().messages({
      "number.base": "Subscription ID must be a number.",
      "number.integer": "Subscription ID must be an integer.",
      "number.positive": "Subscription ID must be a positive number.",
      "any.required": "Subscription ID is required.",
    }),
  }),
};

export const cancelSubscriptionValidation = {
  body: Joi.object().keys({
    reason: Joi.string().max(500).optional().messages({
      "string.base": "Reason must be a string.",
      "string.max": "Reason cannot exceed 500 characters.",
    }),
  }),
};
