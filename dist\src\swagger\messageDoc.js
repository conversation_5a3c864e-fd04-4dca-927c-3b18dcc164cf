"use strict";
/**
 * @swagger
 * components:
 *   schemas:
 *     Message:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Unique identifier for the message
 *           example: 42
 *         chat_id:
 *           type: integer
 *           description: ID of the chat this message belongs to
 *           example: 1
 *         sender_id:
 *           type: integer
 *           description: ID of the user who sent the message
 *           example: 101
 *         receiver_id:
 *           type: integer
 *           description: ID of the user who received the message
 *           example: 102
 *         content:
 *           type: string
 *           description: Encrypted message content
 *           example: "U2FsdGVkX1+A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0="
 *         is_encrypted:
 *           type: boolean
 *           description: Whether the message content is encrypted
 *           example: true
 *         is_delivered:
 *           type: boolean
 *           description: Whether the message has been delivered to the recipient
 *           example: true
 *         is_read:
 *           type: boolean
 *           description: Whether the message has been read by the recipient
 *           example: false
 *         delivered_at:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           description: Timestamp when the message was delivered
 *           example: "2023-06-15T14:30:05.000Z"
 *         read_at:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           description: Timestamp when the message was read
 *           example: null
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the message was created
 *           example: "2023-06-15T14:30:00.000Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the message was last updated
 *           example: "2023-06-15T14:30:05.000Z"
 *
 *     MessagePagination:
 *       type: object
 *       properties:
 *         messages:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Message'
 *         total:
 *           type: integer
 *           description: Total number of messages
 *           example: 42
 *         totalPages:
 *           type: integer
 *           description: Total number of pages
 *           example: 3
 *         currentPage:
 *           type: integer
 *           description: Current page number
 *           example: 1
 */
/**
 * @swagger
 * /chats/{id}/messages:
 *   get:
 *     tags:
 *       - Messages
 *     summary: Get messages for a chat
 *     description: Retrieve messages for a specific chat with pagination.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Chat ID
 *         example: 1
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *         example: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 100
 *         description: Number of messages per page
 *         example: 20
 *     responses:
 *       200:
 *         description: Messages retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Messages retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/MessagePagination'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User does not have permission to access this chat
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /messages:
 *   post:
 *     tags:
 *       - Messages
 *     summary: Send a new message
 *     description: Send a new message to another user via REST API (alternative to Socket.io).
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - chatId
 *               - receiverId
 *               - content
 *             properties:
 *               chatId:
 *                 type: integer
 *                 description: ID of the chat
 *                 example: 1
 *               receiverId:
 *                 type: integer
 *                 description: ID of the message recipient
 *                 example: 102
 *               content:
 *                 type: string
 *                 description: Message content (will be encrypted)
 *                 example: "Hello, how are you?"
 *     responses:
 *       201:
 *         description: Message sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: "Message sent successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Message'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User does not have permission to send messages in this chat
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /messages/{id}/delivered:
 *   put:
 *     tags:
 *       - Messages
 *     summary: Mark message as delivered
 *     description: Mark a message as delivered via REST API (alternative to Socket.io).
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Message ID
 *         example: 42
 *     responses:
 *       200:
 *         description: Message marked as delivered
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Message marked as delivered"
 *                 data:
 *                   $ref: '#/components/schemas/Message'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       404:
 *         description: Message not found
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /messages/{id}/read:
 *   put:
 *     tags:
 *       - Messages
 *     summary: Mark message as read
 *     description: Mark a message as read via REST API (alternative to Socket.io).
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Message ID
 *         example: 42
 *     responses:
 *       200:
 *         description: Message marked as read
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Message marked as read"
 *                 data:
 *                   $ref: '#/components/schemas/Message'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       404:
 *         description: Message not found
 *       500:
 *         description: Internal server error
 */
