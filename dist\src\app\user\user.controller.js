"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const response_1 = __importStar(require("../../utils/response"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
const user_service_1 = __importDefault(require("./user.service"));
const token_service_1 = __importDefault(require("../../common/services/token.service"));
const moment_1 = __importDefault(require("moment"));
class UserController {
    constructor() { }
}
_a = UserController;
UserController.userService = user_service_1.default;
UserController.tokenService = token_service_1.default;
UserController.login = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, password } = request.body;
        const user = yield _a.userService.loginUserWithEmailAndPassword(email, password);
        if (!(user === null || user === void 0 ? void 0 : user.hotel_users.length) && user.role_id !== 1) {
            throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.NOT_ASSIGND_HOTEL);
        }
        const tokens = yield _a.tokenService.generateAuthTokens(user);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.LOGIN.SUCCESS,
            data: Object.assign(Object.assign({}, user), { token: tokens }),
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserController.getAll = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, status } = request.query;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search ? search : "",
            status: status ? status : undefined,
        };
        const users = yield _a.userService.getUsers(option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.SUCCESS,
            data: users,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserController.create = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userData = Object.assign({}, request.body);
        const user = yield _a.userService.createUser(userData);
        const data = user;
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.REGISTER.SUCCESS,
            data: user,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserController.showById = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId } = request.params;
        if (userId === "me") {
            let decoded = request.decoded;
            if (!decoded) {
                throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED);
            }
            // Set the id to the decoded id
            const user = yield _a.userService.getUserById(decoded);
            if (!user) {
                throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED);
            }
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.OK,
                message: httpMessages_1.default.USER.DETAILS.SUCCESS,
                data: user,
            });
        }
        const user = yield _a.userService.getUserById(parseInt(userId));
        if (!user) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER.NOT_FOUND);
        }
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.DETAILS.SUCCESS,
            data: user,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserController.update = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = parseInt(request.params.userId, 10);
        const userData = Object.assign({}, request.body);
        const user = yield _a.userService.updateUserById(userId, userData);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.UPDATE_SUCCESS,
            data: user,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserController.delete = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = parseInt(request.params.userId, 10);
        yield _a.userService.deleteUserById(userId);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.DELETE,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserController.getProfile = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.user.id;
        const user = yield _a.userService.getUserById(userId);
        const data = { user };
        return response.status(http_status_1.default.CREATED).send({
            success: true,
            message: httpMessages_1.default.USER.PROFILE.SUCCESS,
            data
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserController.updateProfile = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userProfile = request.body;
        const userId = request.user.id;
        if (userProfile.dateOfBirth) {
            const formattedDate = new Date(userProfile.dateOfBirth);
            const ageInDays = (0, moment_1.default)().diff(formattedDate, "days");
            const minAge = 25 * 365;
            const maxAge = 65 * 365;
            if (ageInDays < minAge || ageInDays > maxAge) {
                throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.USER.PROFILE.AGE_RANGE_FAILURE);
            }
            userProfile.dateOfBirth = new Date(userProfile.dateOfBirth);
        }
        const profile = yield _a.userService.updateUserByProfile(userId, userProfile);
        const data = { profile };
        return response.status(http_status_1.default.CREATED).send({
            success: true,
            message: httpMessages_1.default.USER.PROFILE.UPDATE_SUCCESS,
            data
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserController.updatePassword = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const { currentPassword, newPassword } = request.body;
        const profile = yield _a.userService.updatePassword(userId, currentPassword, newPassword);
        const data = { profile };
        return response.status(http_status_1.default.CREATED).send({
            success: true,
            message: httpMessages_1.default.USER.PROFILE.PASSWORD_UPDATE_SUCCESS,
            data
        });
    }
    catch (error) {
        console.log('error: ', error);
        return (0, response_1.default)(response, error);
    }
}));
UserController.changeEmail = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const { email } = request.body;
        const profile = yield _a.userService.changeEmail(userId, email);
        const data = { profile };
        return response.status(http_status_1.default.CREATED).send({
            success: true,
            message: httpMessages_1.default.USER.PROFILE.UPDATE_SUCCESS,
            data
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserController.completeProfile = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const userProfileData = Object.assign(Object.assign({}, request.body), { user_id: userId });
        const profile = yield _a.userService.completeProfile(userProfileData);
        const data = { profile };
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.PROFILE.UPDATE_SUCCESS,
            data: null,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = UserController;
