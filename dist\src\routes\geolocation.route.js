"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const geolocation_controller_1 = __importDefault(require("../app/geoLocation/geolocation.controller"));
const auth_1 = require("../middlewares/auth");
const middleware_1 = require("../middlewares/middleware");
const geolocation_validation_1 = require("../validations/geolocation.validation");
const router = express_1.default.Router();
router.get("", auth_1.auth, geolocation_controller_1.default.getAll);
router.post("", auth_1.auth, (0, middleware_1.validate)(geolocation_validation_1.geolocationValidation), geolocation_controller_1.default.create);
router.get("/countries", geolocation_controller_1.default.getCountries);
router.get("/cities/:countryId", geolocation_controller_1.default.getCities);
router.get("/:id", auth_1.auth, geolocation_controller_1.default.showById);
router.put("/:id", auth_1.auth, geolocation_controller_1.default.update);
router.delete("/:id", auth_1.auth, geolocation_controller_1.default.delete);
exports.default = router;
