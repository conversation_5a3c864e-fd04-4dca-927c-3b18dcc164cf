import express from "express";
import HelpCategoryController from "../app/help/help_category/help_category.controller";
import HelpQuestionController from "../app/help/help_question/help_question.controller";
import { auth } from "../middlewares/auth";
import { validate } from "../middlewares/middleware";
import { createHelpCategoryValidation, createHelpQuestionValidation } from "../validations/help.validation";

const router = express.Router();

router.get("/categories", HelpCategoryController.getAll);
router.post("/categories", auth, validate(createHelpCategoryValidation), HelpCategoryController.create);
router.get("/categories/:id",  HelpCategoryController.showById);
router.put("/categories/:id", auth, validate(createHelpCategoryValidation), HelpCategoryController.update);
router.delete("/categories/:id", auth, HelpCategoryController.delete);

router.get("/questions", HelpQuestionController.getAll);
router.post("/questions", auth, validate(createHelpQuestionValidation), HelpQuestionController.create);
router.get("/questions/:id", HelpQuestionController.showById);
router.put("/questions/:id", auth, validate(createHelpQuestionValidation), HelpQuestionController.update);
router.delete("/questions/:id", auth, HelpQuestionController.delete);

export default router;

