import express from "express";
import PaypalController from "../app/paypal/paypal.controller";
import { auth } from "../middlewares/auth";
import { validate } from "../middlewares/middleware";
import {
    createOrderValidation,
    capturePaymentValidation,
    getPaymentDetailsValidation,
    getUserPaymentsValidation,
    getPaymentAnalyticsValidation,
    refundPaymentValidation,
    webhookValidation
} from "../validations/paypal.validation";

const router = express.Router();

// Public webhook endpoint (no auth required)
router.post("/webhook", PaypalController.handleWebhook);

// Success and cancel handlers (no auth required)
router.get("/success", PaypalController.handleSuccess);
router.get("/cancel", PaypalController.handleCancel);

// Protected routes (require authentication)
router.post("/create-order", auth, validate(createOrderValidation), PaypalController.createOrder);
router.post("/capture/:order_id", auth, validate(capturePaymentValidation), PaypalController.capturePayment);
router.get("/payment/:payment_id", auth, validate(getPaymentDetailsValidation), PaypalController.getPaymentDetails);
router.get("/payments", auth, validate(getUserPaymentsValidation), PaypalController.getUserPayments);

// Admin routes (require authentication - you may want to add admin role check)
router.get("/transactions", auth, PaypalController.getTransactions);
router.get("/analytics", auth, validate(getPaymentAnalyticsValidation), PaypalController.getPaymentAnalytics);
router.post("/refund/:payment_id", auth, validate(refundPaymentValidation), PaypalController.refundPayment);

export default router;
