import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../../utils/response";
import httpMessages from "../../../config/httpMessages";
import UserNearbyService from "./user_nearby.service";

export default class UserNearbyController {
    static userNearbyService = UserNearbyService;
    constructor() { }


    static getAll = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page, limit, search } = request.query;
            const option = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
                search: search ? (search as string) : "",
            };
            const userId = request.decoded;
            const users = await this.userNearbyService.getUsersByLocation(option, userId);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Users nearby retrieved successfully',
                data: users,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

}
