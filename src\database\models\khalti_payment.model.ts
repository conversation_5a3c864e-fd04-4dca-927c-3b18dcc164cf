import {
    AllowNull,
    <PERSON>I<PERSON>rement,
    <PERSON><PERSON>s<PERSON>o,
    <PERSON>umn,
    DataType,
    <PERSON><PERSON>ult,
    Foreign<PERSON>ey,
    Model,
    PrimaryKey,
    Table,
    Index,
} from "sequelize-typescript";
import User from "./user.model";
import SubscriptionPlan from "./subscription_plans.mode";

export interface KhaltiPaymentI {
    id?: number;
    user_id: number;
    plan_id?: number;
    khalti_payment_id?: string;
    khalti_transaction_id?: string;
    khalti_order_id?: string;
    pidx?: string; // Khalti payment index
    amount: number;
    currency: string;
    status: "pending" | "completed" | "failed" | "cancelled" | "refunded" | "expired";
    payment_method: string;
    description?: string;
    khalti_response?: any;
    refund_id?: string;
    refund_amount?: number;
    refund_reason?: string;
    webhook_event_id?: string;
    mobile?: string;
    product_identity?: string;
    product_name?: string;
    product_url?: string;
    return_url?: string;
    website_url?: string;
    created_at?: Date;
    updated_at?: Date;
}

@Table({
    tableName: "khalti_payments",
    timestamps: true,
    underscored: true,
})
class KhaltiPayment extends Model<KhaltiPaymentI> implements KhaltiPaymentI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @ForeignKey(() => SubscriptionPlan)
    @AllowNull(true)
    @Column
    plan_id?: number;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    khalti_payment_id?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    khalti_transaction_id?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    khalti_order_id?: string;

    @AllowNull(true)
    @Index
    @Column(DataType.STRING(100))
    pidx?: string;

    @AllowNull(false)
    @Column(DataType.DECIMAL(10, 2))
    amount: number;

    @AllowNull(false)
    @Default("NPR")
    @Column(DataType.STRING(3))
    currency: string;

    @AllowNull(false)
    @Default("pending")
    @Column(DataType.ENUM("pending", "completed", "failed", "cancelled", "refunded", "expired"))
    status: "pending" | "completed" | "failed" | "cancelled" | "refunded" | "expired";

    @AllowNull(false)
    @Default("khalti")
    @Column(DataType.STRING(50))
    payment_method: string;

    @AllowNull(true)
    @Column(DataType.TEXT)
    description?: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    khalti_response?: any;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    refund_id?: string;

    @AllowNull(true)
    @Column(DataType.DECIMAL(10, 2))
    refund_amount?: number;

    @AllowNull(true)
    @Column(DataType.TEXT)
    refund_reason?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    webhook_event_id?: string;

    @AllowNull(true)
    @Column(DataType.STRING(20))
    mobile?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    product_identity?: string;

    @AllowNull(true)
    @Column(DataType.STRING(255))
    product_name?: string;

    @AllowNull(true)
    @Column(DataType.STRING(500))
    product_url?: string;

    @AllowNull(true)
    @Column(DataType.STRING(500))
    return_url?: string;

    @AllowNull(true)
    @Column(DataType.STRING(500))
    website_url?: string;

    // Associations
    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE" })
    user: User;

    @BelongsTo(() => SubscriptionPlan, { foreignKey: "plan_id", onDelete: "SET NULL" })
    subscriptionPlan: SubscriptionPlan;
}

export default KhaltiPayment;
