"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userSubscriptionValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.userSubscriptionValidation = {
    body: joi_1.default.object().keys({
        user_id: joi_1.default.number().integer().positive().required().messages({
            "number.base": "User ID must be a number.",
            "number.integer": "User ID must be an integer.",
            "number.positive": "User ID must be a positive number.",
            "any.required": "User ID is required.",
        }),
        plan_id: joi_1.default.number().integer().positive().required().messages({
            "number.base": "Plan ID must be a number.",
            "number.integer": "Plan ID must be an integer.",
            "number.positive": "Plan ID must be a positive number.",
            "any.required": "Plan ID is required.",
        }),
        start_date: joi_1.default.date().allow(null).messages({
            "date.base": "Start date must be a valid date.",
        }),
        end_date: joi_1.default.date().allow(null).min(joi_1.default.ref('start_date')).messages({
            "date.base": "End date must be a valid date.",
            "date.min": "End date must be after start date.",
        }),
        auto_renew: joi_1.default.boolean().allow(null).default(false).messages({
            "boolean.base": "Auto renew must be a boolean.",
        }),
        issued_at: joi_1.default.date().allow(null).messages({
            "date.base": "Issued at must be a valid date.",
        }),
        expires_at: joi_1.default.date().allow(null).min(joi_1.default.ref('issued_at')).messages({
            "date.base": "Expires at must be a valid date.",
            "date.min": "Expires at must be after issued at.",
        }),
        revoked_at: joi_1.default.date().allow(null).messages({
            "date.base": "Revoked at must be a valid date.",
        }),
        usage_limit: joi_1.default.number().integer().min(0).allow(null).messages({
            "number.base": "Usage limit must be a number.",
            "number.integer": "Usage limit must be an integer.",
            "number.min": "Usage limit must be a non-negative number.",
        }),
        usage_count: joi_1.default.number().integer().min(0).allow(null).default(0).messages({
            "number.base": "Usage count must be a number.",
            "number.integer": "Usage count must be an integer.",
            "number.min": "Usage count must be a non-negative number.",
        }),
        is_active: joi_1.default.boolean().allow(null).default(true).messages({
            "boolean.base": "Is active must be a boolean.",
        }),
        token: joi_1.default.string().allow(null, "").messages({
            "string.base": "Token must be a string.",
        }),
    }),
};
