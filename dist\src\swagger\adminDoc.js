"use strict";
/**
 * @swagger
 * /admin/login:
 *   post:
 *     tags:
 *       - Admin
 *     summary: Admin login
 *     description: Authenticate an admin user with username and password.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - password
 *             properties:
 *               username:
 *                 type: string
 *                 example: admin
 *               password:
 *                 type: string
 *                 example: Admin@123
 *     responses:
 *       200:
 *         description: Admin successfully logged in.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Login successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     first_name:
 *                       type: string
 *                       example: Ravi
 *                     last_name:
 *                       type: string
 *                       example: Sharma
 *                     email:
 *                       type: string
 *                       example: <EMAIL>
 *                     mobile_number:
 *                       type: string
 *                       example: 9876543210
 *                     username:
 *                       type: string
 *                       example: admin
 *                     role:
 *                       type: string
 *                       example: admin
 *                     permissions:
 *                       type: string
 *                       nullable: true
 *                       example: null
 *                     disabled:
 *                       type: boolean
 *                       example: false
 *                     superman:
 *                       type: boolean
 *                       example: true
 *                     auth_secret:
 *                       type: string
 *                       example: ABCDEF123456XYZ
 *                     logged_in:
 *                       type: boolean
 *                       example: false
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-05T13:14:55.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-05T07:53:56.000Z
 *                     token:
 *                       type: object
 *                       properties:
 *                         access:
 *                           type: object
 *                           properties:
 *                             token:
 *                               type: string
 *                               example: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *                             expires:
 *                               type: string
 *                               format: date-time
 *                               example: 2025-07-15T17:18:56.689Z
 *                         refresh:
 *                           type: object
 *                           properties:
 *                             token:
 *                               type: string
 *                               example: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *                             expires:
 *                               type: string
 *                               format: date-time
 *                               example: 2026-03-03T06:38:56.693Z
 *       400:
 *         description: Invalid username or password.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid username or password.
 */
/**
 * @swagger
 * /admin:
 *   get:
 *     tags:
 *       - Admin
 *     summary: Get all admins
 *     description: Retrieve a list of all admins with their details and pagination info.
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           example: 1
 *         description: Page number for pagination.
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Number of records per page.
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           example: "ravi"
 *         description: Search by name, email, or username.
 *     responses:
 *       200:
 *         description: Successfully retrieved the admin list.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Admins retrieved successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                       example: 1
 *                     totalPages:
 *                       type: integer
 *                       example: 1
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     admins:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           first_name:
 *                             type: string
 *                             example: Ravi
 *                           last_name:
 *                             type: string
 *                             example: Sharma
 *                           email:
 *                             type: string
 *                             example: <EMAIL>
 *                           mobile_number:
 *                             type: string
 *                             example: 9876543210
 *                           username:
 *                             type: string
 *                             example: admin
 *                           role:
 *                             type: string
 *                             example: admin
 *                           permissions:
 *                             type: array
 *                             items:
 *                               type: string
 *                             nullable: true
 *                             example: null
 *                           disabled:
 *                             type: boolean
 *                             example: false
 *                           superman:
 *                             type: boolean
 *                             example: true
 *                           auth_secret:
 *                             type: string
 *                             example: ABCDEF123456XYZ
 *                           logged_in:
 *                             type: boolean
 *                             example: false
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                             example: 2025-05-05T13:14:55.000Z
 *                           updatedAt:
 *                             type: string
 *                             format: date-time
 *                             example: 2025-05-05T07:53:56.000Z
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Unable to retrieve users due to a server error.
 */
/**
 * @swagger
 * /admin:
 *   post:
 *     tags:
 *       - Admin
 *     summary: Create a new admin
 *     description: Create a new admin user with necessary information like name, email, mobile number, and password.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - first_name
 *               - last_name
 *               - email
 *               - mobile_number
 *               - username
 *               - password
 *             properties:
 *               first_name:
 *                 type: string
 *                 example: Ravi
 *               last_name:
 *                 type: string
 *                 example: Sharma
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               mobile_number:
 *                 type: string
 *                 example: 9876543210
 *               username:
 *                 type: string
 *                 example: admin
 *               password:
 *                 type: string
 *                 example: Admin@123
 *               role:
 *                 type: string
 *                 example: manager
 *               permissions:
 *                 type: object
 *                 example:
 *                   dashboard: true
 *                   users: false
 *               disabled:
 *                 type: boolean
 *                 example: false
 *               superman:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       201:
 *         description: Admin created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Admin created successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     first_name:
 *                       type: string
 *                       example: Ravi
 *                     last_name:
 *                       type: string
 *                       example: Sharma
 *                     email:
 *                       type: string
 *                       example: <EMAIL>
 *                     mobile_number:
 *                       type: string
 *                       example: 9876543210
 *                     username:
 *                       type: string
 *                       example: admin
 *                     role:
 *                       type: string
 *                       example: manager
 *                     permissions:
 *                       type: object
 *                       example:
 *                         dashboard: true
 *                         users: false
 *                     disabled:
 *                       type: boolean
 *                       example: false
 *                     superman:
 *                       type: boolean
 *                       example: true
 *                     logged_in:
 *                       type: boolean
 *                       example: false
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T09:00:00.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T09:00:00.000Z
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Validation failed
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */
/**
 * @swagger
 * /admin/{id}:
 *   get:
 *     tags:
 *       - Admin
 *     summary: Get admin by ID
 *     description: Retrieve a specific admin user's details by their unique ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique ID of the admin to retrieve.
 *     responses:
 *       200:
 *         description: Successfully retrieved the admin details.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Admin retrieved successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     first_name:
 *                       type: string
 *                       example: Ravi
 *                     last_name:
 *                       type: string
 *                       example: Sharma
 *                     email:
 *                       type: string
 *                       example: <EMAIL>
 *                     mobile_number:
 *                       type: string
 *                       example: 9876543210
 *                     username:
 *                       type: string
 *                       example: admin
 *                     role:
 *                       type: string
 *                       example: manager
 *                     permissions:
 *                       type: object
 *                       example:
 *                         dashboard: true
 *                         users: false
 *                     disabled:
 *                       type: boolean
 *                       example: false
 *                     superman:
 *                       type: boolean
 *                       example: true
 *                     logged_in:
 *                       type: boolean
 *                       example: false
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T09:00:00.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T09:00:00.000Z
 *       404:
 *         description: Admin not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Admin not found.
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */
/**
 * @swagger
 * /admin/{id}:
 *   put:
 *     tags:
 *       - Admin
 *     summary: Update admin by ID
 *     description: Update the details of an existing admin user by their unique ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique ID of the admin to update.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *                 example: Ravi
 *               last_name:
 *                 type: string
 *                 example: Sharma
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               mobile_number:
 *                 type: string
 *                 example: 9876543210
 *               username:
 *                 type: string
 *                 example: admin
 *               password:
 *                 type: string
 *                 example: NewPassword@123
 *               role:
 *                 type: string
 *                 example: manager
 *               permissions:
 *                 type: object
 *                 example:
 *                   dashboard: true
 *                   users: true
 *               disabled:
 *                 type: boolean
 *                 example: false
 *               superman:
 *                 type: boolean
 *                 example: false
 *     responses:
 *       200:
 *         description: Admin updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Admin updated successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     first_name:
 *                       type: string
 *                       example: Ravi
 *                     last_name:
 *                       type: string
 *                       example: Sharma
 *                     email:
 *                       type: string
 *                       example: <EMAIL>
 *                     mobile_number:
 *                       type: string
 *                       example: 9876543210
 *                     username:
 *                       type: string
 *                       example: admin
 *                     role:
 *                       type: string
 *                       example: manager
 *                     permissions:
 *                       type: object
 *                       example:
 *                         dashboard: true
 *                         users: true
 *                     disabled:
 *                       type: boolean
 *                       example: false
 *                     superman:
 *                       type: boolean
 *                       example: false
 *                     logged_in:
 *                       type: boolean
 *                       example: false
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T09:00:00.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T12:00:00.000Z
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Validation failed
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *       404:
 *         description: Admin not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Admin not found.
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */
/**
 * @swagger
 * /admin/{id}:
 *   delete:
 *     tags:
 *       - Admin
 *     summary: Delete admin by ID
 *     description: Delete an admin user by their unique ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique ID of the admin to delete.
 *     responses:
 *       200:
 *         description: Admin deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Admin deleted successfully.
 *       404:
 *         description: Admin not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Admin not found.
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */
