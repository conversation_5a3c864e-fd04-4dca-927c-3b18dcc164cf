 import httpStatus from "http-status";
import { Op } from "sequelize";
import ApiError from "../../../utils/ApiError";
import httpMessages from "../../../config/httpMessages";
import HelpQuestion from "../../../database/models/help_question.model";
import HelpCategory from "../../../database/models/help_category.model";
import { title } from "process";

export default class HelpQuestionService {
  constructor() { }

  /**
   * Create a HelpQuestion
   * @param {Object} body   
   * @returns {Promise<HelpQuestion>}
   */
  static createHelpQuestion = async (body: any) => {
    try {
      if (await this.getHelpQuestionByQuestion(body.question)) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.HELP.NAME_ALREADY_TAKEN
        );
      }

      const helpQuestion: HelpQuestion = await HelpQuestion.create(body);

      return await this.getHelpQuestionById(helpQuestion.id);
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get HelpQuestion by question
   * @param {String} question
   * @returns {Promise<HelpQuestion>}
   */
  static getHelpQuestionByQuestion = async (question: string) => {
    try {
      return HelpQuestion.findOne({
        where: { question },
      }).then((data: any) => data?.toJSON());
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get all HelpQuestions
   * @param {Object} option
   * @returns {Promise<HelpQuestion[]>}
   */
  static getHelpQuestions = async (option: any) => {
    try {
      const { page, limit, search } = option;
      const whereCondition: any = search
        ? {
            [Op.or]: [
              { question: { [Op.like]: `%${search.toLowerCase()}%` } },
            ],
          }
        : {};
      const queryOption: any = {
        where: whereCondition,
        include: [
          {
            model: HelpCategory,
            as: "category",
            attributes: ["title"],
          },
        ],
        order: [["createdAt", "DESC"]],
      };
      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }
      const helpQuestions = await HelpQuestion.findAndCountAll(queryOption);
      if (page && limit) {
        return {
          totalItems: helpQuestions.count,
          totalPages: Math.ceil(helpQuestions.count / limit),
          currentPage: page,
          helpQuestions: helpQuestions.rows,
        };
      } else {
        return helpQuestions.rows;
      }
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get HelpQuestion by id
   * @param {Number} id
   * @returns {Promise<HelpQuestion>}
   */
  static getHelpQuestionById = async (id: number) => {
    try {
      return HelpQuestion.findOne({
        where: { id },
        include: [
          {
            model: HelpCategory,
            as: "category",
            attributes: {
              exclude: ["createdAt", "updatedAt"],
            },
          },
        ],
      }).then((data: any) => data?.toJSON());
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Update role by id
   * @param {Number} Id
   * @param {Object} updateBody
   * @returns {Promise<Role>}
   */
  static updateHelpQuestionById = async (Id: number, updateBody: any) => {
    const details = await HelpQuestion.findByPk(Id);
    if (!details) {
      throw new ApiError(httpStatus.NOT_FOUND, httpMessages.HELP.NOT_FOUND);
    }

    Object.assign(details, updateBody);
    await details.save();
    return details;
  };

  /**
   * Delete role by id
   * @param {Number} Id
   * @returns {Promise<Role>}
   */
  static deleteHelpQuestionById = async (Id: number) => {
    try {
      const details: any = await HelpQuestion.findByPk(Id);
      if (!details) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.HELP.NOT_FOUND);
      }
      await details.destroy();
      return details;
    } catch (error: any) {
      throw new ApiError(
        error.status || httpStatus.BAD_REQUEST,
        error.message || "Error deleting Role."
      );
    }
  };
}







