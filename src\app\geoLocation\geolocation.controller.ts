import { Request, Response } from "express";
import httpStatus from "http-status";

import errorResponse, { sentResponse } from "../../utils/response";
import catchAsync from "../../utils/catchAsync";
import httpMessages from "../../config/httpMessages";

import GeolocationService from "./geolocation.service";
import ApiError from "../../utils/ApiError";

export default class GeolocationController {
  static geolocationService = GeolocationService;

  constructor() {}

  static getCountries = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const countries = await this.geolocationService.getCountries();

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: httpMessages.GEOLOCATION.COUNTRY.SUCCESS,
          data: countries,
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  static getAll = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search, countryIds } = request.query;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
        countryIds: countryIds
          ? (countryIds as string)
              .split(",")
              .map((id: any) =>
                typeof id === "string" ? parseInt(id, 10) : id
              )
          : undefined,
      };
      const geolocation = await this.geolocationService.getCountriesWithCities(
        option
      );
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.GEOLOCATION.COUNTRY.SUCCESS,
        data: geolocation,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const countryData = { ...request.body };
      const country = await this.geolocationService.createCountriesWithCities(
        countryData
      );
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.GEOLOCATION.COUNTRY.ADD_SUCCESS,
        data: country,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static showById = catchAsync(async (request: Request, response: Response) => {
    try {
      const countryId: number = parseInt(request.params.id, 10);
      const country = await this.geolocationService.getCountryById(countryId);
      if (!country) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          httpMessages.GEOLOCATION.COUNTRY.NOT_FOUND
        );
      }
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.GEOLOCATION.COUNTRY.DETAILS.SUCCESS,
        data: country,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static update = catchAsync(async (request: Request, response: Response) => {
    try {
      const countryId: number = parseInt(request.params.id, 10);
      const countryData = { ...request.body };

      const country = await this.geolocationService.updateCountriesWithCities(
        countryId,
        countryData
      );
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.GEOLOCATION.COUNTRY.UPDATE_SUCCESS,
        data: country,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static delete = catchAsync(async (request: Request, response: Response) => {
    try {
      const id: number = parseInt(request.params.id, 10);
      await this.geolocationService.deleteCountryById(id);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.GEOLOCATION.COUNTRY.DELETE,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static getCities = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const countryId = parseInt(request.params.countryId, 10);
        const cities = await this.geolocationService.getCitiesByCountry(countryId);
        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: httpMessages.GEOLOCATION.CITY.SUCCESS,
          data: cities,
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );
}
