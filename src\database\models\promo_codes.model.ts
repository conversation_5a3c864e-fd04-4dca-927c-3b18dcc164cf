import {
    AllowNull,
    <PERSON>Increment,
    Column,
    <PERSON>Type,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Model,
    NotEmpty,
    PrimaryKey,
    Table,
    Unique
} from "sequelize-typescript";
import PromoCodeUsage from "./promo_code_usage.model";

export interface PromoCodeI {
    id: number;
    code: string;
    description?: string;
    discount_type: "percentage" | "fixed_amount";
    discount_value: number;
    minimum_purchase_amount?: number;
    maximum_discount_amount?: number;
    usage_limit?: number;
    used_count: number;
    start_date?: Date;
    expiry_date?: Date;
    is_active: boolean;
    created_by?: number;
    applicable_plans?: string; // JSON array of plan IDs
    first_time_users_only: boolean;
    
    // Timestamps
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: "promo_codes",
    timestamps: true
})
class PromoCode extends Model<PromoCodeI> implements PromoCodeI {
    @AutoIncrement
    @PrimaryKey
    @Column
    id: number;

    @AllowNull(false)
    @NotEmpty
    @Unique
    @Column(DataType.STRING(50))
    code: string;

    @AllowNull(true)
    @Column(DataType.TEXT)
    description?: string;

    @AllowNull(false)
    @Column(DataType.ENUM("percentage", "fixed_amount"))
    discount_type: "percentage" | "fixed_amount";

    @AllowNull(false)
    @Column(DataType.DECIMAL(10, 2))
    discount_value: number;

    @AllowNull(true)
    @Column(DataType.DECIMAL(10, 2))
    minimum_purchase_amount?: number;

    @AllowNull(true)
    @Column(DataType.DECIMAL(10, 2))
    maximum_discount_amount?: number;

    @AllowNull(true)
    @Column(DataType.INTEGER)
    usage_limit?: number;

    @AllowNull(false)
    @Default(0)
    @Column(DataType.INTEGER)
    used_count: number;

    @AllowNull(true)
    @Column(DataType.DATE)
    start_date?: Date;

    @AllowNull(true)
    @Column(DataType.DATE)
    expiry_date?: Date;

    @AllowNull(false)
    @Default(true)
    @Column(DataType.BOOLEAN)
    is_active: boolean;

    @AllowNull(true)
    @Column(DataType.INTEGER)
    created_by?: number;

    @AllowNull(true)
    @Column(DataType.TEXT)
    applicable_plans?: string;

    @AllowNull(false)
    @Default(false)
    @Column(DataType.BOOLEAN)
    first_time_users_only: boolean;

    @HasMany(() => PromoCodeUsage, { foreignKey: "promo_code_id", onDelete: "CASCADE" })
    usage_records: PromoCodeUsage[];
}

export default PromoCode;
