
import Jo<PERSON> from "joi";

export const roleValidation = {
  body: Joi.object().keys({
    role_name: Joi.string().trim().required().messages({
      "string.empty": "Role name is required.",
      "string.base": "Role name must be a string.",
    }),
    description: Joi.string().trim().optional().allow(null, "").messages({
      "string.base": "Description must be a string.",
    }),
  }),
};

