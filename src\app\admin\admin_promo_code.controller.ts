import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";
import AdminPromoCodeService from "./admin_promo_code.service";

export default class AdminPromoCodeController {
    static adminPromoCodeService = AdminPromoCodeService;
    constructor() { }

    /**
     * Create a new promo code
     */
    static createPromoCode = catchAsync(async (request: Request, response: Response) => {
        try {
            const {
                code,
                description,
                discount_type,
                discount_value,
                minimum_purchase_amount,
                maximum_discount_amount,
                usage_limit,
                start_date,
                expiry_date,
                applicable_plans,
                first_time_users_only,
                is_active
            } = request.body;
            
            const created_by = request.decoded; // Admin ID

            const promoCode = await this.adminPromoCodeService.createPromoCode({
                code,
                description,
                discount_type,
                discount_value,
                minimum_purchase_amount,
                maximum_discount_amount,
                usage_limit,
                start_date: start_date ? new Date(start_date) : undefined,
                expiry_date: expiry_date ? new Date(expiry_date) : undefined,
                applicable_plans,
                first_time_users_only,
                is_active,
                created_by
            });

            return sentResponse(response, {
                statusCode: httpStatus.CREATED,
                message: "Promo code created successfully",
                data: promoCode
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    /**
     * Get all promo codes with pagination
     */
    static getPromoCodes = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page, limit, search, is_active } = request.query;
            console.log('page: ', page);

            const options = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
                search: search as string,
                is_active: is_active !== undefined ? is_active === 'true' : undefined
            };

            const result = await this.adminPromoCodeService.getPromoCodes(options);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Promo codes retrieved successfully",
                data: result
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    /**
     * Get promo code by ID
     */
    static getPromoCodeById = catchAsync(async (request: Request, response: Response) => {
        try {
            const { id } = request.params;

            const promoCode = await this.adminPromoCodeService.getPromoCodeById(parseInt(id, 10));

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Promo code retrieved successfully",
                data: promoCode
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    /**
     * Update promo code
     */
    static updatePromoCode = catchAsync(async (request: Request, response: Response) => {
        try {
            const { id } = request.params;
            const updateData = request.body;

            // Convert date strings to Date objects if provided
            if (updateData.start_date) {
                updateData.start_date = new Date(updateData.start_date);
            }
            if (updateData.expiry_date) {
                updateData.expiry_date = new Date(updateData.expiry_date);
            }

            const promoCode = await this.adminPromoCodeService.updatePromoCode(
                parseInt(id, 10),
                updateData
            );

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Promo code updated successfully",
                data: promoCode
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    /**
     * Delete promo code
     */
    static deletePromoCode = catchAsync(async (request: Request, response: Response) => {
        try {
            const { id } = request.params;

            await this.adminPromoCodeService.deletePromoCode(parseInt(id, 10));

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Promo code deleted successfully",
                data: null
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    /**
     * Get promo code usage statistics
     */
    static getPromoCodeStats = catchAsync(async (request: Request, response: Response) => {
        try {
            const { id } = request.params;

            const stats = await this.adminPromoCodeService.getPromoCodeStats(parseInt(id, 10));

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Promo code statistics retrieved successfully",
                data: stats
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });
}
