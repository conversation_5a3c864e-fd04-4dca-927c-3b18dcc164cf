"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middlewares/auth");
const success_story_controller_1 = __importDefault(require("../app/success_story/success_story.controller"));
const middleware_1 = require("../middlewares/middleware");
const success_story_validation_1 = require("../validations/success_story.validation");
const fileUploadMiddleware_1 = require("../middlewares/fileUploadMiddleware");
const router = express_1.default.Router();
// Public routes
router.get("/approved", success_story_controller_1.default.getApproved);
// User routes (require authentication)
router.get("/user", auth_1.auth, success_story_controller_1.default.getUserStories);
router.post("", auth_1.auth, fileUploadMiddleware_1.fileUploadMiddleware, success_story_controller_1.default.create);
// Admin routes (require authentication)
router.get("", auth_1.auth, success_story_controller_1.default.getAll);
router.get("/:id", auth_1.auth, success_story_controller_1.default.showById);
router.put("/:id/status", auth_1.auth, (0, middleware_1.validate)(success_story_validation_1.updateSuccessStoryStatusValidation), success_story_controller_1.default.updateStatus);
router.delete("/:id", auth_1.auth, (0, middleware_1.validate)(success_story_validation_1.deleteSuccessStoryValidation), success_story_controller_1.default.delete);
exports.default = router;
