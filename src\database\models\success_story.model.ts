import {
    Table,
    Column,
    Model,
    DataType,
    <PERSON>Key,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    HasMany,
    Default,
    CreatedAt,
    UpdatedAt
} from "sequelize-typescript";
import User from "./user.model";
import SuccessStoryMedia from "./success_story_media.model";

export interface SuccessStoryI {
    id: number;
    user_id: number;
    bride_name: string;
    bride_email: string;
    groom_name: string;
    groom_email: string;
    bride_country: string;
    groom_country: string;
    meeting_date: Date;
    engagement_date: Date;
    marriage_date: Date;
    current_country: string;
    contact_number: string;
    story_text: string;
    status: "pending" | "approved" | "rejected";
    admin_notes?: string;
    allow_in_ads: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: "success_stories",
    timestamps: true,
})
class SuccessStory extends Model<SuccessStoryI> implements SuccessStoryI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE" })
    user: User;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    bride_name: string;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    bride_email: string;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    groom_name: string;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    groom_email: string;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    bride_country: string;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    groom_country: string;

    @AllowNull(false)
    @Column(DataType.DATE)
    meeting_date: Date;

    @AllowNull(false)
    @Column(DataType.DATE)
    engagement_date: Date;

    @AllowNull(false)
    @Column(DataType.DATE)
    marriage_date: Date;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    current_country: string;

    @AllowNull(true)
    @Column(DataType.STRING(20))
    contact_number: string;

    @AllowNull(false)
    @Column(DataType.TEXT)
    story_text: string;

    @Default("pending")
    @AllowNull(false)
    @Column(DataType.ENUM("pending", "approved", "rejected"))
    status: "pending" | "approved" | "rejected";

    @AllowNull(true)
    @Column(DataType.TEXT)
    admin_notes?: string;

    @Default(false)
    @AllowNull(false)
    @Column(DataType.BOOLEAN)
    allow_in_ads: boolean;

    @CreatedAt
    createdAt?: Date;

    @UpdatedAt
    updatedAt?: Date;

    @HasMany(() => SuccessStoryMedia, { foreignKey: "story_id", onDelete: "CASCADE" })
    media: SuccessStoryMedia[];
}

export default SuccessStory;
