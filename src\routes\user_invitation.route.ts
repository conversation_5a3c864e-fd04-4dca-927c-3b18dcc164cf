import express from "express";
import { auth } from "../middlewares/auth";
import UserInvitationController from "../app/user/user_invitation/user_invitation.controller";
import { validate } from "../middlewares/middleware";
import { userInvitationValidation, updateInvitationStatusValidation } from "../validations/user_invitation.validation";
import { checkActionPermission } from "../middlewares/authWithSubscription";

const router = express.Router();

// Basic invitation endpoints
router.post("", checkActionPermission('interest_sent'), validate(userInvitationValidation), UserInvitationController.create);
router.get("/invitations", auth, UserInvitationController.getInvitations);
router.get("/sent", auth, UserInvitationController.sentInvitations);
router.get("/accepted", auth, UserInvitationController.acceptedInvitations);
router.get("/declined", auth, UserInvitationController.declinedInvitations);
router.put("/:id/status", auth, validate(updateInvitationStatusValidation), UserInvitationController.updateStatus);
router.get("/unread-count", auth, UserInvitationController.getUnreadCount);
router.put("/:id/read", auth, UserInvitationController.markAsRead);

// New endpoints for enhanced invitation functionality  
router.get("/user/:userId", auth, UserInvitationController.getUserProfileForInvitation);
router.get("/:id/contact", checkActionPermission('contact_viewed'), UserInvitationController.requestContactNumber);
router.get("/:id/cancel", auth, UserInvitationController.decline);
router.get("/:id/remove-friend", auth, UserInvitationController.removeFriend);

export default router;