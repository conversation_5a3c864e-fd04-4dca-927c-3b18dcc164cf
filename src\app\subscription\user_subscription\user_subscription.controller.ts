import { Request, Response } from "express";
import httpStatus from "http-status";
import UserSubscriptionService from "./user_subscription.service";
import catchAsync from "../../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../../utils/response";
import httpMessages from "../../../config/httpMessages";
import UserSubscription from "../../../database/models/user_subscriptions.model";
import ApiError from "../../../utils/ApiError";

export default class UserSubscriptionController {
    static userPreferenceService = UserSubscriptionService;
    constructor() { }

    static getAll = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page, limit, search,is_active } = request.query;
            console.log('is_active: ',  is_active);
            const option = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
                search: search ? (search as string) : "",
                is_active : is_active === 'true' ? true : is_active === 'false' ? false : undefined,
            };
            const list = await this.userPreferenceService.getUserSubscriptions(option);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.USER_SUBSCRIPTION.SUCCESS,
                data: list,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static create = catchAsync(async (request: Request, response: Response) => {
        try {
            const body = { ...request.body };
            const craetedData: UserSubscription = await this.userPreferenceService.createUserSubscription(body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.USER_SUBSCRIPTION.ADD_SUCCESS,
                data: craetedData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static showById = catchAsync(async (request: Request, response: Response) => {
        try {
            const Id: number = parseInt(request.params.id, 10);
            const details = await this.userPreferenceService.getUserSubscriptionById(Id);
            if (!details) {
                throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER_SUBSCRIPTION.NOT_FOUND);
            }
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.USER_SUBSCRIPTION.DETAILS.SUCCESS,
                data: details,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static update = catchAsync(async (request: Request, response: Response) => {
        try {
            const Id: number = parseInt(request.params.id, 10);
            const body = { ...request.body };

            const updatedData = await this.userPreferenceService.updateUserSubscriptionById(Id, body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.USER_SUBSCRIPTION.UPDATE_SUCCESS,
                data: updatedData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static delete = catchAsync(async (request: Request, response: Response) => {
        try {
            const id: number = parseInt(request.params.id, 10);
            await this.userPreferenceService.deleteUserSubscriptionById(id);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.USER_SUBSCRIPTION.DELETE,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static updateStatus = catchAsync(async (request: Request, response: Response) => {
        try {
            const id: number = parseInt(request.params.id, 10);
            const body = { ...request.body };
            const updatedData = await this.userPreferenceService.updateUserSubscriptionById(id, body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.USER_SUBSCRIPTION.UPDATE_SUCCESS,
                data: updatedData,
            });
            
        } catch (error) {
            return errorResponse(response, error);
        }
    });
}
