import httpStatus from "http-status";
import bcrypt from "bcrypt";
import { Op, Sequelize } from "sequelize";
import Module from "../../../database/models/modules.model";
import ApiError from "../../../utils/ApiError";
import RolePermission from "../../../database/models/role_permissions.model";
import Role from "../../../database/models/role.model";
import httpMessages from "../../../config/httpMessages";

export default class ModuleService {
  constructor() {}

  /**
   * Get module by id
   * @param {Number} id
   * @returns {Promise<Module>}
   */
  static getModuleById = async (id: number) => {
    try {
      return await Module.findOne({
        where: { id },
        include: [
          {
            model: Module,
            as: "parent",
            attributes: ["id", "name"],
          },
        ],
      });
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };
  /**
   * Get modules
   * @param {Object} options
   * @param {number} [options.page] - Current page number (optional)
   * @param {number} [options.limit] - Number of items per page (optional)
   * @param {string} [options.search] - Search term for filtering (optional)
   * @returns {Promise<Module[]>}
   */
  static getModules = async (options: {
    page?: number;
    limit?: number;
    search?: string;
  }) => {
    try {
      const { page, limit, search } = options;
      const whereCondition: any = search
        ? {
            [Op.or]: [
              { name: { [Op.like]: `%${search.toLowerCase()}%` } },
              { route: { [Op.like]: `%${search.toLowerCase()}%` } },
            ],
          }
        : {};

      whereCondition.parent_id = null;
      whereCondition.is_active = true;

      const queryOption: any = {
        where: whereCondition,
        attributes: ["id", "name", "route", "icon", "sort_order", "is_active"],
        include: [
          {
            model: Module,
            as: "subModules",
            required: false,
            where: { is_active: true },
            attributes: ["id", "name", "route", "icon", "sort_order", "is_active"],
          },
        ],
        order: [["sort_order", "ASC"]],
      };
      // If pagination is provided, apply pagination
      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }
      const modules = await Module.findAndCountAll(queryOption);
      if (page && limit) {
        return {
          totalItems: modules.count,
          totalPages: Math.ceil(modules.count / limit),
          currentPage: page,
          modules: modules.rows,
        };
      } else {
        return modules.rows;
      }
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Create a Module
   * @param {Object} moduleBody
   * @returns {Promise<Module>}
   */
  static createModule = async (moduleBody: any) => {
    try {
      if (await this.getModuleByRoute(moduleBody.route)) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.MODULES.ROUTE_ALREADY_TAKEN
        );
      }
      if (!moduleBody.parent_id) {
        const maxsortorder: any = await Module.findOne({
          attributes: [
            [Sequelize.fn("MAX", Sequelize.col("sort_order")), "maxSortOrder"],
          ],
          where: { parent_id: null },
          raw: true,
        });
        moduleBody.sort_order = maxsortorder?.maxSortOrder
          ? maxsortorder.maxSortOrder + 1
          : 1;
      } else {
        const parentModule: any = await Module.findByPk(moduleBody.parent_id);
        const maxsortorder: any = await Module.findOne({
          attributes: [
            [Sequelize.fn("MAX", Sequelize.col("sort_order")), "maxSortOrder"],
          ],
          where: { parent_id: parentModule.id },
          raw: true,
        });
        moduleBody.sort_order = maxsortorder?.maxSortOrder
          ? maxsortorder.maxSortOrder + 1
          : 1;
      }

      const module: Module = await Module.create(moduleBody);
      const rolePermissions: any = await Role.findAll();
      const rolePermissionsData: any = rolePermissions.map((role: any) => {
        return {
          role_id: role.id,
          module_id: module.id,
        };
      });
      await RolePermission.bulkCreate(rolePermissionsData);
      return module;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get module by route
   * @param {string} route
   * @returns {Promise<Module>}
   */
  static getModuleByRoute = async (route: string) => {
    return Module.findOne({
      where: { route },
    });
  };

  /**
   * Update module by id
   * @param {Number} moduleId
   * @param {Object} updateBody
   * @returns {Promise<Module>}
   */
  static updateModuleById = async (moduleId: number, updateBody: any) => {
    try {
      const module = await Module.findByPk(moduleId);
      if (!module) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          httpMessages.MODULES.NOT_FOUND
        );
      }
      Object.assign(module, updateBody);
      await module.save();
      return module;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Delete module by id
   * @param {Number} moduleId
   * @returns {Promise<Module>}
   */
  static deleteModuleById = async (moduleId: number) => {
    try {
      const module: any = await Module.findByPk(moduleId);
      if (!module) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          httpMessages.MODULES.NOT_FOUND
        );
      }
      await module.destroy();
      return module;
    } catch (error: any) {
      if (error.name === "SequelizeForeignKeyConstraintError") {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          "Cannot delete this Record as it is referenced in another table."
        );
      } else {
        throw new ApiError(
          error.status || httpStatus.BAD_REQUEST,
          error.message || "Error deleting Module."
        );
      }
    }
  };
}
