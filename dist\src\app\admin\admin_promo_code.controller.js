"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const response_1 = __importStar(require("../../utils/response"));
const admin_promo_code_service_1 = __importDefault(require("./admin_promo_code.service"));
class AdminPromoCodeController {
    constructor() { }
}
_a = AdminPromoCodeController;
AdminPromoCodeController.adminPromoCodeService = admin_promo_code_service_1.default;
/**
 * Create a new promo code
 */
AdminPromoCodeController.createPromoCode = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { code, description, discount_type, discount_value, minimum_purchase_amount, maximum_discount_amount, usage_limit, start_date, expiry_date, applicable_plans, first_time_users_only, is_active } = request.body;
        const created_by = request.decoded; // Admin ID
        const promoCode = yield _a.adminPromoCodeService.createPromoCode({
            code,
            description,
            discount_type,
            discount_value,
            minimum_purchase_amount,
            maximum_discount_amount,
            usage_limit,
            start_date: start_date ? new Date(start_date) : undefined,
            expiry_date: expiry_date ? new Date(expiry_date) : undefined,
            applicable_plans,
            first_time_users_only,
            is_active,
            created_by
        });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.CREATED,
            message: "Promo code created successfully",
            data: promoCode
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Get all promo codes with pagination
 */
AdminPromoCodeController.getPromoCodes = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, is_active } = request.query;
        console.log('page: ', page);
        const options = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search,
            is_active: is_active !== undefined ? is_active === 'true' : undefined
        };
        const result = yield _a.adminPromoCodeService.getPromoCodes(options);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Promo codes retrieved successfully",
            data: result
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Get promo code by ID
 */
AdminPromoCodeController.getPromoCodeById = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = request.params;
        const promoCode = yield _a.adminPromoCodeService.getPromoCodeById(parseInt(id, 10));
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Promo code retrieved successfully",
            data: promoCode
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Update promo code
 */
AdminPromoCodeController.updatePromoCode = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = request.params;
        const updateData = request.body;
        // Convert date strings to Date objects if provided
        if (updateData.start_date) {
            updateData.start_date = new Date(updateData.start_date);
        }
        if (updateData.expiry_date) {
            updateData.expiry_date = new Date(updateData.expiry_date);
        }
        const promoCode = yield _a.adminPromoCodeService.updatePromoCode(parseInt(id, 10), updateData);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Promo code updated successfully",
            data: promoCode
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Delete promo code
 */
AdminPromoCodeController.deletePromoCode = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = request.params;
        yield _a.adminPromoCodeService.deletePromoCode(parseInt(id, 10));
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Promo code deleted successfully",
            data: null
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Get promo code usage statistics
 */
AdminPromoCodeController.getPromoCodeStats = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = request.params;
        const stats = yield _a.adminPromoCodeService.getPromoCodeStats(parseInt(id, 10));
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Promo code statistics retrieved successfully",
            data: stats
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = AdminPromoCodeController;
