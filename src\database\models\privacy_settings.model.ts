import {
    AllowNull,
    AutoIncrement,
    BelongsTo,
    Column,
    DataType,
    Foreign<PERSON>ey,
    Model,
    PrimaryKey,
    Table,
} from 'sequelize-typescript';

import User from './user.model';

export interface PrivacySettingI {
    id: number;
    user_id: number;
    alert_matches_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';
    alert_membership_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';
    alert_messages_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';
    alert_invitations_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';
    alert_updates_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';
    alert_profile_completion_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';
    profile_visibility: number;
    profile_start_date: Date | null;
    profile_end_date: Date | null;
    delete_reason: 'found_match_barbadhu' | 'found_match_elsewhere' | 'taking_break' | 'unhappy_experience' | 'other_reasons';
    delete_feedback: string | null;
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: 'privacy_settings',
    timestamps: true,
})
class PrivacySetting extends Model<PrivacySettingI> implements PrivacySettingI {
    @AutoIncrement
    @PrimaryKey
    @Column(DataType.INTEGER)
    id: number;

    @AllowNull(false)
    @ForeignKey(() => User)
    @Column(DataType.INTEGER)
    user_id: number;

    @AllowNull(true)
    @Column(DataType.ENUM('Daily', 'Weekly', 'Unsubscribe'))
    alert_matches_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';

    @AllowNull(true)
    @Column(DataType.ENUM('Daily', 'Weekly', 'Unsubscribe'))
    alert_membership_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';

    @AllowNull(true)
    @Column(DataType.ENUM('Daily', 'Weekly', 'Unsubscribe'))
    alert_messages_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';

    @AllowNull(true)
    @Column(DataType.ENUM('Daily', 'Weekly', 'Unsubscribe'))
    alert_invitations_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';

    @AllowNull(true)
    @Column(DataType.ENUM('Daily', 'Weekly', 'Unsubscribe'))
    alert_updates_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';

    @AllowNull(true)
    @Column(DataType.ENUM('Daily', 'Weekly', 'Unsubscribe'))
    alert_profile_completion_frequency: 'Daily' | 'Weekly' | 'Unsubscribe';

    @AllowNull(true)
    @Column(DataType.INTEGER)
    profile_visibility: number;

    @AllowNull(true)
    @Column(DataType.DATE)
    profile_start_date: Date | null;

    @AllowNull(true)
    @Column(DataType.DATE)
    profile_end_date: Date | null;

    @AllowNull(true)
    @Column(
        DataType.ENUM(
            'found_match_barbadhu',
            'found_match_elsewhere',
            'taking_break',
            'unhappy_experience',
            'other_reasons'
        )
    )
    delete_reason: 'found_match_barbadhu' | 'found_match_elsewhere' | 'taking_break' | 'unhappy_experience' | 'other_reasons';

    @AllowNull(true)
    @Column(DataType.TEXT)
    delete_feedback: string | null;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;
}

export default PrivacySetting;
