import httpStatus from "http-status";
import bcrypt from "bcrypt";

import ApiError from "../../utils/ApiError";
import UserService from "../user/user.service";
import httpMessages from "../../config/httpMessages";
import { config } from "../../config/config";
import TokenService from "../../common/services/token.service";
import Token from "../../database/models/token.model";
import twilio from 'twilio';

import { TOKEN_TYPES } from "../../database/config/enums";
import UserVerification from "../../database/models/user_verifications.model";
import User from "../../database/models/user.model";
// const client = twilio(process.env.TWILIO_ACCOUNT_SID!, process.env.TWILIO_AUTH_TOKEN!);

export default class AuthService {
  static userService = UserService;
  static tokenService = TokenService;

  constructor() { }

  static isPasswordMatch = async (user: any, password: string) =>
    await bcrypt.compare(password, user.password);

  /**
   * Login with username and password
   * @param {string} email
   * @param {string} password
   * @returns {Promise<User>}
   */
  static loginUserWithEmailAndPassword = async (
    email: string,
    password: string
  ) => {
    const MAX_FAILED_ATTEMPTS = 3;
    const SUSPENSION_TIME_MINUTES = 30;

    const user = await this.userService
      .getUserByEmail(email, { shouldReturnPassword: true })
      .then((data: any) => data?.toJSON());

    if (!user) {
      throw new ApiError(
        httpStatus.UNAUTHORIZED,
        httpMessages.LOGIN.INCORRECT_EMAIL
      );
    }

    if (user.isSuspended) {
      const minutesPassed = (Date.now() - new Date(user.suspendedAt!).getTime()) / 60000;
      
      if (minutesPassed < SUSPENSION_TIME_MINUTES) {
        throw new ApiError(
          httpStatus.UNAUTHORIZED,
          `Account suspended. Please try again in ${Math.ceil(SUSPENSION_TIME_MINUTES - minutesPassed)} minutes.`
        );
      } else {
        await User.update({ isSuspended: false, failedLoginAttempts: 0, suspendedAt: null }, { where: { id: user.id } });
      }
    }

    if (!(await this.isPasswordMatch(user, password))) {
      await User.update({ failedLoginAttempts: user.failedLoginAttempts + 1 }, { where: { id: user.id } });
      if (user.failedLoginAttempts + 1 >= MAX_FAILED_ATTEMPTS) {
        await User.update({ isSuspended: true, suspendedAt: new Date() }, { where: { id: user.id } });
        throw new ApiError(
          httpStatus.UNAUTHORIZED,
          `Account suspended. Please try again in ${SUSPENSION_TIME_MINUTES} minutes.`
        );
      }
      throw new ApiError(
        httpStatus.UNAUTHORIZED,
        httpMessages.LOGIN.INCORRECT_PASS
      );
    }
    await User.update({ failedLoginAttempts: 0 }, { where: { id: user.id } });
    user.password = undefined;
    return await User.findOne({
      where: { id: user.id },
      attributes: { exclude: ["password"] },
      include: [
        {
          model: UserVerification,
          as: "verification",
        },
      ],
    }).then((data: any) => data?.toJSON());
  };

  /**
   * Logout
   * @param {string} refreshToken
   * @returns {Promise}
   */
  static logout = async (token: string) => {
    const refreshTokenDoc: any = await Token.findOne({
      where: {
        token,
        type: TOKEN_TYPES.REFRESH,
        blacklisted: false
      }
    });
    if (!refreshTokenDoc) {
      throw new ApiError(
        httpStatus.NOT_FOUND,
        httpMessages.REFRESH_TOKEN_ERROR
      );
    }
    await refreshTokenDoc.destroy();
  };

  /**
   * Refresh auth tokens
   * @param {string} refreshToken
   * @returns {Promise<Object>}
   */
  static refreshAuth = async (refreshToken: string) => {
    try {
      const refreshTokenDoc = await this.tokenService.verifyToken(
        refreshToken,
        TOKEN_TYPES.REFRESH
      );

      const user = await this.userService.getUserById(refreshTokenDoc.userId);
      if (!user) {
        throw new Error();
      }

      return this.tokenService.generateAccessToken(user);
    } catch (error) {
      throw new ApiError(
        httpStatus.UNAUTHORIZED,
        httpMessages.USER.AUTH.UNAUTHORIZED
      );
    }
  };

  /**
   * Reset password
   * @param {string} resetPasswordToken
   * @param {string} newPassword
   * @returns {Promise}
   */
  static resetPassword = async (
    resetPasswordToken: any,
    newPassword: string
  ) => {
    try {
      const resetPasswordTokenDoc = await this.tokenService.verifyToken(
        resetPasswordToken,
        TOKEN_TYPES.RESET_PASSWORD
      );
      const user: any = await User.findByPk(resetPasswordTokenDoc.user_id);

      if (!user) {
        throw new Error(httpMessages.USER.EMAIL_NOT_FOUND);
      }
      Object.assign(user, { password: newPassword });
      await user.save();
      await Token.destroy({
        where: { user_id: user.id, type: TOKEN_TYPES.RESET_PASSWORD }
      });
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

}
