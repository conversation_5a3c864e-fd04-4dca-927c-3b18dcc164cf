import { Response, NextFunction } from "express";
import httpStatus from "http-status";
import jwt from "jsonwebtoken";
import ApiError from "../utils/ApiError";
import { config } from "../config/config";
import errorResponse from "../utils/response";
import httpMessages from "../config/httpMessages";
import SubscriptionService from "../app/subscription/subscription.service";

export const authWithSubscription = async (
  request: any,
  response: Response,
  next: NextFunction
) => {
  try {
    // First, validate the JWT token
    const authorization: string | undefined = request.headers.authorization;
    const token: string = authorization?.split(" ")[1] || "";
    const decoded: any = jwt.verify(token, config.jwt.secret);
    const user: any = decoded.sub;

    if (!user) {
      return errorResponse(
        response,
        new ApiError(
          httpStatus.UNAUTHORIZED,
          httpMessages.USER.AUTH.UNAUTHORIZED
        )
      );
    }

    // Add user to request
    request["decoded"] = user;

    // Check subscription status
    try {
      const subscriptionStatus = await SubscriptionService.getActiveSubscription(user);
      
      // Add subscription info to request
      request["subscription"] = {
        isActive: subscriptionStatus.isActive,
        subscription: subscriptionStatus.subscription,
        plan: subscriptionStatus.plan,
        message: subscriptionStatus.message,
        remainingUsage: subscriptionStatus.remainingUsage
      };

      // If subscription is not active, add warning but don't block
      if (!subscriptionStatus.isActive) {
        request["subscriptionWarning"] = {
          hasActiveSubscription: false,
          message: subscriptionStatus.message,
          requiresUpgrade: true
        };
      }

    } catch (subscriptionError) {
      console.error('Subscription check error:', subscriptionError);
      // Don't block request if subscription check fails
      request["subscription"] = {
        isActive: false,
        message: "Unable to verify subscription status"
      };
    }

    return next();
  } catch (e) {
    return errorResponse(
      response,
      new ApiError(httpStatus.UNAUTHORIZED, httpMessages.USER.AUTH.UNAUTHORIZED)
    );
  }
};

// Middleware that requires active subscription
export const requireActiveSubscription = async (
  request: any,
  response: Response,
  next: NextFunction
) => {
  try {
    // First run auth with subscription
    await authWithSubscription(request, response, () => {});

    // Check if subscription is active
    if (!request.subscription?.isActive) {
      return errorResponse(
        response,
        new ApiError(
          httpStatus.FORBIDDEN,
          request.subscription?.message || "Active subscription required"
        )
      );
    }

    return next();
  } catch (error) {
    return errorResponse(
      response,
      new ApiError(httpStatus.UNAUTHORIZED, "Authentication failed")
    );
  }
};

// Middleware to check if user can perform specific action
export const checkActionPermission = (actionType: string) => {
  return async (request: any, response: Response, next: NextFunction) => {
    try {
      // First run auth with subscription
      await authWithSubscription(request, response, () => {});

      const user_id = request.decoded;
      const target_user_id =  request.params.id || request.body.receiver_id;

      // Check if user can perform the action
      const canPerform = await SubscriptionService.canPerformAction(
        user_id,
        actionType,
        target_user_id ? Number(target_user_id) : undefined
      );

      if (!canPerform.canPerform) {
        return errorResponse(
          response,
          new ApiError(
            httpStatus.FORBIDDEN,
            canPerform.message
          )
        );
      }

      // Add permission info to request
      request["actionPermission"] = canPerform;

      return next();
    } catch (error) {
      return errorResponse(
        response,
        new ApiError(httpStatus.UNAUTHORIZED, "Permission check failed")
      );
    }
  };
};
