import axios from "axios";
import httpStatus from "http-status";
import ApiError from "../../utils/ApiError";
import PaypalPayment from "../../database/models/paypal_payment.model";
import PaypalWebhookEvent from "../../database/models/paypal_webhook_event.model";
import User from "../../database/models/user.model";
import SubscriptionPlan from "../../database/models/subscription_plans.mode";
import UserSubscription from "../../database/models/user_subscriptions.model";
import { Op, Sequelize } from "sequelize";

const client_id: any = process.env.PAYPAL_CLIENT_ID;
const client_secret: any = process.env.PAYPAL_CLIENT_SECRET;

interface CreateOrderParams {
    user_id: number;
    plan_id?: number;
    amount: number;
    currency: string;
}

interface PaymentFilters {
    page: number;
    limit: number;
    status?: string;
}

interface AnalyticsParams {
    start_date?: string;
    end_date?: string;
    user_id?: number;
}

interface RefundParams {
    amount?: number;
    reason?: string;
}

export default class PaypalService {
    constructor() { }

    static generateAccessToken = async () => {
        try {
            const response = await axios.post(
                `${process.env.PAYPAL_API_URL}/v1/oauth2/token`,
                `grant_type=client_credentials`,
                {
                    auth: {
                        username: client_id,
                        password: client_secret,
                    },
                }
            );
            return response.data.access_token;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    static createOrder = async (params: CreateOrderParams) => {
        try {
            const { user_id, plan_id, amount, currency } = params;
            const accessToken = await this.generateAccessToken();

            // Get subscription plan details if plan_id is provided
            let planDetails = null;
            if (plan_id) {
                planDetails = await SubscriptionPlan.findByPk(plan_id);
                if (!planDetails) {
                    throw new ApiError(httpStatus.NOT_FOUND, "Subscription plan not found");
                }
            }

            const orderData = {
                intent: "CAPTURE",
                purchase_units: [
                    {
                        amount: {
                            currency_code: currency,
                            value: planDetails ? planDetails.price : amount.toFixed(2),
                        },
                        description: planDetails ?
                            `Subscription: ${planDetails.name}` :
                            "BarBadhu Payment",
                    },
                ],
                application_context: {
                    return_url: `${process.env.BASE_URL}/paypal/success`,
                    cancel_url: `${process.env.BASE_URL}/paypal/cancel`,
                    landing_page: "LOGIN",
                    shipping_preference: "NO_SHIPPING",
                    user_action: "PAY_NOW",
                    brand_name: "BarBadhu",
                },
            };

            const response = await axios({
                method: 'post',
                url: `${process.env.PAYPAL_API_URL}/v2/checkout/orders`,
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    "Content-Type": "application/json",
                },
                data: JSON.stringify(orderData),
            });

            // Save payment record to database
            const paymentRecord = await PaypalPayment.create({
                user_id,
                plan_id,
                paypal_order_id: response.data.id,
                amount: planDetails ? planDetails.price : amount,
                currency,
                status: "pending",
                payment_method: "paypal",
                description: planDetails ?
                    `Subscription: ${planDetails.name}` :
                    "BarBadhu Payment",
                paypal_response: response.data,
            });

            return {
                ...response.data,
                payment_id: paymentRecord.id,
            };
        } catch (error: any) {
            console.error('PayPal create order error:', error);
            throw new ApiError(httpStatus.BAD_REQUEST, error.response?.data?.message || error.message);
        }
    };

    static capturePayment = async (order_id: string, user_id: number) => {
        try {
            const accessToken = await this.generateAccessToken();

            // Find the payment record
            const paymentRecord = await PaypalPayment.findOne({
                where: { paypal_order_id: order_id, user_id }
            });

            if (!paymentRecord) {
                throw new ApiError(httpStatus.NOT_FOUND, "Payment record not found");
            }

            // Capture the payment
            const response = await axios({
                method: 'post',
                url: `${process.env.PAYPAL_API_URL}/v2/checkout/orders/${order_id}/capture`,
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    "Content-Type": "application/json",
                },
            });

            // Update payment record
            const captureData = response.data;
            const paymentId = captureData.purchase_units[0]?.payments?.captures[0]?.id;
            const payerId = captureData.payer?.payer_id;

            await paymentRecord.update({
                status: "completed",
                paypal_payment_id: paymentId,
                paypal_payer_id: payerId,
                paypal_response: captureData,
            });

            // If this is a subscription payment, create/update user subscription
            if (paymentRecord.plan_id) {
                await this.activateUserSubscription(user_id, paymentRecord.plan_id);
            }

            return {
                ...captureData,
                payment_id: paymentRecord.id,
            };
        } catch (error: any) {
            console.error('PayPal capture payment error:', error);
            throw new ApiError(httpStatus.BAD_REQUEST, error.response?.data?.message || error.message);
        }
    };

    static getPaymentDetails = async (payment_id: string, user_id: number) => {
        try {
            const paymentRecord = await PaypalPayment.findOne({
                where: { id: payment_id, user_id },
                include: [
                    {
                        model: User,
                        attributes: ['id', 'first_name', 'last_name', 'email']
                    },
                    {
                        model: SubscriptionPlan,
                        attributes: ['id', 'name', 'price', 'duration_days']
                    }
                ]
            });

            if (!paymentRecord) {
                throw new ApiError(httpStatus.NOT_FOUND, "Payment not found");
            }

            return paymentRecord;
        } catch (error: any) {
            console.error('Get payment details error:', error);
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    static getUserPayments = async (option: any) => {
        try {
            const { page, limit, search, user_id } = option;
            const whereCondition: any = {};

            whereCondition.user_id = user_id;
            whereCondition.status = "completed";
            if (search) {
                whereCondition.title = { [Op.like]: `%${search.toLowerCase()}%` };
            }
            const queryOption: any = {
                where: whereCondition,
                include: [
                    {
                        model: SubscriptionPlan,
                        attributes: ['id', 'name', 'price', 'duration_days']
                    }
                ],
                order: [["createdAt", "DESC"]],
            };
            if (page && limit) {
                const offset = (page - 1) * limit;
                queryOption.limit = limit;
                queryOption.offset = offset;
            }
            const payments = await PaypalPayment.findAndCountAll(queryOption);
            if (page && limit) {
                return {
                    totalItems: payments.count,
                    totalPages: Math.ceil(payments.count / limit),
                    currentPage: page,
                    payments: payments.rows,
                };
            } else {
                return payments.rows;
            }
        } catch (error: any) {
            console.error('Get user payments error:', error);
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    static handleWebhookEvent = async (webhookEvent: any, webhookId: string) => {
        try {
            // Save webhook event for audit trail
            const webhookRecord = await PaypalWebhookEvent.create({
                event_id: webhookEvent.id,
                event_type: webhookEvent.event_type,
                resource_type: webhookEvent.resource_type,
                resource_id: webhookEvent.resource?.id,
                summary: webhookEvent.summary,
                event_data: webhookEvent,
                processed: false,
            });

            // Process different webhook events
            switch (webhookEvent.event_type) {
                case 'PAYMENT.CAPTURE.COMPLETED':
                    await this.handlePaymentCompleted(webhookEvent);
                    break;
                case 'PAYMENT.CAPTURE.DENIED':
                case 'PAYMENT.CAPTURE.DECLINED':
                    await this.handlePaymentFailed(webhookEvent);
                    break;
                case 'PAYMENT.CAPTURE.REFUNDED':
                    await this.handlePaymentRefunded(webhookEvent);
                    break;
                default:
                    console.log(`Unhandled webhook event type: ${webhookEvent.event_type}`);
            }

            // Mark webhook as processed
            await webhookRecord.update({
                processed: true,
                processed_at: new Date(),
            });

            return { success: true };
        } catch (error: any) {
            console.error('Webhook processing error:', error);
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    static getPaymentAnalytics = async (params: AnalyticsParams) => {
        try {
            const { start_date, end_date, user_id } = params;

            const whereClause: any = {};
            if (user_id) {
                whereClause.user_id = user_id;
            }
            if (start_date && end_date) {
                whereClause.created_at = {
                    [Op.between]: [new Date(start_date), new Date(end_date)]
                };
            }

            const totalPayments = await PaypalPayment.count({ where: whereClause });
            const completedPayments = await PaypalPayment.count({
                where: { ...whereClause, status: 'completed' }
            });
            const totalAmount = await PaypalPayment.sum('amount', {
                where: { ...whereClause, status: 'completed' }
            });

            const paymentsByStatus = await PaypalPayment.findAll({
                where: whereClause,
                attributes: [
                    'status',
                    [PaypalPayment.sequelize!.fn('COUNT', PaypalPayment.sequelize!.col('id')), 'count'],
                    [PaypalPayment.sequelize!.fn('SUM', PaypalPayment.sequelize!.col('amount')), 'total_amount']
                ],
                group: ['status'],
                raw: true,
            });

            return {
                totalPayments,
                completedPayments,
                totalAmount: totalAmount || 0,
                paymentsByStatus,
            };
        } catch (error: any) {
            console.error('Get payment analytics error:', error);
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    static refundPayment = async (payment_id: string, params: RefundParams) => {
        try {
            const { amount, reason } = params;
            const accessToken = await this.generateAccessToken();

            // Find the payment record
            const paymentRecord = await PaypalPayment.findByPk(payment_id);
            if (!paymentRecord || !paymentRecord.paypal_payment_id) {
                throw new ApiError(httpStatus.NOT_FOUND, "Payment not found or not captured");
            }

            // Create refund request
            const refundData: any = {};
            if (amount) {
                refundData.amount = {
                    value: amount.toFixed(2),
                    currency_code: paymentRecord.currency
                };
            }
            if (reason) {
                refundData.note_to_payer = reason;
            }

            const response = await axios({
                method: 'post',
                url: `${process.env.PAYPAL_API_URL}/v2/payments/captures/${paymentRecord.paypal_payment_id}/refund`,
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    "Content-Type": "application/json",
                },
                data: JSON.stringify(refundData),
            });

            // Update payment record
            await paymentRecord.update({
                status: "refunded",
                refund_id: response.data.id,
                refund_amount: amount || paymentRecord.amount,
                refund_reason: reason,
            });

            return response.data;
        } catch (error: any) {
            console.error('PayPal refund error:', error);
            throw new ApiError(httpStatus.BAD_REQUEST, error.response?.data?.message || error.message);
        }
    };

    // Helper methods for webhook processing
    static handlePaymentCompleted = async (webhookEvent: any) => {
        try {
            const captureId = webhookEvent.resource.id;
            const orderId = webhookEvent.resource.supplementary_data?.related_ids?.order_id;

            if (orderId) {
                const paymentRecord = await PaypalPayment.findOne({
                    where: { paypal_order_id: orderId }
                });

                if (paymentRecord) {
                    await paymentRecord.update({
                        status: "completed",
                        paypal_payment_id: captureId,
                        paypal_response: webhookEvent.resource,
                    });

                    // Activate subscription if applicable
                    if (paymentRecord.plan_id) {
                        await this.activateUserSubscription(paymentRecord.user_id, paymentRecord.plan_id);
                    }
                }
            }
        } catch (error: any) {
            console.error('Handle payment completed error:', error);
        }
    };

    static handlePaymentFailed = async (webhookEvent: any) => {
        try {
            const orderId = webhookEvent.resource.supplementary_data?.related_ids?.order_id;

            if (orderId) {
                const paymentRecord = await PaypalPayment.findOne({
                    where: { paypal_order_id: orderId }
                });

                if (paymentRecord) {
                    await paymentRecord.update({
                        status: "failed",
                        paypal_response: webhookEvent.resource,
                    });
                }
            }
        } catch (error: any) {
            console.error('Handle payment failed error:', error);
        }
    };

    static handlePaymentRefunded = async (webhookEvent: any) => {
        try {
            const refundId = webhookEvent.resource.id;
            const captureId = webhookEvent.resource.links?.find((link: any) =>
                link.rel === 'up'
            )?.href?.split('/').pop();

            if (captureId) {
                const paymentRecord = await PaypalPayment.findOne({
                    where: { paypal_payment_id: captureId }
                });

                if (paymentRecord) {
                    await paymentRecord.update({
                        status: "refunded",
                        refund_id: refundId,
                        refund_amount: parseFloat(webhookEvent.resource.amount.value),
                        paypal_response: webhookEvent.resource,
                    });
                }
            }
        } catch (error: any) {
            console.error('Handle payment refunded error:', error);
        }
    };

    static activateUserSubscription = async (user_id: number, plan_id: number) => {
        try {
            const plan = await SubscriptionPlan.findByPk(plan_id);
            if (!plan) {
                throw new ApiError(httpStatus.NOT_FOUND, "Subscription plan not found");
            }

            const startDate = new Date();
            const endDate = new Date();
            endDate.setDate(startDate.getDate() + plan.duration_days);

            // Check if user already has an active subscription
            const existingSubscription = await UserSubscription.findOne({
                where: { user_id, is_active: true }
            });

            if (existingSubscription) {
                // Update existing subscription
                await existingSubscription.update({
                    plan_id,
                    payment_status: "completed",
                    start_date: startDate,
                    end_date: endDate,
                    expires_at: endDate,
                    interest_sent_count: 0,
                    contact_viewed_count: 0,
                    profile_viewed_count: 0,
                    chat_initiated_count: 0,
                    is_active: true,
                    token: `sub_${Date.now()}_${user_id}`,
                });
            } else {
                // Create new subscription
                await UserSubscription.create({
                    user_id,
                    plan_id,
                    payment_status: "completed",
                    start_date: startDate,
                    end_date: endDate,
                    auto_renew: false,
                    issued_at: startDate,
                    expires_at: endDate,
                    interest_sent_count: 0,
                    contact_viewed_count: 0,
                    profile_viewed_count: 0,
                    chat_initiated_count: 0,
                    is_active: true,
                    token: `sub_${Date.now()}_${user_id}`,
                });
            }

            return true;
        } catch (error: any) {
            console.error('Activate user subscription error:', error);
            throw error;
        }
    };

    static getTransactions = async (option: any) => {
        try {
            const { page, limit, search, status } = option;
            const whereCondition: any = {};

            if (status) {
                whereCondition.status = status;
            }
            const queryOption: any = {
                where: whereCondition,
                include: [
                    {
                        model: User,
                        as: 'user',
                        attributes: ['id', 'first_name', 'last_name', 'email']
                    },
                    {
                        model: SubscriptionPlan,
                        as: 'subscriptionPlan',
                        attributes: ['id', 'name', 'price', 'duration_days']
                    }
                ],
                order: [["createdAt", "DESC"]],
            };
            if (search) {
                const escapedSearch = `%${search.toLowerCase()}%`;
                queryOption.where = {
                    ...whereCondition,
                    [Op.or]: [
                        Sequelize.literal(`LOWER(\`paypal_payment_id\`) LIKE '${escapedSearch}'`),
                        Sequelize.literal(`LOWER(\`user\`.\`first_name\`) LIKE '${escapedSearch}'`),
                        Sequelize.literal(`LOWER(\`user\`.\`last_name\`) LIKE '${escapedSearch}'`),
                        Sequelize.literal(`LOWER(\`subscriptionPlan\`.\`name\`) LIKE '${escapedSearch}'`),
                    ]
                };
            }
            if (page && limit) {
                const offset = (page - 1) * limit;
                queryOption.limit = limit;
                queryOption.offset = offset;
            }
            const transactions = await PaypalPayment.findAndCountAll(queryOption);
            if (page && limit) {
                return {
                    totalItems: transactions.count,
                    totalPages: Math.ceil(transactions.count / limit),
                    currentPage: page,
                    transactions: transactions.rows,
                };
            } else {
                return transactions.rows;
            }
        } catch (error: any) {
            console.error('Get transactions error:', error);
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };
}
