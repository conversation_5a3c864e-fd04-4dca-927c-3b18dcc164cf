import { Request, Response, NextFunction } from "express";
import fileUpload from "express-fileupload";
import fs from "fs";
import path from "path";
import errorResponse from "../utils/response";
import ApiError from "../utils/ApiError";
import httpMessages from "../config/httpMessages";
import httpStatus from "http-status";

export const fileUploadMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (req.headers["content-type"]?.includes("multipart/form-data")) {
      if (req.files) {
        const uploadDir = path.join(__dirname, "../../uploads");

        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }

        const processFiles = async (files: any, fieldName?: string) => {
          if (Array.isArray(files)) {
            for (const file of files) {
              const savedFileName = await saveFile(file);
              if (fieldName) {
                req.body[fieldName] = req.body[fieldName]
                  ? [...req.body[fieldName], savedFileName]
                  : [savedFileName];
              }
            }
          } else if (typeof files === "object" && files?.name) {
            const savedFileName = await saveFile(files);
            if (fieldName) {
              req.body[fieldName] = savedFileName;
            }
          } else {
            for (const key in files) {
              await processFiles(files[key], key); // Pass the field name
            }
          }
        };

        const saveFile = async (
          file: fileUpload.UploadedFile
        ): Promise<string> => {
          const fileName = `${Date.now()}_${file.name}`;
          const filePath = path.join(uploadDir, fileName);
          await file.mv(filePath);
          console.log(`File saved: ${filePath}`);
          // const form = new FormData();
          // form.append("image", uploadedFile.data, uploadedFile.name);

          // const response = await axios.post("https://ubsoi.com/images", form, {
          //   headers: {
          //     ...form.getHeaders(), // Set necessary headers for FormData
          //   },
          // });

          // req.uploadedImageUrl = response.data.imageUrl; // Customize based on actual response

          // req.uploadedLocalFilePath = localFilePath;
          return fileName; // Return the saved file name
        };

        await processFiles(req.files);

        next();
      } else {
        next();
      }
    } else {
      next();
    }
  } catch (error) {
    console.error("Error uploading file:", error);
    return errorResponse(
      res,
      new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        httpMessages.DOCUMENT.ERROR
      )
    );
  }
};
