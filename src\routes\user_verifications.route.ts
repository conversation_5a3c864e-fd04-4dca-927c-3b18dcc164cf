import express from "express";
import { auth } from "../middlewares/auth";
import UserVerificationController from "../app/user/user_verifications/user_verifications.controller";
import { validate } from "../middlewares/middleware";
import { fileUploadMiddleware } from "../middlewares/fileUploadMiddleware";

const router = express.Router();


router.post("/send-phone-otp", auth, UserVerificationController.sendPhoneOtp);
router.post("/verify-phone-otp", auth, UserVerificationController.verifyPhoneOtp);
router.post("/upload-id-documents", auth, fileUploadMiddleware, UserVerificationController.uploadIdDocuments);
router.get("", auth, UserVerificationController.getAll);
// router.post("", auth,fileUploadMiddleware, UserVerificationController.create);
router.get("/showById", auth, UserVerificationController.showById);
router.put("/:id", auth, UserVerificationController.update);
// router.delete("/:id", auth, UserVerificationController.delete);

export default router;
