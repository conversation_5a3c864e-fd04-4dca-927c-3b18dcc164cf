"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middlewares/auth");
const user_invitation_controller_1 = __importDefault(require("../app/user/user_invitation/user_invitation.controller"));
const middleware_1 = require("../middlewares/middleware");
const user_invitation_validation_1 = require("../validations/user_invitation.validation");
const authWithSubscription_1 = require("../middlewares/authWithSubscription");
const router = express_1.default.Router();
// Basic invitation endpoints
router.post("", (0, authWithSubscription_1.checkActionPermission)('interest_sent'), (0, middleware_1.validate)(user_invitation_validation_1.userInvitationValidation), user_invitation_controller_1.default.create);
router.get("/invitations", auth_1.auth, user_invitation_controller_1.default.getInvitations);
router.get("/sent", auth_1.auth, user_invitation_controller_1.default.sentInvitations);
router.get("/accepted", auth_1.auth, user_invitation_controller_1.default.acceptedInvitations);
router.get("/declined", auth_1.auth, user_invitation_controller_1.default.declinedInvitations);
router.put("/:id/status", auth_1.auth, (0, middleware_1.validate)(user_invitation_validation_1.updateInvitationStatusValidation), user_invitation_controller_1.default.updateStatus);
router.get("/unread-count", auth_1.auth, user_invitation_controller_1.default.getUnreadCount);
router.put("/:id/read", auth_1.auth, user_invitation_controller_1.default.markAsRead);
// New endpoints for enhanced invitation functionality  
router.get("/user/:userId", auth_1.auth, user_invitation_controller_1.default.getUserProfileForInvitation);
router.get("/:id/contact", (0, authWithSubscription_1.checkActionPermission)('contact_viewed'), user_invitation_controller_1.default.requestContactNumber);
router.get("/:id/cancel", auth_1.auth, user_invitation_controller_1.default.decline);
router.get("/:id/remove-friend", auth_1.auth, user_invitation_controller_1.default.removeFriend);
exports.default = router;
