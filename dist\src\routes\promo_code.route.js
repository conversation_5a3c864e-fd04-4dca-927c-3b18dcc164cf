"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const promo_code_controller_1 = __importDefault(require("../app/promo_code/promo_code.controller"));
const auth_1 = require("../middlewares/auth");
const middleware_1 = require("../middlewares/middleware");
const promo_code_validation_1 = require("../validations/promo_code.validation");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.auth);
// Validate promo code
router.post("/validate", (0, middleware_1.validate)(promo_code_validation_1.validatePromoCodeValidation), promo_code_controller_1.default.validatePromoCode);
// Get user's promo code usage history
router.get("/usage-history", promo_code_controller_1.default.getUserPromoCodeUsage);
// Get promo code by code (public info)
router.get("/:code", (0, middleware_1.validate)(promo_code_validation_1.getPromoCodeByCodeValidation), promo_code_controller_1.default.getPromoCodeByCode);
exports.default = router;
