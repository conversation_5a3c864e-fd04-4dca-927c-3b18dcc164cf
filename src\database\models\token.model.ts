import {
  AllowNull,
  AutoIncrement,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  NotEmpty,
  PrimaryKey,
  Table
} from "sequelize-typescript";
import User from "./user.model";

export interface TokenI {
  id: number;
  user_id: number;
  token: string;
  type: string;
  expires_at: Date;
  blacklisted: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

@Table({
  tableName: "tokens",
  timestamps: true
})
class Token extends Model implements TokenI {
  @AutoIncrement
  @PrimaryKey
  @Column
  id: number;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column
  user_id: number;

  @AllowNull(false)
  @NotEmpty
  @Column(DataType.TEXT)
  token: string;

  @AllowNull(true)
  @Column(DataType.STRING)
  type: string;

  @AllowNull(false)
  @NotEmpty
  @Column(DataType.DATE)
  expires_at: Date;

  @AllowNull(false)
  @NotEmpty
  @Column(DataType.BOOLEAN)
  blacklisted: boolean;

  @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
  user: User;
}

export default Token;