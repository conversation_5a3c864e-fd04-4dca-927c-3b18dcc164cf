import { Request, Response } from "express";
import httpStatus from "http-status";
import jwt from "jsonwebtoken";
import RoleService from "./role.service";
import catchAsync from "../../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../../utils/response";
import httpMessages from "../../../config/httpMessages";
import ApiError from "../../../utils/ApiError";
import Role from "../../../database/models/role.model";

export default class RoleController {
  static roleService = RoleService;
  constructor() {}

  static getAll = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search } = request.query;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
      };
      const roles = await this.roleService.getRoles(option);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.ROLES.SUCCESS,
        data: roles,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const adminData = { ...request.body };
      const admin: Role = await this.roleService.createRole(adminData);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.ROLES.ADD_SUCCESS,
        data: null,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static showById = catchAsync(async (request: Request, response: Response) => {
    try {
      const roleId: number = parseInt(request.params.id, 10);
      const role = await this.roleService.getRoleById(roleId);
      if (!role) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.ROLES.NOT_FOUND);
      }
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.ROLES.DETAILS.SUCCESS,
        data: role,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static update = catchAsync(async (request: Request, response: Response) => {
    try {
      const roleId: number = parseInt(request.params.id, 10);
      const roleData = { ...request.body };

      const role = await this.roleService.updateRoleById(roleId, roleData);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.ROLES.UPDATE_SUCCESS,
        data: role,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static delete = catchAsync(async (request: Request, response: Response) => {
    try {
      const id: number = parseInt(request.params.id, 10);
      await this.roleService.deleteUserById(id);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.ROLES.DELETE,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });
}
