"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateAdminValidation = exports.createAdminValidation = exports.adminloginValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.adminloginValidation = {
    body: joi_1.default.object().keys({
        username: joi_1.default.string().trim().required().messages({
            "string.empty": "User Name is required.",
            "string.base": "User Name must be a string.",
        }),
        password: joi_1.default.string().trim().required().messages({
            "string.empty": "Password is required.",
            "string.base": "Password must be a string.",
        }),
    }),
};
exports.createAdminValidation = {
    body: joi_1.default.object().keys({
        first_name: joi_1.default.string().trim().required().messages({
            "string.empty": "First name is required.",
            "string.base": "First name must be a string.",
        }),
        last_name: joi_1.default.string().trim().required().messages({
            "string.empty": "Last name is required.",
            "string.base": "Last name must be a string.",
        }),
        email: joi_1.default.string().trim().email().required().messages({
            "string.empty": "Email is required.",
            "string.email": "Email must be a valid email address.",
        }),
        mobile_number: joi_1.default.string()
            .trim()
            .pattern(/^[0-9]{7,20}$/)
            .required()
            .messages({
            "string.empty": "Mobile number is required.",
            "string.pattern.base": "Mobile number must be numeric and between 7 to 20 digits.",
        }),
        username: joi_1.default.string().trim().required().messages({
            "string.empty": "Username is required.",
            "string.base": "Username must be a string.",
        }),
        password: joi_1.default.string().trim().min(6).required().messages({
            "string.empty": "Password is required.",
            "string.min": "Password must be at least 6 characters long.",
        }),
        role_id: joi_1.default.number().required().messages({
            "number.base": "ID must be a number.",
            "any.required": "ID is required.",
        }),
        is_super_admin: joi_1.default.boolean().optional().allow(null, ""),
    }),
};
exports.updateAdminValidation = {
    body: joi_1.default.object().keys({
        first_name: joi_1.default.string().trim().required().messages({
            "string.empty": "First name is required.",
            "string.base": "First name must be a string.",
        }),
        last_name: joi_1.default.string().trim().required().messages({
            "string.empty": "Last name is required.",
            "string.base": "Last name must be a string.",
        }),
        email: joi_1.default.string().trim().email().required().messages({
            "string.empty": "Email is required.",
            "string.email": "Email must be a valid email address.",
        }),
        mobile_number: joi_1.default.string()
            .trim()
            .pattern(/^[0-9]{7,20}$/)
            .required()
            .messages({
            "string.empty": "Mobile number is required.",
            "string.pattern.base": "Mobile number must be numeric and between 7 to 20 digits.",
        }),
        username: joi_1.default.string().trim().required().messages({
            "string.empty": "Username is required.",
            "string.base": "Username must be a string.",
        }),
        password: joi_1.default.string().trim().min(6).optional().messages({
            "string.empty": "Password is required.",
            "string.min": "Password must be at least 6 characters long.",
        }),
        role_id: joi_1.default.number().required().messages({
            "number.base": "ID must be a number.",
            "any.required": "ID is required.",
        }),
        permissions: joi_1.default.object()
            .pattern(joi_1.default.string(), joi_1.default.object().pattern(joi_1.default.string(), joi_1.default.boolean()).unknown(true))
            .optional()
            .allow(null)
            .messages({
            "object.base": "Permissions must be an object with proper structure.",
        }),
        is_super_admin: joi_1.default.boolean().optional().allow(null, ""),
    }),
};
//  permissions: Joi.object()
//       .pattern(
//         Joi.string(),
//         Joi.object().pattern(Joi.string(), Joi.boolean()).unknown(true)
//       )
//       .optional()
//       .allow(null)
//       .messages({
//         "object.base": "Permissions must be an object with proper structure.",
//       }),
