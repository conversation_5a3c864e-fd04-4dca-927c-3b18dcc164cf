import express from "express";
import { auth } from "../middlewares/auth";
import Chat<PERSON>ontroller from "../app/chat/chat.controller";
import MessageController from "../app/message/message.controller";
import { checkActionPermission } from "../middlewares/authWithSubscription";

const router = express.Router();

// Apply auth middleware to all chat routes
router.use(auth);

// Chat routes
router.get("/", auth, ChatController.getUserChats);
router.post("/", ChatController.createChat);
router.get("/:id", auth,checkActionPermission('chat_initiated'), ChatController.getChatById);
router.get("/:id/messages", ChatController.getChatMessages);
router.put("/:id/auto-delete", ChatController.setAutoDeleteDays);
router.delete("/:id", ChatController.deleteChat);

// Message-related routes that operate on chats
router.put("/:id/read-all", MessageController.markAllAsRead);

export default router;
