import {
    Table,
    Column,
    Model,
    DataType,
    PrimaryKey,
    AutoIncrement,
    AllowNull,
    Default,
    ForeignKey,
    BelongsTo,
} from "sequelize-typescript";
import Admin from "./admin.model";

export interface InquiryI {
    id: number;
    name: string;
    email: string;
    subject: string;
    message: string;
    status: 'pending' | 'in_progress' | 'resolved' | 'closed';
    admin_notes?: string;
    changed_by_admin_id?: number;
    changed_at?: Date;
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: "inquiries",
    timestamps: true,
})
class Inquiry extends Model<InquiryI> implements InquiryI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    name: string;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    email: string;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    subject: string;

    @AllowNull(false)
    @Column(DataType.TEXT)
    message: string;

    @AllowNull(false)
    @Default('pending')
    @Column(DataType.ENUM('pending', 'in_progress', 'resolved', 'closed'))
    status: 'pending' | 'in_progress' | 'resolved' | 'closed';

    @AllowNull(true)
    @Column(DataType.TEXT)
    admin_notes?: string;

    @ForeignKey(() => Admin)
    @AllowNull(true)
    @Column(DataType.INTEGER)
    changed_by_admin_id?: number;

    @BelongsTo(() => Admin, { foreignKey: "changed_by_admin_id", onDelete: "SET NULL", })
    changedByAdmin: Admin;

    @AllowNull(true)
    @Column(DataType.DATE)
    changed_at?: Date;
}

export default Inquiry;
