"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../../config/httpMessages"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const user_gallery_model_1 = __importDefault(require("../../../database/models/user_gallery.model"));
class UserGalleryService {
    constructor() { }
}
_a = UserGalleryService;
/**
 * Create a UserGallery
 * @param {Object} body
 * @returns {Promise<UserGallery>}
 */
UserGalleryService.createUserGallery = (body, userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const gallery_images = Object.keys(body)
            .filter((key) => key.startsWith("gallery_image["))
            .sort((a, b) => {
            var _b, _c;
            const indexA = parseInt(((_b = a.match(/\[(\d+)\]/)) === null || _b === void 0 ? void 0 : _b[1]) || "0", 10);
            const indexB = parseInt(((_c = b.match(/\[(\d+)\]/)) === null || _c === void 0 ? void 0 : _c[1]) || "0", 10);
            return indexA - indexB;
        })
            .map((key) => body[key]);
        if (gallery_images.length) {
            let payload = [];
            for (let index = 0; index < gallery_images.length; index++) {
                const element = gallery_images[index];
                payload.push({
                    user_id: userId,
                    gallery_image: element,
                });
            }
            yield user_gallery_model_1.default.bulkCreate(payload);
        }
        return true;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Return UserProfileBios
 * @param {Object} options
 * @param {number} [options.page] - Current page number (optional)
 * @param {number} [options.limit] - Number of items per page (optional)
 * @param {string} [options.search] - Search term for filtering (optional)
 * @returns {Promise<UserGallery[]>}
 */
UserGalleryService.getUserGalleryImages = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, currentUserId } = options;
        const whereCondition = {};
        if (currentUserId) {
            whereCondition.user_id = currentUserId;
        }
        const queryOption = {
            where: whereCondition,
        };
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const data = yield user_gallery_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: data.count,
                totalPages: Math.ceil(data.count / limit),
                currentPage: page,
                user_gallery: data.rows,
            };
        }
        else {
            return data.rows;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get UserGallery by id
 * @param {Number} id
 * @returns {Promise<UserGallery>}
 */
UserGalleryService.getUserGalleryById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return user_gallery_model_1.default.findOne({
            where: { id },
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update role by id
 * @param {Number} Id
 * @param {Object} updateBody
 * @returns {Promise<Role>}
 */
UserGalleryService.updateUserGalleryById = (Id, updateBody) => __awaiter(void 0, void 0, void 0, function* () {
    const details = yield user_gallery_model_1.default.findByPk(Id);
    if (!details) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER_GALLERY.NOT_FOUND);
    }
    Object.assign(details, updateBody);
    yield details.save();
    return details;
});
/**
 * Delete role by id
 * @param {Number} Id
 * @returns {Promise<Role>}
 */
UserGalleryService.deleteUserGalleryById = (Id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const details = yield user_gallery_model_1.default.findByPk(Id);
        if (!details) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER_GALLERY.NOT_FOUND);
        }
        if (details.gallery_image) {
            const fullPath = path_1.default.join(__dirname, "../../../../uploads", details.gallery_image); // adjust path as needed
            console.log("fullPath: ", fullPath);
            if (fs_1.default.existsSync(fullPath)) {
                fs_1.default.unlinkSync(fullPath); // delete the file
            }
        }
        yield details.destroy();
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting Role.");
    }
});
exports.default = UserGalleryService;
