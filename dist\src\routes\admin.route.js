"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const admin_controller_1 = __importDefault(require("../app/admin/admin.controller"));
const auth_1 = require("../middlewares/auth");
const middleware_1 = require("../middlewares/middleware");
const admin_validation_1 = require("../validations/admin.validation");
const router = express_1.default.Router();
router.post("/login", (0, middleware_1.validate)(admin_validation_1.adminloginValidation), admin_controller_1.default.login);
router.get("/", auth_1.auth, admin_controller_1.default.getAll);
router.post("/", auth_1.auth, (0, middleware_1.validate)(admin_validation_1.createAdminValidation), admin_controller_1.default.create);
router.get("/:adminId", auth_1.auth, admin_controller_1.default.showById);
router.put("/:adminId", auth_1.auth, (0, middleware_1.validate)(admin_validation_1.updateAdminValidation), admin_controller_1.default.update);
router.delete("/:adminId", auth_1.auth, admin_controller_1.default.delete);
exports.default = router;
