import {
    AllowNull,
    AutoIncrement,
    BelongsTo,
    Column,
    DataType,
    ForeignKey,
    Model,
    PrimaryKey,
    Table
} from "sequelize-typescript";
import User from "./user.model";
import PromoCode from "./promo_codes.model";
import UserSubscription from "./user_subscriptions.model";

export interface PromoCodeUsageI {
    id: number;
    promo_code_id: number;
    user_id: number;
    subscription_id?: number;
    discount_amount: number;
    original_amount: number;
    final_amount: number;
    used_at: Date;
    
    // Timestamps
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: "promo_code_usage",
    timestamps: true
})
class PromoCodeUsage extends Model<PromoCodeUsageI> implements PromoCodeUsageI {
    @AutoIncrement
    @PrimaryKey
    @Column
    id: number;

    @ForeignKey(() => PromoCode)
    @AllowNull(false)
    @Column
    promo_code_id: number;

    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @ForeignKey(() => UserSubscription)
    @AllowNull(true)
    @Column
    subscription_id?: number;

    @AllowNull(false)
    @Column(DataType.DECIMAL(10, 2))
    discount_amount: number;

    @AllowNull(false)
    @Column(DataType.DECIMAL(10, 2))
    original_amount: number;

    @AllowNull(false)
    @Column(DataType.DECIMAL(10, 2))
    final_amount: number;

    @AllowNull(false)
    @Column(DataType.DATE)
    used_at: Date;

    // Associations
    @BelongsTo(() => PromoCode, { foreignKey: "promo_code_id" })
    promo_code: PromoCode;

    @BelongsTo(() => User, { foreignKey: "user_id" })
    user: User;

    @BelongsTo(() => UserSubscription, { foreignKey: "subscription_id" })
    subscription: UserSubscription;
}

export default PromoCodeUsage;
