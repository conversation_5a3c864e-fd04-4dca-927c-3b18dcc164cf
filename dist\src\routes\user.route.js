"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const user_controller_1 = __importDefault(require("../app/user/user.controller"));
const auth_1 = require("../middlewares/auth");
const fileUploadMiddleware_1 = require("../middlewares/fileUploadMiddleware");
const router = express_1.default.Router();
// router.post("/login", validate(loginValidation), UserController.login);
router.get("", user_controller_1.default.getAll);
router.post("/complete_profile", auth_1.auth, fileUploadMiddleware_1.fileUploadMiddleware, user_controller_1.default.completeProfile);
router.get("/:userId", auth_1.auth, user_controller_1.default.showById);
router.put("/:userId", auth_1.auth, fileUploadMiddleware_1.fileUploadMiddleware, user_controller_1.default.update);
router.delete("/:userId", auth_1.auth, user_controller_1.default.delete);
router.post("/settings/change_email", auth_1.auth, user_controller_1.default.changeEmail);
router.post("/settings/change_password", auth_1.auth, user_controller_1.default.updatePassword);
exports.default = router;
