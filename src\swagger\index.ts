// swaggerSpec.ts
import { Request, Response, NextFunction } from "express";
import path from "path";
import swagger<PERSON>SD<PERSON> from "swagger-jsdoc";
import { SwaggerOptions } from "swagger-ui-express";
import basicAuth from "basic-auth";

const swaggerAuth = (req: Request, res: Response, next: NextFunction) => {
  const user = basicAuth(req);

  // Replace 'admin' and 'password123' with your desired credentials
  const username = "admin";
  const password = "admin@123";

  if (!user || user.name !== username || user.pass !== password) {
    res.set("WWW-Authenticate", 'Basic realm="Swagger API Docs"');
    res.status(401).send("Authentication required.");
    return;
  }

  next();
};

/**
 * Swagger definition for API documentation
 */
const swaggerDefinition = {
  openapi: "3.0.0", // OpenAPI version
  info: {
    title: "BarBadhu API", // Title of the API
    version: "1.0.0", // API version
    description: "API documentation for the BarBadhu System", // API description
  },
  servers: [
    {
      url: `http://${process.env.HOST}:${process.env.PORT}`, // The base URL of the API
    },
  ],
  tags: [

    {
      name: "Auth",
      description: "Auth related operations",
    },
    {
      name: "User",
      description: "User related operations",
    },
    {
      name: "PrivacySettings",
      description: "PrivacySettings related operations",
    },
    {
      name: "Geolocation",
      description: "Geolocation related operations (countries with states and cities)",
    },
    {
      name: "User Profile",
      description: "User Profile related operations",
    },
    {
      name: "Admin",
      description: "Admin related operations",
    },
    {
      name: "SubscriptionPlans",
      description: "Subscription plans related operations",
    },
    {
      name: "UserSubscriptions",
      description: "User subscriptions related operations",
    },
    {
      name: "Socket.io",
      description: "Real-time communication using Socket.io",
    },
    {
      name: "Chat",
      description: "Chat related operations",
    },
    {
      name: "Messages",
      description: "Message related operations",
    },
  ],
  components: {
    schemas: {
      EncryptedResponse: {
        type: "object",
        properties: {
          encryptedData: {
            type: "string",
            example: "h&EKZdsBUkRaG...encryptedMessageHere",
          },
        },
      },
    },
    securitySchemes: {
      BearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT", // JWT format
      },
    },
  },
  security: [
    {
      BearerAuth: [],
    },
  ],
};

/**
 * Swagger options for swagger-jsdoc
 */
const options = {
  swaggerDefinition,
  apis: [
    path.join(__dirname, "/*.ts"), // Load separate JSDoc comment files
    path.join(__dirname, "/*.js"), // Load separate JSDoc comment files
  ],
};

const swaggerUiOptions: SwaggerOptions = {
  swaggerOptions: {
    persistAuthorization: true, // Enable token persistence
  },
};

const swaggerSpec = swaggerJSDoc(options);

export { swaggerSpec, swaggerUiOptions, swaggerAuth };
