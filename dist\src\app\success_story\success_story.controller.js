"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const response_1 = __importStar(require("../../utils/response"));
const success_story_service_1 = __importDefault(require("./success_story.service"));
const success_story_model_1 = __importDefault(require("../../database/models/success_story.model"));
const user_model_1 = __importDefault(require("../../database/models/user.model"));
const success_story_media_model_1 = __importDefault(require("../../database/models/success_story_media.model"));
class SuccessStoryController {
    constructor() { }
}
_a = SuccessStoryController;
SuccessStoryController.successStoryService = success_story_service_1.default;
SuccessStoryController.getAll = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, status } = request.query;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search ? search : "",
            status: status ? status : undefined,
        };
        const list = yield _a.successStoryService.getSuccessStories(option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Success stories retrieved successfully",
            data: list,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
SuccessStoryController.getApproved = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit } = request.query;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
        };
        const list = yield _a.successStoryService.getApprovedSuccessStories(option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Approved success stories retrieved successfully",
            data: list,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
SuccessStoryController.getUserStories = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const { page, limit, search, status } = request.query;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search ? search : "",
            status: status ? status : undefined,
        };
        const list = yield _a.successStoryService.getUserSuccessStories(userId, option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "User success stories retrieved successfully",
            data: list,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
SuccessStoryController.create = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const body = Object.assign(Object.assign({}, request.body), { user_id: userId });
        // Handle media files
        const mediaFiles = [];
        if (request.body.media_files) {
            if (Array.isArray(request.body.media_files)) {
                mediaFiles.push(...request.body.media_files);
            }
            else {
                mediaFiles.push(request.body.media_files);
            }
        }
        const createdData = yield _a.successStoryService.createSuccessStory(body, mediaFiles);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Success story submitted successfully",
            data: createdData,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
SuccessStoryController.showById = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        let { id } = request.params;
        if (id === "show") {
            const userId = request.decoded;
            const successStory = yield success_story_model_1.default.findOne({
                where: { user_id: userId },
                include: [
                    {
                        model: user_model_1.default,
                        attributes: ["id", "first_name", "last_name", "email"],
                    },
                    {
                        model: success_story_media_model_1.default,
                        attributes: ["id", "media_type", "media_path"],
                    },
                ],
            });
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.OK,
                message: "Success story retrieved successfully",
                data: successStory,
            });
        }
        const data = yield _a.successStoryService.getSuccessStoryById(id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Success story retrieved successfully",
            data: data,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
SuccessStoryController.updateStatus = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = parseInt(request.params.id, 10);
        const body = Object.assign({}, request.body);
        const updatedData = yield _a.successStoryService.updateSuccessStoryStatus(id, body);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: `Success story ${body.status} successfully`,
            data: updatedData,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
SuccessStoryController.delete = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = parseInt(request.params.id, 10);
        yield _a.successStoryService.deleteSuccessStory(id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Success story deleted successfully",
            data: { id },
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = SuccessStoryController;
