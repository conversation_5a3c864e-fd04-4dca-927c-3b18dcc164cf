import express from "express";
import UserController from "../app/user/user.controller";
// import { auth } from "../middlewares/auth";
import { validate } from "../middlewares/middleware";
// import { loginValidation } from "../validations/auth.validation";
import { userValidation } from "../validations/user.validation";
import UserProfileController from "../app/user/user_profile/user_profile.controller";
import { auth } from "../middlewares/auth";
import { fileUploadMiddleware } from "../middlewares/fileUploadMiddleware";


const router = express.Router();
// router.post("/login", validate(loginValidation), UserController.login);

router.get("", UserController.getAll);
router.post("/complete_profile",auth,fileUploadMiddleware, UserController.completeProfile);

router.get("/:userId", auth, UserController.showById);
router.put("/:userId", auth,fileUploadMiddleware, UserController.update);
router.delete("/:userId", auth, UserController.delete);


router.post("/settings/change_email",auth, UserController.changeEmail);
router.post("/settings/change_password",auth, UserController.updatePassword);

export default router;
