"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const paypal_controller_1 = __importDefault(require("../app/paypal/paypal.controller"));
const auth_1 = require("../middlewares/auth");
const middleware_1 = require("../middlewares/middleware");
const paypal_validation_1 = require("../validations/paypal.validation");
const router = express_1.default.Router();
// Public webhook endpoint (no auth required)
router.post("/webhook", paypal_controller_1.default.handleWebhook);
// Success and cancel handlers (no auth required)
router.get("/success", paypal_controller_1.default.handleSuccess);
router.get("/cancel", paypal_controller_1.default.handleCancel);
// Protected routes (require authentication)
router.post("/create-order", auth_1.auth, (0, middleware_1.validate)(paypal_validation_1.createOrderValidation), paypal_controller_1.default.createOrder);
router.post("/capture/:order_id", auth_1.auth, (0, middleware_1.validate)(paypal_validation_1.capturePaymentValidation), paypal_controller_1.default.capturePayment);
router.get("/payment/:payment_id", auth_1.auth, (0, middleware_1.validate)(paypal_validation_1.getPaymentDetailsValidation), paypal_controller_1.default.getPaymentDetails);
router.get("/payments", auth_1.auth, (0, middleware_1.validate)(paypal_validation_1.getUserPaymentsValidation), paypal_controller_1.default.getUserPayments);
// Admin routes (require authentication - you may want to add admin role check)
router.get("/transactions", auth_1.auth, paypal_controller_1.default.getTransactions);
router.get("/analytics", auth_1.auth, (0, middleware_1.validate)(paypal_validation_1.getPaymentAnalyticsValidation), paypal_controller_1.default.getPaymentAnalytics);
router.post("/refund/:payment_id", auth_1.auth, (0, middleware_1.validate)(paypal_validation_1.refundPaymentValidation), paypal_controller_1.default.refundPayment);
exports.default = router;
