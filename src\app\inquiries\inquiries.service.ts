 import httpStatus from "http-status";
import { Op } from "sequelize";
import Inquiry from "../../database/models/inquiries.model";
import ApiError from "../../utils/ApiError";
import httpMessages from "../../config/httpMessages";

export default class InquiryService {
  constructor() { }

  /**
   * Create a Inquiry
   * @param {Object} body
   * @returns {Promise<Inquiry>}
   */
  static createInquiry = async (body: any) => {
    try {
      const inquiry: Inquiry = await Inquiry.create(body);

      return inquiry;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get all inquiries with pagination and search
   * @param {Object} options - Query options
   * @returns {Promise<Object>}
   */
  static getInquiries = async (options: any) => {
    try {
      const { page = 1, limit = 10, search = "", status } = options;
      const offset = (page - 1) * limit;

      const whereClause: any = {};
      if (status) {
        whereClause.status = status;
      }
      if (search) {
        whereClause[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { email: { [Op.like]: `%${search}%` } },
          { subject: { [Op.like]: `%${search}%` } },
          { message: { [Op.like]: `%${search}%` } },
        ];
      }

      const { count, rows } = await Inquiry.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [["createdAt", "DESC"]],
      });
      if (page && limit) {
        return {
          totalItems: count,
          totalPages: Math.ceil(count / limit),
          currentPage: page,
          inquiries: rows,
        };
      } else {
        return rows;
      }
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get inquiry by id
   * @param {Number} id
   * @returns {Promise<Inquiry>}
   */
  static getInquiryById = async (id: number) => {
    try {
      return Inquiry.findOne({
        where: { id },
      }).then((data: any) => data?.toJSON());
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Update inquiry status
   * @param {Number} id
   * @param {String} status
   * @param {String} admin_notes
   * @returns {Promise<Inquiry>}
   */
  static updateInquiryStatus = async (id: number, status: string, admin_notes: string, changed_by_admin_id: number) => {
    try {
      const inquiry : any = await Inquiry.findByPk(id);
      if (!inquiry) {
        throw new ApiError(httpStatus.NOT_FOUND, "Inquiry not found");
      }
      if (!['pending', 'in_progress', 'resolved', 'closed'].includes(status)) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Invalid status");
      }

      inquiry.status = status;
      inquiry.admin_notes = admin_notes;
      inquiry.changed_by_admin_id = changed_by_admin_id; 
      inquiry.changed_at = new Date();
      await inquiry.save();
      return inquiry;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Delete inquiry
   * @param {Number} id
   * @returns {Promise<Inquiry>}
   */
  static deleteInquiry = async (id: number) => {
    try {
      const inquiry = await Inquiry.findByPk(id);
      if (!inquiry) {
        throw new ApiError(httpStatus.NOT_FOUND, "Inquiry not found");
      }
      await inquiry.destroy();
      return inquiry;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };
}







