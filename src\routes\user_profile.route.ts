import express from "express";
import UserProfileController from "../app/user/user_profile/user_profile.controller";
import { auth } from "../middlewares/auth";
import { fileUploadMiddleware } from "../middlewares/fileUploadMiddleware";
import { authWithSubscription, checkActionPermission } from "../middlewares/authWithSubscription";


const router = express.Router();

router.get("",auth, UserProfileController.getAll);
router.post("",auth, UserProfileController.create);
router.get("/view/:id", auth,checkActionPermission('profile_viewed'), UserProfileController.viewProfile);
router.get("/user_search", UserProfileController.getUserSearch);
router.post("/profile_bio",auth,fileUploadMiddleware, UserProfileController.createProfile);
router.get("/:userId", auth, UserProfileController.showById);

export default router;
