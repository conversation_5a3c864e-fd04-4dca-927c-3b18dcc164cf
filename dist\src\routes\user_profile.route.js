"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const user_profile_controller_1 = __importDefault(require("../app/user/user_profile/user_profile.controller"));
const auth_1 = require("../middlewares/auth");
const fileUploadMiddleware_1 = require("../middlewares/fileUploadMiddleware");
const router = express_1.default.Router();
router.get("", auth_1.auth, user_profile_controller_1.default.getAll);
router.post("", auth_1.auth, user_profile_controller_1.default.create);
router.get("/view/:id", auth_1.auth, user_profile_controller_1.default.viewProfile);
router.get("/user_search", user_profile_controller_1.default.getUserSearch);
router.post("/profile_bio", auth_1.auth, fileUploadMiddleware_1.fileUploadMiddleware, user_profile_controller_1.default.createProfile);
router.get("/:userId", auth_1.auth, user_profile_controller_1.default.showById);
exports.default = router;
