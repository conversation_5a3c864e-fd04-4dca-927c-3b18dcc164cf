import Joi from "joi";

export const initiatePaymentValidation = {
  body: Joi.object().keys({
    plan_id: Joi.number().integer().positive().optional().messages({
      "number.base": "Plan ID must be a number.",
      "number.integer": "Plan ID must be an integer.",
      "number.positive": "Plan ID must be a positive number.",
    }),
    amount: Joi.number().positive().when('plan_id', {
      is: Joi.exist(),
      then: Joi.optional(),
      otherwise: Joi.required()
    }).messages({
      "number.base": "Amount must be a number.",
      "number.positive": "Amount must be a positive number.",
      "any.required": "Amount is required when plan_id is not provided.",
    }),
    mobile: Joi.string().pattern(/^98\d{8}$/).optional().messages({
      "string.base": "Mobile must be a string.",
      "string.pattern.base": "Mobile must be a valid Nepali mobile number (98XXXXXXXX).",
    }),
    product_identity: Joi.string().max(100).optional().messages({
      "string.base": "Product identity must be a string.",
      "string.max": "Product identity cannot exceed 100 characters.",
    }),
    product_name: Joi.string().max(255).optional().messages({
      "string.base": "Product name must be a string.",
      "string.max": "Product name cannot exceed 255 characters.",
    }),
    product_url: Joi.string().uri().optional().messages({
      "string.base": "Product URL must be a string.",
      "string.uri": "Product URL must be a valid URI.",
    }),
    return_url: Joi.string().uri().optional().messages({
      "string.base": "Return URL must be a string.",
      "string.uri": "Return URL must be a valid URI.",
    }),
    website_url: Joi.string().uri().optional().messages({
      "string.base": "Website URL must be a string.",
      "string.uri": "Website URL must be a valid URI.",
    }),
  }),
};

export const verifyPaymentValidation = {
  body: Joi.object().keys({
    pidx: Joi.string().required().messages({
      "string.base": "Payment index (pidx) must be a string.",
      "any.required": "Payment index (pidx) is required.",
    }),
  }),
};

export const getPaymentByIdValidation = {
  params: Joi.object().keys({
    payment_id: Joi.number().integer().positive().required().messages({
      "number.base": "Payment ID must be a number.",
      "number.integer": "Payment ID must be an integer.",
      "number.positive": "Payment ID must be a positive number.",
      "any.required": "Payment ID is required.",
    }),
  }),
};

export const getUserPaymentsValidation = {
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).optional().default(1).messages({
      "number.base": "Page must be a number.",
      "number.integer": "Page must be an integer.",
      "number.min": "Page must be at least 1.",
    }),
    limit: Joi.number().integer().min(1).max(100).optional().default(10).messages({
      "number.base": "Limit must be a number.",
      "number.integer": "Limit must be an integer.",
      "number.min": "Limit must be at least 1.",
      "number.max": "Limit cannot exceed 100.",
    }),
    status: Joi.string().valid("pending", "completed", "failed", "cancelled", "refunded", "expired").optional().messages({
      "string.base": "Status must be a string.",
      "any.only": "Status must be one of: pending, completed, failed, cancelled, refunded, expired.",
    }),
  }),
};

export const getTransactionsValidation = {
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).optional().default(1).messages({
      "number.base": "Page must be a number.",
      "number.integer": "Page must be an integer.",
      "number.min": "Page must be at least 1.",
    }),
    limit: Joi.number().integer().min(1).max(100).optional().default(10).messages({
      "number.base": "Limit must be a number.",
      "number.integer": "Limit must be an integer.",
      "number.min": "Limit must be at least 1.",
      "number.max": "Limit cannot exceed 100.",
    }),
    status: Joi.string().valid("pending", "completed", "failed", "cancelled", "refunded", "expired").optional().messages({
      "string.base": "Status must be a string.",
      "any.only": "Status must be one of: pending, completed, failed, cancelled, refunded, expired.",
    }),
    search: Joi.string().max(255).optional().messages({
      "string.base": "Search must be a string.",
      "string.max": "Search cannot exceed 255 characters.",
    }),
  }),
};

export const getAnalyticsValidation = {
  query: Joi.object().keys({
    start_date: Joi.date().iso().optional().messages({
      "date.base": "Start date must be a valid date.",
      "date.format": "Start date must be in ISO format (YYYY-MM-DD).",
    }),
    end_date: Joi.date().iso().min(Joi.ref('start_date')).optional().messages({
      "date.base": "End date must be a valid date.",
      "date.format": "End date must be in ISO format (YYYY-MM-DD).",
      "date.min": "End date must be after start date.",
    }),
    user_id: Joi.number().integer().positive().optional().messages({
      "number.base": "User ID must be a number.",
      "number.integer": "User ID must be an integer.",
      "number.positive": "User ID must be a positive number.",
    }),
  }),
};

export const checkPaymentStatusValidation = {
  params: Joi.object().keys({
    pidx: Joi.string().required().messages({
      "string.base": "Payment index (pidx) must be a string.",
      "any.required": "Payment index (pidx) is required.",
    }),
  }),
};

export const webhookValidation = {
  body: Joi.object().keys({
    event_id: Joi.string().optional(),
    type: Joi.string().required().messages({
      "string.base": "Event type must be a string.",
      "any.required": "Event type is required.",
    }),
    data: Joi.object().required().messages({
      "object.base": "Event data must be an object.",
      "any.required": "Event data is required.",
    }),
  }),
};
