"use strict";
/**
 * @swagger
 * /user_subscription:
 *   get:
 *     tags:
 *       - UserSubscriptions
 *     summary: Get all user subscriptions
 *     description: Retrieve a list of all user subscriptions with pagination.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           example: 1
 *         description: Page number for pagination.
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Number of records per page.
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for filtering subscriptions.
 *     responses:
 *       200:
 *         description: Successfully retrieved user subscriptions.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User subscriptions retrieved successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     count:
 *                       type: integer
 *                       example: 5
 *                     rows:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           user_id:
 *                             type: integer
 *                             example: 101
 *                           plan_id:
 *                             type: integer
 *                             example: 2
 *                           start_date:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-05-15T00:00:00.000Z"
 *                           end_date:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-06-15T00:00:00.000Z"
 *                           auto_renew:
 *                             type: boolean
 *                             example: true
 *                           issued_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-05-15T10:30:00.000Z"
 *                           expires_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-06-15T10:30:00.000Z"
 *                           revoked_at:
 *                             type: string
 *                             format: date-time
 *                             example: null
 *                           usage_limit:
 *                             type: integer
 *                             example: 100
 *                           usage_count:
 *                             type: integer
 *                             example: 25
 *                           is_active:
 *                             type: boolean
 *                             example: true
 *                           token:
 *                             type: string
 *                             example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                           updatedAt:
 *                             type: string
 *                             format: date-time
 *       401:
 *         description: Unauthorized - Authentication required.
 *       500:
 *         description: Internal server error.
 */
/**
 * @swagger
 * /user_subscription:
 *   post:
 *     tags:
 *       - UserSubscriptions
 *     summary: Create a new user subscription
 *     description: Create and store a new user subscription.
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - plan_id
 *             properties:
 *               user_id:
 *                 type: integer
 *                 example: 101
 *               plan_id:
 *                 type: integer
 *                 example: 2
 *               start_date:
 *                 type: string
 *                 format: date-time
 *                 example: "2023-05-15T00:00:00.000Z"
 *               end_date:
 *                 type: string
 *                 format: date-time
 *                 example: "2023-06-15T00:00:00.000Z"
 *               auto_renew:
 *                 type: boolean
 *                 example: true
 *               issued_at:
 *                 type: string
 *                 format: date-time
 *                 example: "2023-05-15T10:30:00.000Z"
 *               expires_at:
 *                 type: string
 *                 format: date-time
 *                 example: "2023-06-15T10:30:00.000Z"
 *               usage_limit:
 *                 type: integer
 *                 example: 100
 *               usage_count:
 *                 type: integer
 *                 example: 0
 *               is_active:
 *                 type: boolean
 *                 example: true
 *               token:
 *                 type: string
 *                 example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *     responses:
 *       200:
 *         description: User subscription created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User subscription created successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     user_id:
 *                       type: integer
 *                       example: 101
 *                     plan_id:
 *                       type: integer
 *                       example: 2
 *                     start_date:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-05-15T00:00:00.000Z"
 *                     end_date:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-06-15T00:00:00.000Z"
 *                     auto_renew:
 *                       type: boolean
 *                       example: true
 *                     issued_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-05-15T10:30:00.000Z"
 *                     expires_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-06-15T10:30:00.000Z"
 *                     revoked_at:
 *                       type: string
 *                       format: date-time
 *                       example: null
 *                     usage_limit:
 *                       type: integer
 *                       example: 100
 *                     usage_count:
 *                       type: integer
 *                       example: 0
 *                     is_active:
 *                       type: boolean
 *                       example: true
 *                     token:
 *                       type: string
 *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Bad request - Invalid input data.
 *       401:
 *         description: Unauthorized - Authentication required.
 *       500:
 *         description: Internal server error.
 */
/**
 * @swagger
 * /user_subscription/{id}:
 *   get:
 *     tags:
 *       - UserSubscriptions
 *     summary: Get a user subscription by ID
 *     description: Retrieve a specific user subscription by its ID.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *         description: The ID of the user subscription to retrieve.
 *     responses:
 *       200:
 *         description: Successfully retrieved the user subscription.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User subscription retrieved successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     user_id:
 *                       type: integer
 *                       example: 101
 *                     plan_id:
 *                       type: integer
 *                       example: 2
 *                     start_date:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-05-15T00:00:00.000Z"
 *                     end_date:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-06-15T00:00:00.000Z"
 *                     auto_renew:
 *                       type: boolean
 *                       example: true
 *                     issued_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-05-15T10:30:00.000Z"
 *                     expires_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-06-15T10:30:00.000Z"
 *                     revoked_at:
 *                       type: string
 *                       format: date-time
 *                       example: null
 *                     usage_limit:
 *                       type: integer
 *                       example: 100
 *                     usage_count:
 *                       type: integer
 *                       example: 25
 *                     is_active:
 *                       type: boolean
 *                       example: true
 *                     token:
 *                       type: string
 *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized - Authentication required.
 *       404:
 *         description: User subscription not found.
 *       500:
 *         description: Internal server error.
 */
/**
 * @swagger
 * /user_subscription/{id}:
 *   put:
 *     tags:
 *       - UserSubscriptions
 *     summary: Update a user subscription by ID
 *     description: Update an existing user subscription by its ID.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *         description: The ID of the user subscription to update.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - plan_id
 *             properties:
 *               user_id:
 *                 type: integer
 *                 example: 101
 *               plan_id:
 *                 type: integer
 *                 example: 3
 *               start_date:
 *                 type: string
 *                 format: date-time
 *                 example: "2023-06-01T00:00:00.000Z"
 *               end_date:
 *                 type: string
 *                 format: date-time
 *                 example: "2023-07-01T00:00:00.000Z"
 *               auto_renew:
 *                 type: boolean
 *                 example: false
 *               issued_at:
 *                 type: string
 *                 format: date-time
 *                 example: "2023-06-01T10:30:00.000Z"
 *               expires_at:
 *                 type: string
 *                 format: date-time
 *                 example: "2023-07-01T10:30:00.000Z"
 *               usage_limit:
 *                 type: integer
 *                 example: 150
 *               usage_count:
 *                 type: integer
 *                 example: 30
 *               is_active:
 *                 type: boolean
 *                 example: true
 *               token:
 *                 type: string
 *                 example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *     responses:
 *       200:
 *         description: User subscription updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User subscription updated successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     user_id:
 *                       type: integer
 *                       example: 101
 *                     plan_id:
 *                       type: integer
 *                       example: 3
 *                     start_date:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-06-01T00:00:00.000Z"
 *                     end_date:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-07-01T00:00:00.000Z"
 *                     auto_renew:
 *                       type: boolean
 *                       example: false
 *                     issued_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-06-01T10:30:00.000Z"
 *                     expires_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-07-01T10:30:00.000Z"
 *                     revoked_at:
 *                       type: string
 *                       format: date-time
 *                       example: null
 *                     usage_limit:
 *                       type: integer
 *                       example: 150
 *                     usage_count:
 *                       type: integer
 *                       example: 30
 *                     is_active:
 *                       type: boolean
 *                       example: true
 *                     token:
 *                       type: string
 *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Bad request - Invalid input data.
 *       401:
 *         description: Unauthorized - Authentication required.
 *       404:
 *         description: User subscription not found.
 *       500:
 *         description: Internal server error.
 */
/**
 * @swagger
 * /user_subscription/{id}:
 *   delete:
 *     tags:
 *       - UserSubscriptions
 *     summary: Delete a user subscription by ID
 *     description: Remove an existing user subscription by its ID.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *         description: The ID of the user subscription to delete.
 *     responses:
 *       200:
 *         description: User subscription deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User subscription deleted successfully.
 *                 data:
 *                   type: object
 *                   example: {}
 *       401:
 *         description: Unauthorized - Authentication required.
 *       404:
 *         description: User subscription not found.
 *       500:
 *         description: Internal server error.
 */
