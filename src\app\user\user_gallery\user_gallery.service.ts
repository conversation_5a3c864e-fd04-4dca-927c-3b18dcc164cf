import httpStatus from "http-status";
import { Op } from "sequelize";
import ApiError from "../../../utils/ApiError";
import httpMessages from "../../../config/httpMessages";
import fs from "fs";
import path from "path";
import UserGallery from "../../../database/models/user_gallery.model";

export default class UserGalleryService {
  constructor() {}

  /**
   * Create a UserGallery
   * @param {Object} body
   * @returns {Promise<UserGallery>}
   */
  static createUserGallery = async (body: any, userId: number) => {
    try {
      const gallery_images: string[] = Object.keys(body)
        .filter((key) => key.startsWith("gallery_image["))
        .sort((a, b) => {
          const indexA = parseInt(a.match(/\[(\d+)\]/)?.[1] || "0", 10);
          const indexB = parseInt(b.match(/\[(\d+)\]/)?.[1] || "0", 10);
          return indexA - indexB;
        })
        .map((key) => body[key]);

      if (gallery_images.length) {
        let payload: any = [];
        for (let index = 0; index < gallery_images.length; index++) {
          const element = gallery_images[index];
          payload.push({
            user_id: userId,
            gallery_image: element,
          });
        }
        await UserGallery.bulkCreate(payload);
      }
      return true;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Return UserProfileBios
   * @param {Object} options
   * @param {number} [options.page] - Current page number (optional)
   * @param {number} [options.limit] - Number of items per page (optional)
   * @param {string} [options.search] - Search term for filtering (optional)
   * @returns {Promise<UserGallery[]>}
   */
  static getUserGalleryImages = async (options: {
    page?: number;
    limit?: number;
    search?: string;
    currentUserId?: number;
  }) => {
    try {
      const { page, limit, search, currentUserId } = options;
      const whereCondition: any = {};

      if (currentUserId) {
        whereCondition.user_id = currentUserId;
      }
      const queryOption: any = {
        where: whereCondition,
      };
      // If pagination is provided, apply pagination
      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }
      const data = await UserGallery.findAndCountAll(queryOption);
      if (page && limit) {
        return {
          totalItems: data.count,
          totalPages: Math.ceil(data.count / limit),
          currentPage: page,
          user_gallery: data.rows,
        };
      } else {
        return data.rows;
      }
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get UserGallery by id
   * @param {Number} id
   * @returns {Promise<UserGallery>}
   */
  static getUserGalleryById = async (id: number) => {
    try {
      return UserGallery.findOne({
        where: { id },
      }).then((data: any) => data?.toJSON());
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Update role by id
   * @param {Number} Id
   * @param {Object} updateBody
   * @returns {Promise<Role>}
   */
  static updateUserGalleryById = async (Id: number, updateBody: any) => {
    const details = await UserGallery.findByPk(Id);
    if (!details) {
      throw new ApiError(
        httpStatus.NOT_FOUND,
        httpMessages.USER_GALLERY.NOT_FOUND
      );
    }

    Object.assign(details, updateBody);
    await details.save();
    return details;
  };

  /**
   * Delete role by id
   * @param {Number} Id
   * @returns {Promise<Role>}
   */
  static deleteUserGalleryById = async (Id: number) => {
    try {
      const details: any = await UserGallery.findByPk(Id);
      if (!details) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          httpMessages.USER_GALLERY.NOT_FOUND
        );
      }

      if (details.gallery_image) {
        const fullPath = path.join(
          __dirname,
          "../../../../uploads",
          details.gallery_image
        ); // adjust path as needed
        console.log("fullPath: ", fullPath);
        if (fs.existsSync(fullPath)) {
          fs.unlinkSync(fullPath); // delete the file
        }
      }

      await details.destroy();
      return details;
    } catch (error: any) {
      throw new ApiError(
        error.status || httpStatus.BAD_REQUEST,
        error.message || "Error deleting Role."
      );
    }
  };
}
