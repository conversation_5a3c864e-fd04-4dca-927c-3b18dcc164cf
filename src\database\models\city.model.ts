import {
  AllowNull,
  AutoIncrement,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Index,
  Model,
  NotEmpty,
  PrimaryKey,
  Table
} from "sequelize-typescript";
import Country from "./country.model";

export interface CityI {
  id: number;
  name: string;
  country_id: number;
}

@Table({
  tableName: "cities",
  timestamps: true
})
export default class City extends Model<CityI> implements CityI {
  @AutoIncrement
  @PrimaryKey
  @Column
  id: number;

  @AllowNull(false)
  @NotEmpty
  @Column(DataType.STRING)
  name: string;

  @ForeignKey(() => Country)
  @AllowNull(false)
  @Column(DataType.INTEGER)
  country_id: number;

  @BelongsTo(() => Country, { foreignKey: "country_id", onDelete: "CASCADE" })
  country: Country;
}
