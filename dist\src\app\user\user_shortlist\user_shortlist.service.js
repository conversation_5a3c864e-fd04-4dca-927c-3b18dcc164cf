"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const user_shortlist_model_1 = __importDefault(require("../../../database/models/user_shortlist.model"));
const user_basic_details_model_1 = __importDefault(require("../../../database/models/user_basic_details.model"));
const user_location_details_model_1 = __importDefault(require("../../../database/models/user_location_details.model"));
const user_education_career_model_1 = __importDefault(require("../../../database/models/user_education_career.model"));
const user_lifestyle_model_1 = __importDefault(require("../../../database/models/user_lifestyle.model"));
const user_family_details_model_1 = __importDefault(require("../../../database/models/user_family_details.model"));
const user_hobbies_model_1 = __importDefault(require("../../../database/models/user_hobbies.model"));
const user_gallery_model_1 = __importDefault(require("../../../database/models/user_gallery.model"));
const user_model_1 = __importDefault(require("../../../database/models/user.model"));
const country_model_1 = __importDefault(require("../../../database/models/country.model"));
const city_model_1 = __importDefault(require("../../../database/models/city.model"));
class UserShortlistService {
    constructor() { }
}
_a = UserShortlistService;
/**
* Return Roles
* @param {Object} options
* @param {number} [options.page] - Current page number (optional)
* @param {number} [options.limit] - Number of items per page (optional)
* @param {string} [options.search] - Search term for filtering (optional)
* @returns {Promise<Role[]>}
*/
UserShortlistService.getShortlists = (options, userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search } = options;
        // const whereCondition = search
        //     ? {
        //         [Op.or]: [
        //             { role_name: { [Op.like]: `%${search.toLowerCase()}%` } },
        //         ],
        //     }
        //     : {};
        // const queryOption: any = {
        //     where: { user_id: userId },
        //     order: [["createdAt", "DESC"]],
        // };
        // const queryOption: any = {
        //     where: { user_id: userId },
        //     distinct: true,
        //     include: [
        //         {
        //             model: User,
        //             as: "shortlisted_user",
        //             include: [
        //                 {
        //                     model: UserBasicDetails,
        //                     as: "basicDetails",
        //                     attributes: ["religion", "caste", "gotra", 'marital_status']
        //                 },
        //                 {
        //                     model: UserLocationDetails,
        //                     as: "locationDetails",
        //                     attributes: ["city", "country_living_in"]
        //                 },
        //                 {
        //                     model: UserEducationCareer,
        //                     as: "educationCareer",
        //                     attributes: ["education", "profession"]
        //                 },
        //                 {
        //                     model: UserLifestyle,
        //                     as: "lifestyle",
        //                     attributes: ["age", "height_cm"]
        //                 },
        //                 {
        //                     model: UserFamilyDetails,
        //                     as: "familyDetails",
        //                     attributes: ["family_type", "father_occupation", "mother_occupation"]
        //                 },
        //                 {
        //                     model: UserHobbies,
        //                     as: "hobbies",
        //                     attributes: ["hobbies", "interests"]
        //                 },
        //                 {
        //                     model: UserProfileBio,
        //                     as: "profileBioDetails",
        //                 },
        //             ]
        //         },
        //     ],
        //     // attributes: {
        //     //   include: [
        //     //     [
        //     //       Sequelize.literal("`lifestyle`.`age`"),
        //     //       "age",
        //     //     ],
        //     //     [
        //     //       Sequelize.literal("`lifestyle`.`height_cm`"),
        //     //       "height_cm",
        //     //     ],
        //     //   ],
        //     // },
        //     // include: [
        //     //   {
        //     //     model: UserBasicDetails,
        //     //     as: "basicDetails",
        //     //     attributes: ["religion", "caste", "gotra", 'marital_status']
        //     //   },
        //     //   {
        //     //     model: UserLocationDetails,
        //     //     as: "locationDetails",
        //     //     attributes: ["city", "country_living_in"]
        //     //   },
        //     //   {
        //     //     model: UserEducationCareer,
        //     //     as: "educationCareer",
        //     //     attributes: ["education", "profession"]
        //     //   },
        //     //   {
        //     //     model: UserLifestyle,
        //     //     as: "lifestyle",
        //     //     attributes: ["age", "height_cm"]
        //     //   },
        //     //   {
        //     //     model: UserFamilyDetails,
        //     //     as: "familyDetails",
        //     //     attributes: ["family_type", "father_occupation", "mother_occupation"]
        //     //   },
        //     //   {
        //     //     model: UserHobbies,
        //     //     as: "hobbies",
        //     //     attributes: ["hobbies", "interests"]
        //     //   },
        //     //   {
        //     //     model: UserProfileBio,
        //     //     as: "profileBioDetails",
        //     //   },
        //     //   {
        //     //     model: UserShortlist,
        //     //     as: "userShortlistDetails",
        //     //   },
        //     // ],
        //     order: [["createdAt", "DESC"]],
        // };
        const shortlistWhere = {};
        if (userId)
            shortlistWhere.user_id = userId;
        const queryOption = {
            // where: whereCondition,
            distinct: true,
            // attributes: {
            //   include: [
            //     [
            //       Sequelize.literal("`lifestyle`.`age`"),
            //       "age",
            //     ],
            //     [
            //       Sequelize.literal("`lifestyle`.`height_cm`"),
            //       "height_cm",
            //     ],
            //   ],
            // },
            include: [
                {
                    model: user_basic_details_model_1.default,
                    as: "basicDetails",
                    attributes: ["religion", "caste", "gotra", 'marital_status']
                },
                {
                    model: user_location_details_model_1.default,
                    as: "locationDetails",
                    attributes: ["city", "country_living_in"],
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ['name'],
                        },
                        {
                            model: city_model_1.default,
                            as: "cities",
                            attributes: ['name'],
                        },
                    ],
                },
                {
                    model: user_education_career_model_1.default,
                    as: "educationCareer",
                    attributes: ["education", "profession"]
                },
                {
                    model: user_lifestyle_model_1.default,
                    as: "lifestyle",
                    attributes: ["age", "height_cm"]
                },
                {
                    model: user_family_details_model_1.default,
                    as: "familyDetails",
                    attributes: ["family_type", "father_occupation", "mother_occupation"]
                },
                {
                    model: user_hobbies_model_1.default,
                    as: "hobbies",
                    attributes: ["hobbies", "interests"]
                },
                {
                    model: user_gallery_model_1.default,
                    as: "userGallery",
                },
                {
                    model: user_shortlist_model_1.default,
                    where: Object.keys(shortlistWhere).length ? shortlistWhere : undefined,
                    as: "shortlisted_user"
                }
            ],
            order: [["createdAt", "DESC"]],
        };
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const shortlists = yield user_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: shortlists.count,
                totalPages: Math.ceil(shortlists.count / limit),
                currentPage: page,
                shortlists: shortlists.rows,
            };
        }
        else {
            return shortlists.rows;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get role by rolename
 * @param {string} shortlisted_user_id
 * @param {any} options
 * @returns {Promise<Role>}
 */
UserShortlistService.getShortlistByUserId = (shortlisted_user_id) => __awaiter(void 0, void 0, void 0, function* () {
    return user_shortlist_model_1.default.findOne({
        where: { shortlisted_user_id: shortlisted_user_id },
    });
});
/**
 * Create a Role
 * @param {Object} shortlistBody
 * @returns {Promise<Role>}
 */
UserShortlistService.createShortlist = (shortlistBody) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (yield _a.getShortlistByUserId(shortlistBody.shortlisted_user_id)) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "User already shortlisted");
        }
        const shortlist = yield user_shortlist_model_1.default.create(shortlistBody);
        return yield _a.getShortlistById(shortlist.id);
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get role by id
 * @param {Number} id
 * @returns {Promise<Role>}
 */
UserShortlistService.getShortlistById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return user_shortlist_model_1.default.findOne({
            where: { id },
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update role by id
 * @param {Number} shortlistId
 * @param {Object} updateBody
 * @returns {Promise<Role>}
 */
UserShortlistService.updateShortlistById = (shortlistId, updateBody) => __awaiter(void 0, void 0, void 0, function* () {
    const shortlist = yield user_shortlist_model_1.default.findByPk(shortlistId);
    if (!shortlist) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, 'Shortlist not found');
    }
    Object.assign(shortlist, updateBody);
    yield shortlist.save();
    return shortlist;
});
/**
 * Delete role by id
 * @param {Number} shortlistId
 * @returns {Promise<Role>}
 */
UserShortlistService.deleteShortlistById = (shortlistId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const shortlist = yield user_shortlist_model_1.default.findByPk(shortlistId);
        if (!shortlist) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, 'Shortlist not found');
        }
        yield shortlist.destroy();
        return shortlist;
    }
    catch (error) {
        if (error.name === "SequelizeForeignKeyConstraintError") {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Cannot delete this Record as it is referenced in another table.");
        }
        else {
            throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting Role.");
        }
    }
});
exports.default = UserShortlistService;
