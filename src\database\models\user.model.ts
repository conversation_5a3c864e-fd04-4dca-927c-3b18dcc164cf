import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Key,
  Table,
  Unique,
} from "sequelize-typescript";
import * as bcrypt from "bcrypt";
import UserAstroDetails from "./user_astro_details.model";
import UserBasicDetails from "./user_basic_details.model";
import UserEducationCareer from "./user_education_career.model";
import UserFamilyDetails from "./user_family_details.model";
import UserLifestyle from "./user_lifestyle.model";
import UserLocationDetails from "./user_location_details.model";
import UserVerification from "./user_verifications.model";
import UserHobbies from "./user_hobbies.model";
import UserShortlist from "./user_shortlist.model";
import PrivacySetting from "./privacy_settings.model";
import Token from "./token.model";
import UserSubscription from "./user_subscriptions.model";
import UserPreference from "./user_preferences.model";
import UserGallery from "./user_gallery.model";
import { Sequelize } from "sequelize";

export interface UserI {
  id: number;
  member_id: string;
  first_name: string;
  middle_name: string;
  last_name: string;
  gender: string;
  profile_created_for: string;
  date_of_birth: Date;
  email: string;
  phone: string;
  phone_code: string;
  password: string;
  status: "new" | "pending" | "approved" | "rejected" | "blocked" | "deactivated";
  is_hide_profile: boolean;
  terms_condition: boolean;
  profile_bio: string;
  profile_image: string;
  failedLoginAttempts: number;
  isSuspended: boolean;
  suspendedAt?: Date | null;
  createdAt?: Date;
  updatedAt?: Date;
}

@Table({
  tableName: "users",
  timestamps: true,
})
class User extends Model<UserI> implements UserI {
  @PrimaryKey
  @AutoIncrement
  @Column
  id: number;

  @AllowNull(true)
  @Unique
  @Column(DataType.STRING(266))
  member_id: string;

  @AllowNull(false)
  @Column(DataType.STRING(100))
  first_name: string;

  @AllowNull(true)
  @Column(DataType.STRING(100))
  middle_name: string;

  @AllowNull(false)
  @Column(DataType.STRING(100))
  last_name: string;

  @AllowNull(false)
  @Default("male")
  @Column(DataType.ENUM("male", "female", "other"))
  gender: string;

  @AllowNull(true)
  @Default("Myself")
  @Column(DataType.STRING(255))
  profile_created_for: string;

  @AllowNull(false)
  @Column(DataType.DATE)
  date_of_birth: Date;

  @AllowNull(false)
  @Column(DataType.STRING(100))
  email: string;

  @AllowNull(false)
  @Column(DataType.STRING(15))
  phone: string;

  @AllowNull(false)
  @Column(DataType.STRING())
  phone_code: string;

  @AllowNull(false)
  @Column(DataType.STRING(255))
  password: string;


  @AllowNull(false)
  @Default("new")
  @Column(DataType.ENUM("new", "pending", "approved", "rejected", "blocked", "deactivated"))
  status:  "new" | "pending" | "approved" | "rejected" | "blocked" | "deactivated";

  @AllowNull(false)
  @Default(false)
  @Column(DataType.BOOLEAN)
  is_hide_profile: boolean;

  @AllowNull(false)
  @Default(false)
  @Column(DataType.BOOLEAN)
  terms_condition: boolean;

  @AllowNull(true)
  @Column(DataType.TEXT)
  profile_bio: string;

  @AllowNull(true)
  @Column(DataType.STRING(255))
  profile_image: string;

  @AllowNull(false)
  @Default(0)
  @Column(DataType.INTEGER)
  failedLoginAttempts: number;

  @AllowNull(false)
  @Default(false)
  @Column(DataType.BOOLEAN)
  isSuspended: boolean;

  @AllowNull(true)
  @Column(DataType.DATE)
  suspendedAt?: Date | null;

  @HasMany(() => UserGallery, { foreignKey: "user_id", onDelete: "CASCADE", })
  userGallery: UserGallery;

  @HasOne(() => PrivacySetting, { foreignKey: "user_id", onDelete: "CASCADE", })
  privacySetting: PrivacySetting;

  @HasMany(() => UserSubscription, { foreignKey: "user_id", onDelete: "CASCADE", })
  userSubscription: UserSubscription;

  @HasMany(() => Token, { foreignKey: "user_id", onDelete: "CASCADE", })
  token: Token;

  @HasOne(() => UserBasicDetails, { foreignKey: "user_id", onDelete: "CASCADE", })
  basicDetails: UserBasicDetails;

  @HasOne(() => UserLocationDetails, { foreignKey: "user_id", onDelete: "CASCADE", })
  locationDetails: UserLocationDetails;

  @HasOne(() => UserEducationCareer, { foreignKey: "user_id", onDelete: "CASCADE", })
  educationCareer: UserEducationCareer;

  @HasOne(() => UserLifestyle, { foreignKey: "user_id", onDelete: "CASCADE", })
  lifestyle: UserLifestyle;

  @HasOne(() => UserAstroDetails, { foreignKey: "user_id", onDelete: "CASCADE", })
  astroDetails: UserAstroDetails;

  @HasOne(() => UserFamilyDetails, { foreignKey: "user_id", onDelete: "CASCADE", })
  familyDetails: UserFamilyDetails;

  @HasOne(() => UserHobbies, { foreignKey: "user_id", onDelete: "CASCADE", })
  hobbies: UserHobbies;

  @HasOne(() => UserVerification, { foreignKey: "user_id", onDelete: "CASCADE", })
  verification: UserVerification;

  @HasMany(() => UserShortlist, { foreignKey: "user_id", onDelete: "CASCADE", })
  userShortlistDetails: UserShortlist;

  @HasOne(() => UserShortlist, { foreignKey: "shortlisted_user_id", onDelete: "CASCADE", })
  shortlisted_user: UserShortlist;

  @HasOne(() => UserPreference, { foreignKey: "user_id", onDelete: "CASCADE", })
  userPreference: UserPreference;

  @BeforeCreate
  @BeforeUpdate
  static async encryptPassword(instance: User) {
    if (instance.changed('password')) {
      instance.password = await bcrypt.hash(instance.password, 10);
    }
  }

  @BeforeCreate
  static async generateMemberId(instance: User) {
    const result:any = await User.findOne({
      attributes: [
        [Sequelize.fn('MAX', Sequelize.col('member_id')), 'maxMemberId']
      ],
      raw: true,
    });

    let nextId = 1111;
    if (result?.maxMemberId) {
      nextId = parseInt(result.maxMemberId, 10) + 1;
    }

    instance.member_id = nextId.toString().padStart(6, '0');
  }
}

export default User;


// import {
//   AllowNull,
//   AutoIncrement,
//   Column,
//   DataType,
//   Default,
//   Model,
//   PrimaryKey,
//   Table,
//   ForeignKey,
//   BelongsTo
// } from "sequelize-typescript";
// import User from "./user.model";

// export interface UserReportI {
//   id: number;
//   reporter_id: number;
//   reported_user_id: number;
//   reason: "Harassment" | "Fake Profile" | "Inappropriate Content" | "Other";
//   details: string;
//   status: "Pending" | "Reviewed" | "Resolved" | "Dismissed";
//   admin_notes: string;
//   admin_action: "None" | "Warning" | "Suspension" | "Account Deletion";
//   createdAt?: Date;
//   updatedAt?: Date;
// }

// @Table({
//   tableName: "user_reports",
//   timestamps: true,
// })
// class UserReport extends Model<UserReportI> implements UserReportI {
//   @PrimaryKey
//   @AutoIncrement
//   @Column
//   id: number;

//   @AllowNull(false)
//   @ForeignKey(() => User)
//   @Column(DataType.INTEGER)
//   reporter_id: number;

//   @AllowNull(false)
//   @ForeignKey(() => User)
//   @Column(DataType.INTEGER)
//   reported_user_id: number;

//   @AllowNull(false)
//   @Column(DataType.ENUM("Harassment", "Fake Profile", "Inappropriate Content", "Other"))
//   reason: "Harassment" | "Fake Profile" | "Inappropriate Content" | "Other";

//   @AllowNull(true)
//   @Column(DataType.TEXT)
//   details: string;

//   @AllowNull(false)
//   @Default("Pending")
//   @Column(DataType.ENUM("Pending", "Reviewed", "Resolved", "Dismissed"))
//   status: "Pending" | "Reviewed" | "Resolved" | "Dismissed";

//   @AllowNull(true)
//   @Column(DataType.TEXT)
//   admin_notes: string;

//   @AllowNull(false)
//   @Default("None")
//   @Column(DataType.ENUM("None", "Warning", "Suspension", "Account Deletion"))
//   admin_action: "None" | "Warning" | "Suspension" | "Account Deletion";

//   @BelongsTo(() => User, { foreignKey: 'reporter_id', as: 'reporter' })
//   reporter: User;

//   @BelongsTo(() => User, { foreignKey: 'reported_user_id', as: 'reportedUser' })
//   reportedUser: User;
// }

// export default UserReport;