"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middlewares/auth");
const subscription_plan_controller_1 = __importDefault(require("../app/subscription/subscription_plan.controller"));
const middleware_1 = require("../middlewares/middleware");
const subscription_plan_validation_1 = require("../validations/subscription_plan.validation");
const router = express_1.default.Router();
router.get("", auth_1.auth, subscription_plan_controller_1.default.getAll);
router.post("", auth_1.auth, (0, middleware_1.validate)(subscription_plan_validation_1.subscriptionPlanValidation), subscription_plan_controller_1.default.create);
router.put("/:id", auth_1.auth, (0, middleware_1.validate)(subscription_plan_validation_1.subscriptionPlanValidation), subscription_plan_controller_1.default.update);
router.get("/:id", auth_1.auth, subscription_plan_controller_1.default.showById);
router.delete("/:id", auth_1.auth, subscription_plan_controller_1.default.delete);
exports.default = router;
