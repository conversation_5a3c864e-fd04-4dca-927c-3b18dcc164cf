import express from "express";
import UserProfileController from "../app/user/user_profile/user_profile.controller";
import { auth } from "../middlewares/auth";
import { fileUploadMiddleware } from "../middlewares/fileUploadMiddleware";
import UserShortlistController from "../app/user/user_shortlist/user_shortlist.controller";


const router = express.Router();

router.get("",auth, UserShortlistController.getAll);
router.post("",auth, UserShortlistController.create);
router.put("/:id",auth, UserShortlistController.update);
router.get("/:id",auth, UserShortlistController.showById);
router.delete("/:id", auth, UserShortlistController.delete);

export default router;
