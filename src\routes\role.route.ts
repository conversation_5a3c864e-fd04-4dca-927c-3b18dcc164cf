import express from "express";
import { auth } from "../middlewares/auth";
import { roleValidation } from "../validations/role.validation";
import { validate } from "../middlewares/middleware";
import RoleController from "../app/admin/role/role.controller";
const router = express.Router();

router.get("", auth, RoleController.getAll);
router.post("", auth,  RoleController.create);
router.get("/:id", auth, RoleController.showById);
router.put("/:id", auth, RoleController.update);
router.delete("/:id", auth, RoleController.delete);

export default router;
