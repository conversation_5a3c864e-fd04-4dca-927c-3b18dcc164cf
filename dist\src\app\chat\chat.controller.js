"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const chat_service_1 = __importDefault(require("./chat.service"));
const message_service_1 = __importDefault(require("../message/message.service"));
const response_1 = __importStar(require("../../utils/response"));
class ChatController {
}
_a = ChatController;
ChatController.chatService = chat_service_1.default;
ChatController.messageService = message_service_1.default;
/**
 * Create a new chat
 * @route POST /api/chats
 */
ChatController.createChat = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { user_id } = request.body;
        const user1Id = request.decoded;
        const chat = yield _a.chatService.createChat(user1Id, user_id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.CREATED,
            message: "Chat created successfully",
            data: chat,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Get chat by ID
 * @route GET /api/chats/:id
 */
ChatController.getChatById = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const chatId = parseInt(request.params.id);
        const userId = request.decoded;
        const chat = yield _a.chatService.getChatById(chatId, userId);
        // Check if user is part of the chat
        if (chat.user1_id !== userId && chat.user2_id !== userId) {
            return (0, response_1.default)(response, {
                statusCode: http_status_1.default.FORBIDDEN,
                message: "You do not have permission to access this chat",
            });
        }
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Chat retrieved successfully",
            data: chat,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Get all chats for the authenticated user
 * @route GET /api/chats
 */
ChatController.getUserChats = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const chats = yield _a.chatService.getUserChats(userId);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Chats retrieved successfully",
            data: chats,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Get chat messages
 * @route GET /api/chats/:id/messages
 */
ChatController.getChatMessages = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const chatId = parseInt(request.params.id);
        const userId = request.decoded;
        // Verify user has access to this chat
        const chat = yield _a.chatService.getChatById(chatId, userId);
        if (chat.user1_id !== userId && chat.user2_id !== userId) {
            return (0, response_1.default)(response, {
                statusCode: http_status_1.default.FORBIDDEN,
                message: "You do not have permission to access this chat",
            });
        }
        const page = parseInt(request.query.page) || 1;
        const limit = parseInt(request.query.limit) || 20;
        const messages = yield _a.messageService.getChatMessages(chatId, page, limit);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Messages retrieved successfully",
            data: messages,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Set auto-delete days for a chat
 * @route PUT /api/chats/:id/auto-delete
 */
ChatController.setAutoDeleteDays = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const chatId = parseInt(request.params.id);
        const userId = request.decoded;
        const { days } = request.body;
        // Verify user has access to this chat
        const chat = yield _a.chatService.getChatById(chatId, userId);
        if (chat.user1_id !== userId && chat.user2_id !== userId) {
            return (0, response_1.default)(response, {
                statusCode: http_status_1.default.FORBIDDEN,
                message: "You do not have permission to modify this chat",
            });
        }
        const updatedChat = yield _a.chatService.setAutoDeleteDays(chatId, days);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Auto-delete days set successfully",
            data: updatedChat,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
/**
 * Delete a chat
 * @route DELETE /api/chats/:id
 */
ChatController.deleteChat = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const chatId = parseInt(request.params.id);
        const userId = request.decoded.id;
        yield _a.chatService.deleteChat(chatId, userId);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Chat deleted successfully",
            data: null,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = ChatController;
