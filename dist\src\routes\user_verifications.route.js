"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middlewares/auth");
const user_verifications_controller_1 = __importDefault(require("../app/user/user_verifications/user_verifications.controller"));
const fileUploadMiddleware_1 = require("../middlewares/fileUploadMiddleware");
const router = express_1.default.Router();
router.post("/send-phone-otp", auth_1.auth, user_verifications_controller_1.default.sendPhoneOtp);
router.post("/verify-phone-otp", auth_1.auth, user_verifications_controller_1.default.verifyPhoneOtp);
router.post("/upload-id-documents", auth_1.auth, fileUploadMiddleware_1.fileUploadMiddleware, user_verifications_controller_1.default.uploadIdDocuments);
router.get("", auth_1.auth, user_verifications_controller_1.default.getAll);
// router.post("", auth,fileUploadMiddleware, UserVerificationController.create);
router.get("/showById", auth_1.auth, user_verifications_controller_1.default.showById);
router.put("/:id", auth_1.auth, user_verifications_controller_1.default.update);
// router.delete("/:id", auth, UserVerificationController.delete);
exports.default = router;
