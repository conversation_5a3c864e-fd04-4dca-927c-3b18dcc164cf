"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const inquiries_controller_1 = __importDefault(require("../app/inquiries/inquiries.controller"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// Public routes
router.post("", inquiries_controller_1.default.create);
// Admin routes (require authentication)
router.get("", auth_1.auth, inquiries_controller_1.default.getAll);
router.get("/:id", auth_1.auth, inquiries_controller_1.default.showById);
router.put("/:id/status", auth_1.auth, inquiries_controller_1.default.updateStatus);
router.delete("/:id", auth_1.auth, inquiries_controller_1.default.delete);
exports.default = router;
