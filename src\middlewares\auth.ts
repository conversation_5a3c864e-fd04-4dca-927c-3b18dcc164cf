import { Response, NextFunction } from "express";
import httpStatus from "http-status";
import jwt from "jsonwebtoken";
import ApiError from "../utils/ApiError";
import { config } from "../config/config";
import errorResponse from "../utils/response";
import httpMessages from "../config/httpMessages";

export const auth = async (
  request: any,
  response: Response,
  next: NextFunction
) => {
  try {
    const authorization: string | undefined = request.headers.authorization;
    const token: string = authorization?.split(" ")[1] || "";
    const decoded: any = jwt.verify(token, config.jwt.secret);
    console.log('decoded: ', decoded);
    const user: any = decoded.sub;
    console.log("user: ", user);

    if (user) {
      request["decoded"] = user;
      return next();
    } else {
      return errorResponse(
        response,
        new ApiError(
          httpStatus.UNAUTHORIZED,
          httpMessages.USER.AUTH.UNAUTHORIZED
        )
      );
    }
  } catch (e) {
    return errorResponse(
      response,
      new ApiError(httpStatus.UNAUTHORIZED, httpMessages.USER.AUTH.UNAUTHORIZED)
    );
  }
};
