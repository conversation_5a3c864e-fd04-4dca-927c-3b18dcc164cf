"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteSuccessStoryValidation = exports.getSuccessStoryByIdValidation = exports.updateSuccessStoryStatusValidation = exports.successStoryValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.successStoryValidation = {
    body: joi_1.default.object().keys({
        bride_name: joi_1.default.string().required().max(100).messages({
            "string.base": "Bride's name must be a string.",
            "string.empty": "Bride's name is required.",
            "string.max": "Bride's name cannot exceed 100 characters.",
            "any.required": "Bride's name is required.",
        }),
        bride_email: joi_1.default.string().email().required().max(100).messages({
            "string.base": "Bride's email must be a string.",
            "string.empty": "Bride's email is required.",
            "string.email": "Bride's email must be a valid email address.",
            "string.max": "Bride's email cannot exceed 100 characters.",
            "any.required": "Bride's email is required.",
        }),
        groom_name: joi_1.default.string().required().max(100).messages({
            "string.base": "Groom's name must be a string.",
            "string.empty": "Groom's name is required.",
            "string.max": "Groom's name cannot exceed 100 characters.",
            "any.required": "Groom's name is required.",
        }),
        groom_email: joi_1.default.string().email().required().max(100).messages({
            "string.base": "Groom's email must be a string.",
            "string.empty": "Groom's email is required.",
            "string.email": "Groom's email must be a valid email address.",
            "string.max": "Groom's email cannot exceed 100 characters.",
            "any.required": "Groom's email is required.",
        }),
        bride_country: joi_1.default.string().required().max(100).messages({
            "string.base": "Bride's country must be a string.",
            "string.empty": "Bride's country is required.",
            "string.max": "Bride's country cannot exceed 100 characters.",
            "any.required": "Bride's country is required.",
        }),
        groom_country: joi_1.default.string().required().max(100).messages({
            "string.base": "Groom's country must be a string.",
            "string.empty": "Groom's country is required.",
            "string.max": "Groom's country cannot exceed 100 characters.",
            "any.required": "Groom's country is required.",
        }),
        meeting_date: joi_1.default.date().required().messages({
            "date.base": "Meeting date must be a valid date.",
            "any.required": "Meeting date is required.",
        }),
        engagement_date: joi_1.default.date().required().messages({
            "date.base": "Engagement date must be a valid date.",
            "any.required": "Engagement date is required.",
        }),
        marriage_date: joi_1.default.date().required().messages({
            "date.base": "Marriage date must be a valid date.",
            "any.required": "Marriage date is required.",
        }),
        current_country: joi_1.default.string().required().max(100).messages({
            "string.base": "Current country must be a string.",
            "string.empty": "Current country is required.",
            "string.max": "Current country cannot exceed 100 characters.",
            "any.required": "Current country is required.",
        }),
        contact_number: joi_1.default.string().allow('').max(20).messages({
            "string.base": "Contact number must be a string.",
            "string.max": "Contact number cannot exceed 20 characters.",
        }),
        story_text: joi_1.default.string().required().messages({
            "string.base": "Story text must be a string.",
            "string.empty": "Story text is required.",
            "any.required": "Story text is required.",
        }),
        allow_in_ads: joi_1.default.boolean().default(false).messages({
            "boolean.base": "Allow in ads must be a boolean.",
        }),
        media_files: joi_1.default.array().items(joi_1.default.string()).messages({
            "array.base": "Media files must be an array of strings.",
        }),
    }),
};
exports.updateSuccessStoryStatusValidation = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required().messages({
            "number.base": "ID must be a number.",
            "any.required": "ID is required.",
        }),
    }),
    body: joi_1.default.object().keys({
        status: joi_1.default.string().valid("pending", "approved", "rejected").required().messages({
            "string.base": "Status must be a string.",
            "string.empty": "Status is required.",
            "string.valid": "Status must be one of: pending, approved, rejected.",
            "any.required": "Status is required.",
        }),
        admin_notes: joi_1.default.string().allow('').messages({
            "string.base": "Admin notes must be a string.",
        }),
    }),
};
exports.getSuccessStoryByIdValidation = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required().messages({
            "number.base": "ID must be a number.",
            "any.required": "ID is required.",
        }),
    }),
};
exports.deleteSuccessStoryValidation = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required().messages({
            "number.base": "ID must be a number.",
            "any.required": "ID is required.",
        }),
    }),
};
