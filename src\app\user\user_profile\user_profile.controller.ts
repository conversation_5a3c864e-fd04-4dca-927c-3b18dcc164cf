import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../../utils/response";
import httpMessages from "../../../config/httpMessages";
import UserProfileService from "./user_profile.service";
import ApiError from "../../../utils/ApiError";
import UserService from "../user.service";
import TokenService from "../../../common/services/token.service";
import SubscriptionService from "../../subscription/subscription.service";

export default class UserProfileController {
  static userService = UserService;
  static userProfileService = UserProfileService;
  static tokenService = TokenService;
  static subscriptionService = SubscriptionService;

  constructor() { }

  static getAll = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search, filters } = request.query;
      const decoded = request.decoded;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
        filters: filters ? JSON.parse(filters as string) : null,
        currentUserId: decoded,
      };
      const users = await this.userProfileService.getUserProfiles(option);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.SUCCESS,
        data: users,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static getUserSearch = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search, filters } = request.query;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
        filters: filters ? JSON.parse(filters as string) : null,
      };
      console.log('option: ', option);
      const users = await this.userProfileService.getUserSearchProfiles(option);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.SUCCESS,
        data: users,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const userProfileData = { ...request.body };
      const userId = request.decoded;
      let keyCount = 0;
      let message = '';
      for (const key in userProfileData) {
        keyCount++;
        if (userProfileData[key] && typeof userProfileData[key] === 'object') {
          userProfileData[key].user_id = userId;
        }
      }

      let userData = await this.userProfileService.createUserDetails(userProfileData, userId);
      if (keyCount === 1) message = `${userData.message} saved successfully`;
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: message || "Profile Updated successfully",
        data: userData,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static showById = catchAsync(async (request: Request, response: Response) => {
    try {
      const { userId } = request.params;
      if (userId === "mefull") {
        let decoded = request.decoded;
        if (!decoded) {
          throw new ApiError(
            httpStatus.UNAUTHORIZED,
            httpMessages.USER.AUTH.UNAUTHORIZED
          );
        }
        // Set the id to the decoded id
        const user: any = await this.userProfileService.getUserProfileById(
          decoded
        );
        if (!user) {
          throw new ApiError(httpStatus.UNAUTHORIZED, httpMessages.USER.AUTH.UNAUTHORIZED);
        }

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: httpMessages.USER.DETAILS.SUCCESS,
          data: user,
        });
      }

      const user: any = await this.userProfileService.getUserProfileById(
        parseInt(userId)
      );
      if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER.NOT_FOUND);
      }
  
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.DETAILS.SUCCESS,
        data: user,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });


  static viewProfile = catchAsync(async (request: Request, response: Response) => {
    try {
      const { id } = request.params;
      if (id === "mefull") {
        let decoded = request.decoded;
        if (!decoded) {
          throw new ApiError(
            httpStatus.UNAUTHORIZED,
            httpMessages.USER.AUTH.UNAUTHORIZED
          );
        }
        // Set the id to the decoded id
        const user: any = await this.userProfileService.getUserProfileById(
          decoded
        );
        if (!user) {
          throw new ApiError(httpStatus.UNAUTHORIZED, httpMessages.USER.AUTH.UNAUTHORIZED);
        }

        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: httpMessages.USER.DETAILS.SUCCESS,
          data: user,
        });
      }

      const user: any = await this.userProfileService.getUserProfileById(
        parseInt(id)
      );
      if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER.NOT_FOUND);
      }
      await SubscriptionService.trackUsage({
        user_id: parseInt(request.decoded),
        action_type: 'profile_viewed',
        target_user_id: parseInt(id)
      });
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.DETAILS.SUCCESS,
        data: user,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static createProfile = catchAsync(async (request: Request, response: Response) => {
    try {
      const userId = request.decoded;
      const userProfileData = { ...request.body, user_id: userId };

      await this.userProfileService.createUserProfile(userProfileData);
      // let user = await this.userService.getUserById(userId);
      // const tokens = await this.tokenService.generateAuthTokens(user);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.REGISTER.SUCCESS,
        data: null,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });
}
