import express from "express";
import GeolocationController from "../app/geoLocation/geolocation.controller";
import { auth } from "../middlewares/auth";
import { validate } from "../middlewares/middleware";
import { geolocationValidation } from "../validations/geolocation.validation";

const router = express.Router();
router.get("", auth, GeolocationController.getAll);
router.post("", auth,validate(geolocationValidation), GeolocationController.create);

router.get("/countries", GeolocationController.getCountries);
router.get("/cities/:countryId", GeolocationController.getCities);

router.get("/:id", auth, GeolocationController.showById);
router.put("/:id", auth, GeolocationController.update);
router.delete("/:id", auth, GeolocationController.delete);
export default router;
