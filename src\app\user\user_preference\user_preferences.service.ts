import httpStatus from "http-status";
import { Op } from "sequelize";
import ApiError from "../../../utils/ApiError";
import httpMessages from "../../../config/httpMessages";
import UserPreference from "../../../database/models/user_preferences.model";
import UserBasicDetails from "../../../database/models/user_basic_details.model";
import Country from "../../../database/models/country.model";
import UserLocationDetails from "../../../database/models/user_location_details.model";
import City from "../../../database/models/city.model";
import UserEducationCareer from "../../../database/models/user_education_career.model";
import UserLifestyle from "../../../database/models/user_lifestyle.model";
import UserFamilyDetails from "../../../database/models/user_family_details.model";
import UserAstroDetails from "../../../database/models/user_astro_details.model";
import UserGallery from "../../../database/models/user_gallery.model";
import User from "../../../database/models/user.model";
import UserHobbies from "../../../database/models/user_hobbies.model";
import UserShortlist from "../../../database/models/user_shortlist.model";

export default class UserPreferenceService {
  constructor() { }

  /**
   * Create a UserPreference
   * @param {Object} body
   * @returns {Promise<UserPreference>}
   */
  static createUserPreference = async (body: any) => {
    try {
      const details: UserPreference = await UserPreference.create(body);
      return details;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Return UserPreferences
   * @param {Object} options
   * @param {number} [options.page] - Current page number (optional)
   * @param {number} [options.limit] - Number of items per page (optional)
   * @param {string} [options.search] - Search term for filtering (optional)
   * @returns {Promise<UserPreference[]>}
   */
  static getUserPreferences = async (options: {
    page?: number;
    limit?: number;
    search?: string;
    currentUserId?: number;
  }) => {
    try {
      const { page, limit, search, currentUserId } = options;

      // 1. Get user preferences
      const userPreference: any = await UserPreference.findOne({
        where: { user_id: currentUserId }
      });

      if (!userPreference) {
        throw new ApiError(httpStatus.NOT_FOUND, "User preferences not found");
      }

      const whereCondition: any = search
        ? {
          [Op.or]: [
            { first_name: { [Op.like]: `%${search.toLowerCase()}%` } },
            { last_name: { [Op.like]: `%${search.toLowerCase()}%` } },
          ],
        }
        : {};

      whereCondition.id = { [Op.ne]: currentUserId };
      whereCondition.status = 'active';

      const basicWhere: any = {};
      const locationWhere: any = {};
      const educationWhere: any = {};
      const lifestyleWhere: any = {};
      const familyWhere: any = {};
      const astroWhere: any = {};
      const hobbiesWhere: any = {};

      // Apply basic details filters
      if (userPreference.religion?.length) {
        basicWhere.religion = { [Op.in]: userPreference.religion };
      }

      if (userPreference.caste?.length) {
        basicWhere.caste = { [Op.in]: userPreference.caste };
      }

      if (userPreference.gotra) {
        basicWhere.gotra = userPreference.gotra;
      }

      if (userPreference.marital_status) {
        basicWhere.marital_status = userPreference.marital_status;
      }

      if (userPreference.country_of_citizenship?.length) {
        basicWhere.country_of_citizenship = { [Op.in]: userPreference.country_of_citizenship };
      }

      // Apply location filters
      if (userPreference.country_living_in?.length) {
        locationWhere.country_living_in = { [Op.in]: userPreference.country_living_in };
      }

      if (userPreference.city?.length) {
        locationWhere.city = { [Op.in]: userPreference.city };
      }

      if (userPreference.residency_status?.length) {
        locationWhere.residency_status = { [Op.in]: userPreference.residency_status };
      }

      // Apply education filters
      if (userPreference.education?.length) {
        educationWhere.education = { [Op.in]: userPreference.education };
      }

      if (userPreference.profession?.length) {
        educationWhere.profession = { [Op.in]: userPreference.profession };
      }

      if (userPreference.employment_status?.length) {
        educationWhere.employment_status = { [Op.in]: userPreference.employment_status };
      }

      if (userPreference.working_for?.length) {
        educationWhere.working_for = { [Op.in]: userPreference.working_for };
      }

      // Apply lifestyle filters
      if (userPreference.age_range) {
        lifestyleWhere.age = {};
        if (userPreference.age_range.start) {
          lifestyleWhere.age[Op.gte] = userPreference.age_range.start;
        }
        if (userPreference.age_range.end) {
          lifestyleWhere.age[Op.lte] = userPreference.age_range.end;
        }
      }

      if (userPreference.height_range) {
        lifestyleWhere.height_cm = {};
        if (userPreference.height_range.start) {
          lifestyleWhere.height_cm[Op.gte] = userPreference.height_range.start;
        }
        if (userPreference.height_range.end) {
          lifestyleWhere.height_cm[Op.lte] = userPreference.height_range.end;
        }
      }

      if (userPreference.body_type?.length) {
        lifestyleWhere.body_type = { [Op.in]: userPreference.body_type };
      }

      if (userPreference.complexion?.length) {
        lifestyleWhere.complexion = { [Op.in]: userPreference.complexion };
      }

      if (userPreference.diet?.length) {
        lifestyleWhere.diet = { [Op.in]: userPreference.diet };
      }

      if (userPreference.smoke) {
        lifestyleWhere.smoking_habit = userPreference.smoke;
      }

      if (userPreference.drink) {
        lifestyleWhere.drinking_habit = userPreference.drink;
      }

      if (userPreference.disability) {
        lifestyleWhere.any_disability = userPreference.disability;
      }

      // Apply astro details filters
      if (userPreference.countryOfBirth?.length) {
        astroWhere.countryOfBirth = { [Op.in]: userPreference.countryOfBirth };
      }

      if (userPreference.birthCity?.length) {
        astroWhere.birthCity = { [Op.in]: userPreference.birthCity };
      }

      // Apply family details filters
      if (userPreference.family_type?.length) {
        familyWhere.family_type = { [Op.in]: userPreference.family_type };
      }

      // 3. Build query options
      const queryOption: any = {
        where: whereCondition,
        distinct: true,
        include: [
          {
            model: UserBasicDetails,
            as: "basicDetails",
            where: Object.keys(basicWhere).length ? basicWhere : undefined,
            attributes: ["religion", "caste", "gotra", "marital_status", "country_of_citizenship"],
            include: [
              {
                model: Country,
                as: "country",
                attributes: ["id", "name"],
              },
            ],
          },
          {
            model: UserLocationDetails,
            as: "locationDetails",
            where: Object.keys(locationWhere).length ? locationWhere : undefined,
            attributes: ["city", "country_living_in", "residency_status"],
            include: [
              {
                model: Country,
                as: "country",
                attributes: ["id", "name"],
              },
              {
                model: City,
                as: "cities",
                attributes: ["id", "name"],
              },
            ],
          },
          {
            model: UserEducationCareer,
            as: "educationCareer",
            where: Object.keys(educationWhere).length ? educationWhere : undefined,
            attributes: ["education", "profession", "employment_status", "working_for"]
          },
          {
            model: UserLifestyle,
            as: "lifestyle",
            where: Object.keys(lifestyleWhere).length ? lifestyleWhere : undefined,
            attributes: ["age", "height_cm", "body_type", "complexion", "diet", "smoking_habit", "drinking_habit", "any_disability"]
          },
          {
            model: UserFamilyDetails,
            as: "familyDetails",
            where: Object.keys(familyWhere).length ? familyWhere : undefined,
            attributes: ["family_type", "father_occupation", "mother_occupation"]
          },
          {
            model: UserAstroDetails,
            as: "astroDetails",
            where: Object.keys(astroWhere).length ? astroWhere : undefined,
            attributes: ["countryOfBirth", "birthCity"],
            include: [
              {
                model: Country,
                as: "country",
                attributes: ["id", "name"],
              },
              {
                model: City,
                as: "cities",
                attributes: ["id", "name"],
              },
            ],
          },

        ],
        order: [["createdAt", "DESC"]],
      };
      // Apply pagination
      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }

      // 4. Execute query
      const totalItems = await User.count({
        where: whereCondition,
        include: queryOption.include.map((include: any) => ({
          model: include.model,
          as: include.as,
          where: include.where
        }))
      });

      const users = await User.findAll(queryOption);

      // 5. Format and return results
      if (page && limit) {
        return {
          totalItems,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
          users
        };
      } else {
        return users;
      }
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get UserPreference by id with enriched reference data
   * @param {Number} id
   * @returns {Promise<UserPreference>}
   */
  static getUserPreferenceById = async (id: number) => {
    try {
      const user_preference: UserPreference | null = await UserPreference.findByPk(id);
      const country_of_citizenship_names = await user_preference?.country_of_citizenship_names;
      const country_living_in_names = await user_preference?.country_living_in_names;
      const city_names = await user_preference?.city_names;
      const countryOfBirth_names = await user_preference?.countryOfBirth_names;
      const birthCity_names = await user_preference?.birthCity_names;
      return { ...user_preference?.toJSON(), country_of_citizenship_names, country_living_in_names, city_names,countryOfBirth_names, birthCity_names };
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Update user preference by id
   * @param {Number} Id
   * @param {Object} updateBody
   * @returns {Promise<UserPreference>}
   */
  static updateUserPreferenceById = async (Id: number, updateBody: any) => {
    const details = await UserPreference.findByPk(Id);
    if (!details) {
      throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USERPRE.NOT_FOUND);
    }

    Object.assign(details, updateBody);
    await details.save();
    return details;
  };

  /**
   * Delete role by id
   * @param {Number} Id
   * @returns {Promise<Role>}
   */
  static deleteUserPreferenceById = async (Id: number) => {
    try {
      const details: any = await UserPreference.findByPk(Id);
      if (!details) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USERPRE.NOT_FOUND);
      }
      await details.destroy();
      return details;
    } catch (error: any) {
      throw new ApiError(
        error.status || httpStatus.BAD_REQUEST,
        error.message || "Error deleting Role."
      );
    }
  };

  /**
   * Get matching users based on user preferences with flexible matching
   * @param {Number} userId - The ID of the user whose preferences to use
   * @param {Object} options - Pagination and search options
   * @returns {Promise<Object>} - Matching users with pagination info
   */
  static getMatchingUsersByPreference = async (userId: number, options: {
    page?: number;
    limit?: number;
    search?: string;
  }) => {
    try {
      // 1. Get user preferences
      const userPreference: any = await UserPreference.findOne({
        where: { user_id: userId }
      });

      if (!userPreference) {
        throw new ApiError(httpStatus.NOT_FOUND, "User preferences not found");
      }

      // 2. Build filter conditions based on preferences
      const { page, limit, search } = options;
      const whereCondition: any = search
        ? {
          [Op.or]: [
            { first_name: { [Op.like]: `%${search.toLowerCase()}%` } },
            { last_name: { [Op.like]: `%${search.toLowerCase()}%` } },
          ],
        }
        : {};

      // Exclude the current user
      whereCondition.id = { [Op.ne]: userId };
      whereCondition.status = 'approved';
      whereCondition.is_hide_profile = false;
      if (userId) {
        const currentUser = await User.findOne({
          where: { id: userId },
          attributes: ['gender']
        });
        if (currentUser?.gender === 'male') {
          whereCondition.gender = 'female';
        } else {
          whereCondition.gender = 'male';
        }
      }

      const orConditions: any[] = [];

      // Add basic details conditions
      if (userPreference.religion?.length) {
        orConditions.push({
          '$basicDetails.religion$': { [Op.in]: userPreference.religion }
        });
      }

      if (userPreference.caste?.length) {
        orConditions.push({
          '$basicDetails.caste$': { [Op.in]: userPreference.caste }
        });
      }

      if (userPreference.gotra) {
        orConditions.push({
          '$basicDetails.gotra$': userPreference.gotra
        });
      }

      if (userPreference.marital_status) {
        orConditions.push({
          '$basicDetails.marital_status$': userPreference.marital_status
        });
      }

      if (userPreference.country_of_citizenship?.length) {
        orConditions.push({
          '$basicDetails.country_of_citizenship$': { [Op.in]: userPreference.country_of_citizenship }
        });
      }

      // Add location conditions
      if (userPreference.country_living_in?.length) {
        orConditions.push({
          '$locationDetails.country_living_in$': { [Op.in]: userPreference.country_living_in }
        });
      }

      if (userPreference.city?.length) {
        orConditions.push({
          '$locationDetails.city$': { [Op.in]: userPreference.city }
        });
      }


      if (userPreference.residency_status?.length) {
        orConditions.push({
          '$locationDetails.residency_status$': { [Op.in]: userPreference.residency_status }
        });
      }

      // Add education conditions
      if (userPreference.education?.length) {
        orConditions.push({
          '$educationCareer.education$': { [Op.in]: userPreference.education }
        });
      }

      if (userPreference.profession?.length) {
        orConditions.push({
          '$educationCareer.profession$': { [Op.in]: userPreference.profession }
        });
      }

      if (userPreference.employment_status?.length) {
        orConditions.push({
          '$educationCareer.employment_status$': { [Op.in]: userPreference.employment_status }
        });
      }

      if (userPreference.working_for?.length) {
        orConditions.push({
          '$educationCareer.working_for$': { [Op.in]: userPreference.working_for }
        });
      }

      // Add lifestyle conditions
      if (userPreference.age_range) {
        if (userPreference.age_range.start) {
          orConditions.push({
            '$lifestyle.age$': { [Op.gte]: userPreference.age_range.start }
          });
        }
        if (userPreference.age_range.end) {
          orConditions.push({
            '$lifestyle.age$': { [Op.lte]: userPreference.age_range.end }
          });
        }
      }

      if (userPreference.height_range) {
        if (userPreference.height_range.start) {
          orConditions.push({
            '$lifestyle.height_cm$': { [Op.gte]: userPreference.height_range.start }
          });
        }
        if (userPreference.height_range.end) {
          orConditions.push({
            '$lifestyle.height_cm$': { [Op.lte]: userPreference.height_range.end }
          });
        }
      }

      if (userPreference.body_type?.length) {
        orConditions.push({
          '$lifestyle.body_type$': { [Op.in]: userPreference.body_type }
        });
      }

      if (userPreference.complexion?.length) {
        orConditions.push({
          '$lifestyle.complexion$': { [Op.in]: userPreference.complexion }
        });
      }

      if (userPreference.diet?.length) {
        orConditions.push({
          '$lifestyle.diet$': { [Op.in]: userPreference.diet }
        });
      }

      if (userPreference.smoke) {
        orConditions.push({
          '$lifestyle.smoking_habit$': userPreference.smoke
        });
      }

      if (userPreference.drink) {
        orConditions.push({
          '$lifestyle.drinking_habit$': userPreference.drink
        });
      }

      if (userPreference.disability) {
        orConditions.push({
          '$lifestyle.any_disability$': userPreference.disability
        });
      }

      // Add astro details conditions
      if (userPreference.countryOfBirth?.length) {
        orConditions.push({
          '$astroDetails.countryOfBirth$': { [Op.in]: userPreference.countryOfBirth }
        });
      }

      if (userPreference.birthCity?.length) {
        orConditions.push({
          '$astroDetails.birthCity$': { [Op.in]: userPreference.birthCity }
        });
      }

      // Add family details conditions
      if (userPreference.family_type?.length) {
        orConditions.push({
          '$familyDetails.family_type$': { [Op.in]: userPreference.family_type }
        });
      }

      // Add the OR conditions to the main where clause if there are any
      if (orConditions.length > 0) {
        whereCondition[Op.or] = orConditions;
      }

      // 3. Build query options
      const queryOption: any = {
        where: whereCondition,
        distinct: true,
        include: [
          {
            model: UserBasicDetails,
            as: "basicDetails",
            required: false,
            attributes: ["religion", "caste", "gotra", "marital_status", "country_of_citizenship"],
            include: [
              {
                model: Country,
                as: "country",
                attributes: ["id", "name"],
              },
            ],
          },
          {
            model: UserLocationDetails,
            as: "locationDetails",
            required: false,
            attributes: ["city", "country_living_in", "residency_status"],
            include: [
              {
                model: Country,
                as: "country",
                attributes: ["id", "name"],
              },
              {
                model: City,
                as: "cities",
                attributes: ["id", "name"],
              },
            ],
          },
          {
            model: UserEducationCareer,
            as: "educationCareer",
            required: false,
            attributes: ["education", "profession", "employment_status", "working_for"]
          },
          {
            model: UserLifestyle,
            as: "lifestyle",
            required: false,
            attributes: ["age", "height_cm", "body_type", "complexion", "diet", "smoking_habit", "drinking_habit", "any_disability"]
          },
          {
            model: UserFamilyDetails,
            as: "familyDetails",
            required: false,
            attributes: ["family_type", "father_occupation", "mother_occupation"]
          },
          {
            model: UserAstroDetails,
            as: "astroDetails",
            required: false,
            attributes: ["countryOfBirth", "birthCity"],
            include: [
              {
                model: Country,
                as: "country",
                attributes: ["id", "name"],
              },
              {
                model: City,
                as: "cities",
                attributes: ["id", "name"],
              },
            ],
          },
          {
            model: UserHobbies,
            as: "hobbies",
            attributes: ["hobbies", "interests"]
          },
          {
            model: UserGallery,
            as: "userGallery",
          },
          {
            model: UserShortlist,
            as: "shortlisted_user"
          }
        ],
        order: [["createdAt", "DESC"]],
      };

      // Apply pagination
      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }

      // 4. Execute query
      const totalItems = await User.count({
        where: whereCondition,
        include: queryOption.include.map((include: any) => ({
          model: include.model,
          as: include.as,
          required: false
        })),
        distinct: true
      });

      const users = await User.findAll(queryOption);

      // 5. Format and return results
      if (page && limit) {
        return {
          totalItems,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
          users
        };
      } else {
        return users;
      }
    } catch (error: any) {
      throw new ApiError(error.statusCode || httpStatus.BAD_REQUEST, error.message);
    }
  };
}
