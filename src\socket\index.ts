import { Server as SocketServer } from 'socket.io';
import { Server } from 'http';
import jwt from 'jsonwebtoken';
import { config } from '../config/config';
import User from '../database/models/user.model';
import Chat from '../database/models/chat.model';
import Message from '../database/models/message.model';
import { encryptData } from '../middlewares/EncryptionResponse';

// Store active users
const activeUsers = new Map<number, string>();

export const  initializeSocket = (server: Server) => {
  const io = new SocketServer(server, {
    cors: {
      origin: '*',
      methods: ['GET', 'POST']
    }
  });

  // Middleware for authentication
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
        if (!token) {
        return next(new Error('Authentication error: Token not provided'));
      }
      let checkToken: string = token?.split(" ")[1] || "";
      const decoded: any = jwt.verify(checkToken, config.jwt.secret);
      const user = decoded.sub;

      if (!user) {
        return next(new Error('Authentication error: Invalid token'));
      }

      socket.data.user_id = user;
      next();
    } catch (error) {
      next(new Error('Authentication error: Invalid token'));
    }
  });

  io.on('connection', (socket) => {
    console.log('User connected:================', socket.id);
    
    // Store user connection
    if (socket.data.user_id) {
      const userId = socket.data.user_id;
      activeUsers.set(userId, socket.id);
      socket.emit("online-users", Array.from(activeUsers.keys()));
      // Notify friends that user is online
      socket.broadcast.emit('user_status', { userId, status: 'online' });
    }

    // Handle private messages
    socket.on('private_message', async (data) => {
      try {
        const { chatId, receiverId, content,temp_id } = data;
        const senderId = socket.data.user_id;
        
        // Encrypt message content
        const iv = 'v@XI!kaW3BK,@8ki';
        const secretKey = 'Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn';
        const encryptedContent = encryptData(content, secretKey, iv);
        
        // Save message to database
        const message = await Message.create({
          chat_id: chatId,
          sender_id: senderId,
          receiver_id: receiverId,
          content: content,
          is_encrypted: true,
          is_delivered: false,
          is_read: false,
          delivered_at: null,
          read_at: null
        });
        
        // Update last message time in chat
        await Chat.update(
          { last_message_at: new Date() },
          { where: { id: chatId } }
        );
        
        // Send message to receiver if online
        const receiverSocketId = activeUsers.get(receiverId);
        if (receiverSocketId) {
          io.to(receiverSocketId).emit('new_message', {
            messageId: message.id,
            chatId,
            senderId,
            content: content,
            createdAt: message.createdAt
          });
          
          // Mark as delivered
          await Message.update(
            { is_delivered: true, delivered_at: new Date() },
            { where: { id: message.id } }
          );
          
          // Notify sender that message was delivered
          socket.emit('message_delivered', { messageId: message.id,temp_id :temp_id  });
        }
        socket.emit('sent_message', { messageId: message.id,temp_id :temp_id  });
      } catch (error) {
        console.error('Error sending message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle message read status
    socket.on('message_read', async (data) => {
      try {
        const { messageId } = data;
        
        // Update message as read
        await Message.update(
          { is_read: true, read_at: new Date(),is_delivered: true,delivered_at: new Date() },
          { where: { id: messageId } }
        );
        
        const message = await Message.findByPk(messageId);
        if (message) {
          // Notify sender that message was read
          const senderSocketId = activeUsers.get(message.sender_id);
          if (senderSocketId) {
            io.to(senderSocketId).emit('message_delivered', { messageId });
            io.to(senderSocketId).emit('message_read', { messageId });
          }
        }
      } catch (error) {
        console.error('Error marking message as read:', error);
      }
    });

    socket.on('typing_start', (data) => {
      const { chatId, userId } = data;
      const senderId = socket.data.user_id;
      const receiverSocketId = activeUsers.get(userId);
      if (receiverSocketId) {
        io.to(receiverSocketId).emit('user_typing_start', { chatId, senderId });
      }
    });

    socket.on('typing_stop', (data) => {
      const { chatId, userId } = data;
      const senderId = socket.data.user_id;
      const receiverSocketId = activeUsers.get(userId);
      if (receiverSocketId) {
        io.to(receiverSocketId).emit('user_typing_stop', { chatId, senderId });
      }
    });
    // Handle disconnection
    socket.on('disconnect', () => {
      console.log('User disconnected:=====================', socket.id);
      
      if (socket.data.user_id) {
        const userId = socket.data.user_id;
        activeUsers.delete(userId);
        
        // Notify friends that user is offline
        socket.broadcast.emit('user_status', { userId, status: 'offline' });
      }
    });
  });

  return io;
};

export const getActiveUsers = () => {
  return activeUsers;
};
