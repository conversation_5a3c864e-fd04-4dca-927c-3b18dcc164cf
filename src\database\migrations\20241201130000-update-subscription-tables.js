'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add new columns to user_subscriptions table
    await queryInterface.addColumn('user_subscriptions', 'payment_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'paypal_payments',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    await queryInterface.addColumn('user_subscriptions', 'payment_status', {
      type: Sequelize.ENUM('pending', 'completed', 'failed', 'refunded'),
      allowNull: false,
      defaultValue: 'pending'
    });

    // Add usage tracking columns
    await queryInterface.addColumn('user_subscriptions', 'interest_sent_count', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('user_subscriptions', 'contact_viewed_count', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('user_subscriptions', 'profile_viewed_count', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('user_subscriptions', 'chat_initiated_count', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0
    });

    // Remove old usage columns if they exist
    try {
      await queryInterface.removeColumn('user_subscriptions', 'usage_limit');
      await queryInterface.removeColumn('user_subscriptions', 'usage_count');
    } catch (error) {
      console.log('Old usage columns not found, skipping removal');
    }

    // Create user_action_logs table
    await queryInterface.createTable('user_action_logs', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      action_type: {
        type: Sequelize.ENUM('interest_sent', 'contact_viewed', 'profile_viewed', 'chat_initiated'),
        allowNull: false
      },
      target_user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      subscription_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'user_subscriptions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      action_date: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint to prevent duplicate actions
    await queryInterface.addConstraint('user_action_logs', {
      fields: ['user_id', 'action_type', 'target_user_id', 'subscription_id'],
      type: 'unique',
      name: 'unique_user_action_target_subscription'
    });

    // Add indexes for better performance
    await queryInterface.addIndex('user_action_logs', ['user_id']);
    await queryInterface.addIndex('user_action_logs', ['target_user_id']);
    await queryInterface.addIndex('user_action_logs', ['action_type']);
    await queryInterface.addIndex('user_action_logs', ['subscription_id']);
    await queryInterface.addIndex('user_action_logs', ['action_date']);

    // Add indexes to user_subscriptions for better performance
    await queryInterface.addIndex('user_subscriptions', ['payment_status']);
    await queryInterface.addIndex('user_subscriptions', ['expires_at']);
    await queryInterface.addIndex('user_subscriptions', ['is_active']);
  },

  down: async (queryInterface, Sequelize) => {
    // Remove new columns from user_subscriptions
    await queryInterface.removeColumn('user_subscriptions', 'payment_id');
    await queryInterface.removeColumn('user_subscriptions', 'payment_status');
    await queryInterface.removeColumn('user_subscriptions', 'interest_sent_count');
    await queryInterface.removeColumn('user_subscriptions', 'contact_viewed_count');
    await queryInterface.removeColumn('user_subscriptions', 'profile_viewed_count');
    await queryInterface.removeColumn('user_subscriptions', 'chat_initiated_count');

    // Drop user_action_logs table
    await queryInterface.dropTable('user_action_logs');

    // Re-add old columns if needed
    await queryInterface.addColumn('user_subscriptions', 'usage_limit', {
      type: Sequelize.INTEGER,
      allowNull: true
    });

    await queryInterface.addColumn('user_subscriptions', 'usage_count', {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 0
    });
  }
};
