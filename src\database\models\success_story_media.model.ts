import {
    Table,
    Column,
    Model,
    DataType,
    PrimaryKey,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    CreatedAt,
    UpdatedAt
} from "sequelize-typescript";
import SuccessStory from "./success_story.model";

export interface SuccessStoryMediaI {
    id?: number;
    story_id: number;
    media_type: "image" | "video";
    media_path: string;
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: "success_story_media",
    timestamps: true,
})
class SuccessStoryMedia extends Model<SuccessStoryMediaI> implements SuccessStoryMediaI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @ForeignKey(() => SuccessStory)
    @AllowNull(false)
    @Column
    story_id: number;

    @BelongsTo(() => SuccessStory)
    story: SuccessStory;

    @AllowNull(false)
    @Column(DataType.ENUM("image", "video"))
    media_type: "image" | "video";

    @AllowNull(false)
    @Column(DataType.STRING(255))
    media_path: string;

    @CreatedAt
    createdAt?: Date;

    @UpdatedAt
    updatedAt?: Date;
}

export default SuccessStoryMedia;
