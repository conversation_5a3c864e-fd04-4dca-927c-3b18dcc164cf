import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON>Key,
  AutoIncrement,
  AllowNull,
  ForeignKey,
  BelongsTo,
  HasMany,
  Default,
  CreatedAt,
  UpdatedAt
} from "sequelize-typescript";
import User from "./user.model";
import Message from "./message.model";

export interface ChatI {
  id: number;
  user1_id: number;
  user2_id: number;
  is_active: boolean;
  last_message_at: Date;
  auto_delete_days: number | null;
  createdAt?: Date;
  updatedAt?: Date;
}

@Table({
  tableName: "chats",
  timestamps: true,
})
export default class Chat extends Model implements ChatI {
  @PrimaryKey
  @AutoIncrement
  @Column
  id: number;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column
  user1_id: number;

  @BelongsTo(() => User, { foreignKey: 'user1_id',onDelete: "CASCADE" })
  user1: User;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column
  user2_id: number;

  @BelongsTo(() => User, { foreignKey: 'user2_id', onDelete: "CASCADE" })
  user2: User;

  @Default(true)
  @Column(DataType.BOOLEAN)
  is_active: boolean;

  @CreatedAt
  @Column
  last_message_at: Date;

  @AllowNull(true)
  @Column(DataType.INTEGER)
  auto_delete_days: number | null;

  @CreatedAt
  @Column
  createdAt: Date;

  @UpdatedAt
  @Column
  updatedAt: Date;

  @HasMany(() => Message, { foreignKey: 'chat_id' })
  messages: Message[];
}

