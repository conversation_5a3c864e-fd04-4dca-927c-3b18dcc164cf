"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initScheduler = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const autoUnhideProfiles_1 = require("../utils/autoUnhideProfiles");
const subscription_service_1 = __importDefault(require("../app/subscription/subscription.service"));
/**
 * Initialize all scheduled tasks
 */
const initScheduler = () => {
    // Schedule auto-delete messages task (keep your existing schedule)
    node_cron_1.default.schedule('0 0 * * *', () => __awaiter(void 0, void 0, void 0, function* () {
        console.log('Running scheduled task: Auto-delete messages');
        try {
            // await runAutoDeleteMessages();
        }
        catch (error) {
            console.error('Error in auto-delete messages task:', error);
        }
    }));
    // Schedule auto-unhide profiles task at midnight (00:00) every day
    node_cron_1.default.schedule('0 0 * * *', () => __awaiter(void 0, void 0, void 0, function* () {
        console.log('Running scheduled task: Auto-unhide profiles');
        try {
            yield (0, autoUnhideProfiles_1.runAutoUnhideProfiles)();
        }
        catch (error) {
            console.error('Error in auto-unhide profiles task:', error);
        }
    }));
    node_cron_1.default.schedule('0 0 * * *', () => __awaiter(void 0, void 0, void 0, function* () {
        console.log('Running scheduled task: Auto-delete messages');
        try {
            try {
                console.log('Running daily subscription expiry check...');
                const result = yield subscription_service_1.default.checkAndUpdateExpiredSubscriptions();
                console.log(`Subscription expiry check completed. Updated ${result.updatedCount} subscriptions.`);
                if (result.updatedCount > 0) {
                    console.log('Expired subscriptions:', result.expiredSubscriptions);
                }
            }
            catch (error) {
                console.error('Error in subscription expiry check:', error);
            }
        }
        catch (error) {
            console.error('Error in auto-delete messages task:', error);
        }
    }));
};
exports.initScheduler = initScheduler;
