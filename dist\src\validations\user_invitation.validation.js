"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateInvitationStatusValidation = exports.userInvitationValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.userInvitationValidation = {
    body: joi_1.default.object().keys({
        receiver_id: joi_1.default.number().required(),
        message: joi_1.default.string().allow('').optional(),
    }),
};
exports.updateInvitationStatusValidation = {
    body: joi_1.default.object().keys({
        status: joi_1.default.string().valid('accepted', 'declined').required(),
    }),
};
