"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const sequelize_1 = require("sequelize");
const country_model_1 = __importDefault(require("../../database/models/country.model"));
const city_model_1 = __importDefault(require("../../database/models/city.model"));
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
const database_1 = require("../../database/database");
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const http_status_1 = __importDefault(require("http-status"));
class GeolocationService {
    constructor() { }
}
_a = GeolocationService;
/**
 * Return Countries With Cities
 * @param {Object} options
 * @param {number} [options.page] - Current page number (optional)
 * @param {number} [options.limit] - Number of items per page (optional)
 * @param {string} [options.search] - Search term for filtering (optional)
 * @param {number[]} [options.countryIds] - Array of country IDs to filter by (optional)
 * @returns {Promise<Role[]>}
 */
GeolocationService.getCountriesWithCities = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, countryIds } = options;
        // Build the where condition
        let whereCondition = {};
        // Add search condition if provided
        if (search) {
            whereCondition = {
                [sequelize_1.Op.or]: [
                    {
                        name: {
                            [sequelize_1.Op.like]: `%${search.toLowerCase()}%`,
                        },
                    },
                ],
            };
        }
        // Add country IDs filter if provided
        if (countryIds && Array.isArray(countryIds) && countryIds.length > 0) {
            whereCondition.id = {
                [sequelize_1.Op.in]: countryIds,
            };
        }
        const queryOption = {
            where: whereCondition,
            attributes: {
                exclude: ["timezone", "is_active", "createdAt", "updatedAt"],
            },
            include: [
                {
                    model: city_model_1.default,
                    as: "cities",
                    attributes: {
                        exclude: ["createdAt", "updatedAt"],
                    },
                },
            ],
            order: [["name", "ASC"]],
        };
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        // Count total items
        const totalItems = yield country_model_1.default.count({
            where: whereCondition,
        });
        // Fetch paginated staff data
        const countries = yield country_model_1.default.findAll(queryOption);
        if (page && limit) {
            return {
                totalItems: totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                countries: countries,
            };
        }
        else {
            return countries;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Create a Countries With Cities
 * @param {Object} countryBody
 * @returns {Promise<Role>}
 */
GeolocationService.createCountriesWithCities = (countryBody) => __awaiter(void 0, void 0, void 0, function* () {
    const t = yield database_1.sequelize.transaction();
    try {
        if (yield _a.getCountryByName(countryBody.name)) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.GEOLOCATION.COUNTRY.NAME_ALREADY_TAKEN);
        }
        const country = yield country_model_1.default.create(countryBody, {
            transaction: t,
        });
        if (countryBody.cities) {
            const cityDataArray = countryBody.cities.map((city) => (Object.assign(Object.assign({}, city), { country_id: country.id })));
            const cities = yield city_model_1.default.bulkCreate(cityDataArray, {
                returning: true,
                transaction: t,
            });
        }
        yield t.commit();
        return yield _a.getCountryById(country.id);
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update a Countries With Cities
 * @param {Object} countryBody
 * @returns {Promise<Role>}
 */
GeolocationService.updateCountriesWithCities = (countryId, countryBody) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const existingCountry = yield country_model_1.default.findByPk(countryId);
        if (!existingCountry) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Country not found");
        }
        Object.assign(existingCountry, countryBody);
        yield existingCountry.save();
        if (countryBody.cities.length) {
            const cityIds = [];
            for (let index = 0; index < countryBody.cities.length; index++) {
                const city = countryBody.cities[index];
                let dbCity;
                let cityById = yield city_model_1.default.findByPk(city.id);
                if (cityById) {
                    Object.assign(cityById, city);
                    dbCity = yield cityById.save();
                    cityIds.push(cityById.id);
                }
                else {
                    city["country_id"] = existingCountry.id;
                    dbCity = yield city_model_1.default.create(city);
                    cityIds.push(dbCity.id);
                }
            }
            if (cityIds.length) {
                yield city_model_1.default.destroy({
                    where: {
                        country_id: existingCountry.id,
                        id: {
                            [sequelize_1.Op.notIn]: cityIds,
                        },
                    },
                });
            }
        }
        return yield _a.getCountryById(countryId);
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get Country by id
 * @param {Number} id
 * @returns {Promise<Role>}
 */
GeolocationService.getCountryById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return country_model_1.default.findOne({
            where: {
                id,
            },
            include: [
                {
                    model: city_model_1.default,
                    as: "cities",
                    attributes: {
                        exclude: ["createdAt", "updatedAt"],
                    },
                },
            ],
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get role by rolename
 * @param {string} country_Name
 * @returns {Promise<Role>}
 */
GeolocationService.getCountryByName = (country_Name) => __awaiter(void 0, void 0, void 0, function* () {
    return country_model_1.default.findOne({
        where: {
            name: country_Name,
        },
    });
});
/**
 * Return countries
 * @returns {Promise<Country[]>}
 */
GeolocationService.getCountries = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return yield country_model_1.default.findAll({
            attributes: {
                exclude: ["timezone", "is_active", "createdAt", "updatedAt"],
            },
        });
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Delete role by id
 * @param {Number} countryId
 * @returns {Promise<Role>}
 */
GeolocationService.deleteCountryById = (countryId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const country = yield country_model_1.default.findByPk(countryId);
        if (!country) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.GEOLOCATION.COUNTRY.NOT_FOUND);
        }
        yield country.destroy();
        return country;
    }
    catch (error) {
        if (error.name === "SequelizeForeignKeyConstraintError") {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Cannot delete this Record as it is referenced in another table.");
        }
        else {
            throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting Role.");
        }
    }
});
/**
 * Return cities
 * @returns {Promise<City[]>}
 */
GeolocationService.getCitiesByCountry = (countryId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return yield city_model_1.default.findAll({
            where: {
                country_id: countryId,
            },
        });
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = GeolocationService;
