"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const sequelize_typescript_1 = require("sequelize-typescript");
const user_model_1 = __importDefault(require("./user.model")); // Adjust import path based on your structure
const country_model_1 = __importDefault(require("./country.model"));
const city_model_1 = __importDefault(require("./city.model"));
let UserLocationDetails = class UserLocationDetails extends sequelize_typescript_1.Model {
};
__decorate([
    sequelize_typescript_1.PrimaryKey,
    sequelize_typescript_1.AutoIncrement,
    sequelize_typescript_1.Column,
    __metadata("design:type", Number)
], UserLocationDetails.prototype, "id", void 0);
__decorate([
    sequelize_typescript_1.Index,
    (0, sequelize_typescript_1.ForeignKey)(() => user_model_1.default),
    (0, sequelize_typescript_1.AllowNull)(false),
    sequelize_typescript_1.Column,
    __metadata("design:type", Number)
], UserLocationDetails.prototype, "user_id", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => user_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_model_1.default)
], UserLocationDetails.prototype, "user", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => country_model_1.default),
    (0, sequelize_typescript_1.AllowNull)(true),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.INTEGER),
    __metadata("design:type", Number)
], UserLocationDetails.prototype, "country_living_in", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => country_model_1.default, 'country_living_in'),
    __metadata("design:type", country_model_1.default)
], UserLocationDetails.prototype, "country", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => city_model_1.default),
    (0, sequelize_typescript_1.AllowNull)(true),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.INTEGER),
    __metadata("design:type", Number)
], UserLocationDetails.prototype, "city", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => city_model_1.default, 'city'),
    __metadata("design:type", city_model_1.default)
], UserLocationDetails.prototype, "cities", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(true),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING(100)),
    __metadata("design:type", String)
], UserLocationDetails.prototype, "residency_status", void 0);
UserLocationDetails = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: "user_location_details",
        timestamps: false,
    })
], UserLocationDetails);
exports.default = UserLocationDetails;
