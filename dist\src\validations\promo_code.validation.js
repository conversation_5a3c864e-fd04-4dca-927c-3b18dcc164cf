"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPromoCodeValidation = exports.deletePromoCodeValidation = exports.updatePromoCodeValidation = exports.createPromoCodeValidation = exports.getPromoCodeByCodeValidation = exports.validatePromoCodeValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.validatePromoCodeValidation = {
    body: joi_1.default.object().keys({
        code: joi_1.default.string().trim().min(1).max(50).required().messages({
            "string.base": "Promo code must be a string.",
            "string.empty": "Promo code cannot be empty.",
            "string.min": "Promo code must be at least 1 character long.",
            "string.max": "Promo code cannot exceed 50 characters.",
            "any.required": "Promo code is required.",
        }),
        plan_id: joi_1.default.number().integer().positive().required().messages({
            "number.base": "Plan ID must be a number.",
            "number.integer": "Plan ID must be an integer.",
            "number.positive": "Plan ID must be a positive number.",
            "any.required": "Plan ID is required.",
        }),
    }),
};
exports.getPromoCodeByCodeValidation = {
    params: joi_1.default.object().keys({
        code: joi_1.default.string().trim().min(1).max(50).required().messages({
            "string.base": "Promo code must be a string.",
            "string.empty": "Promo code cannot be empty.",
            "string.min": "Promo code must be at least 1 character long.",
            "string.max": "Promo code cannot exceed 50 characters.",
            "any.required": "Promo code is required.",
        }),
    }),
};
// Admin validation schemas
exports.createPromoCodeValidation = {
    body: joi_1.default.object().keys({
        code: joi_1.default.string().trim().min(1).max(50).required().messages({
            "string.base": "Promo code must be a string.",
            "string.empty": "Promo code cannot be empty.",
            "string.min": "Promo code must be at least 1 character long.",
            "string.max": "Promo code cannot exceed 50 characters.",
            "any.required": "Promo code is required.",
        }),
        description: joi_1.default.string().trim().max(500).optional().messages({
            "string.base": "Description must be a string.",
            "string.max": "Description cannot exceed 500 characters.",
        }),
        discount_type: joi_1.default.string().valid("percentage", "fixed_amount").required().messages({
            "string.base": "Discount type must be a string.",
            "any.only": "Discount type must be either 'percentage' or 'fixed_amount'.",
            "any.required": "Discount type is required.",
        }),
        discount_value: joi_1.default.number().positive().required().messages({
            "number.base": "Discount value must be a number.",
            "number.positive": "Discount value must be a positive number.",
            "any.required": "Discount value is required.",
        }),
        minimum_purchase_amount: joi_1.default.number().positive().optional().messages({
            "number.base": "Minimum purchase amount must be a number.",
            "number.positive": "Minimum purchase amount must be a positive number.",
        }),
        maximum_discount_amount: joi_1.default.number().positive().optional().messages({
            "number.base": "Maximum discount amount must be a number.",
            "number.positive": "Maximum discount amount must be a positive number.",
        }),
        usage_limit: joi_1.default.number().integer().positive().optional().messages({
            "number.base": "Usage limit must be a number.",
            "number.integer": "Usage limit must be an integer.",
            "number.positive": "Usage limit must be a positive number.",
        }),
        start_date: joi_1.default.date().optional().messages({
            "date.base": "Start date must be a valid date.",
        }),
        expiry_date: joi_1.default.date().optional().messages({
            "date.base": "Expiry date must be a valid date.",
        }),
        applicable_plans: joi_1.default.array().items(joi_1.default.number().integer().positive()).optional().messages({
            "array.base": "Applicable plans must be an array.",
            "number.base": "Plan ID must be a number.",
            "number.integer": "Plan ID must be an integer.",
            "number.positive": "Plan ID must be a positive number.",
        }),
        first_time_users_only: joi_1.default.boolean().optional().messages({
            "boolean.base": "First time users only must be a boolean.",
        }),
        is_active: joi_1.default.boolean().optional().messages({
            "boolean.base": "Is active must be a boolean.",
        }),
    }),
};
exports.updatePromoCodeValidation = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().integer().positive().required().messages({
            "number.base": "Promo code ID must be a number.",
            "number.integer": "Promo code ID must be an integer.",
            "number.positive": "Promo code ID must be a positive number.",
            "any.required": "Promo code ID is required.",
        }),
    }),
    body: joi_1.default.object().keys({
        description: joi_1.default.string().trim().max(500).optional().messages({
            "string.base": "Description must be a string.",
            "string.max": "Description cannot exceed 500 characters.",
        }),
        discount_value: joi_1.default.number().positive().optional().messages({
            "number.base": "Discount value must be a number.",
            "number.positive": "Discount value must be a positive number.",
        }),
        minimum_purchase_amount: joi_1.default.number().positive().optional().messages({
            "number.base": "Minimum purchase amount must be a number.",
            "number.positive": "Minimum purchase amount must be a positive number.",
        }),
        maximum_discount_amount: joi_1.default.number().positive().optional().messages({
            "number.base": "Maximum discount amount must be a number.",
            "number.positive": "Maximum discount amount must be a positive number.",
        }),
        usage_limit: joi_1.default.number().integer().positive().optional().messages({
            "number.base": "Usage limit must be a number.",
            "number.integer": "Usage limit must be an integer.",
            "number.positive": "Usage limit must be a positive number.",
        }),
        start_date: joi_1.default.date().optional().messages({
            "date.base": "Start date must be a valid date.",
        }),
        expiry_date: joi_1.default.date().optional().messages({
            "date.base": "Expiry date must be a valid date.",
        }),
        applicable_plans: joi_1.default.array().items(joi_1.default.number().integer().positive()).optional().messages({
            "array.base": "Applicable plans must be an array.",
            "number.base": "Plan ID must be a number.",
            "number.integer": "Plan ID must be an integer.",
            "number.positive": "Plan ID must be a positive number.",
        }),
        first_time_users_only: joi_1.default.boolean().optional().messages({
            "boolean.base": "First time users only must be a boolean.",
        }),
        is_active: joi_1.default.boolean().optional().messages({
            "boolean.base": "Is active must be a boolean.",
        }),
    }),
};
exports.deletePromoCodeValidation = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().integer().positive().required().messages({
            "number.base": "Promo code ID must be a number.",
            "number.integer": "Promo code ID must be an integer.",
            "number.positive": "Promo code ID must be a positive number.",
            "any.required": "Promo code ID is required.",
        }),
    }),
};
exports.getPromoCodeValidation = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().integer().positive().required().messages({
            "number.base": "Promo code ID must be a number.",
            "number.integer": "Promo code ID must be an integer.",
            "number.positive": "Promo code ID must be a positive number.",
            "any.required": "Promo code ID is required.",
        }),
    }),
};
