"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createHelpCategoryValidation = exports.deleteHelpQuestionValidation = exports.getHelpQuestionByIdValidation = exports.updateHelpQuestionValidation = exports.createHelpQuestionValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.createHelpQuestionValidation = {
    body: joi_1.default.object().keys({
        question: joi_1.default.string().required().max(255).messages({
            "string.base": "Question must be a string.",
            "string.max": "Question cannot exceed 255 characters.",
            "any.required": "Question is required.",
        }),
        description: joi_1.default.string().required().messages({
            "string.base": "Description must be a string.",
            "any.required": "Description is required.",
        }),
        category_id: joi_1.default.number().required().messages({
            "number.base": "Category ID must be a number.",
            "any.required": "Category ID is required.",
        }),
    }),
};
exports.updateHelpQuestionValidation = {
    body: joi_1.default.object().keys({
        question: joi_1.default.string().max(255).messages({
            "string.base": "Question must be a string.",
            "string.max": "Question cannot exceed 255 characters.",
        }),
        answer: joi_1.default.string().messages({
            "string.base": "Answer must be a string.",
        }),
        category_id: joi_1.default.number().messages({
            "number.base": "Category ID must be a number.",
        }),
    }),
};
exports.getHelpQuestionByIdValidation = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required().messages({
            "number.base": "ID must be a number.",
            "any.required": "ID is required.",
        }),
    }),
};
exports.deleteHelpQuestionValidation = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required().messages({
            "number.base": "ID must be a number.",
            "any.required": "ID is required.",
        }),
    }),
};
exports.createHelpCategoryValidation = {
    body: joi_1.default.object().keys({
        title: joi_1.default.string().required().max(255).messages({
            "string.base": "Title must be a string.",
            "string.max": "Title cannot exceed 255 characters.",
            "any.required": "Title is required.",
        }),
        icon: joi_1.default.string().max(255).messages({
            "string.base": "Icon must be a string.",
            "string.max": "Icon cannot exceed 255 characters.",
        }),
    }),
};
