import {
  AllowNull,
  AutoIncrement,
  BelongsTo,
  Column,
  DataType,
  Default,
  ForeignKey,
  HasMany,
  HasOne,
  Model,
  NotEmpty,
  PrimaryKey,
  Table,
} from "sequelize-typescript";
import Role from "./role.model";
import RolePermission from "./role_permissions.model";

export interface ModuleI {
  id: number;
  name: string;
  route: string;
  parent_id?: number | null;
  icon?: string;
  sort_order?: number;
  is_active?: boolean;
}

@Table({
  tableName: "modules",
  timestamps: true,
})
class Module extends Model<ModuleI> implements ModuleI {
  @AutoIncrement
  @PrimaryKey
  @Column(DataType.INTEGER)
  id: number;

  @AllowNull(false)
  @NotEmpty
  @Column(DataType.STRING(255))
  name: string;

  @AllowNull(false)
  @NotEmpty
  @Column(DataType.STRING(255))
  route: string;

  @ForeignKey(() => Module)
  @AllowNull(true)
  @Column(DataType.INTEGER)
  parent_id?: number | null;

  @BelongsTo(() => <PERSON><PERSON><PERSON>, { foreignKey: "parent_id", onDelete: "CASCADE" })
  parent: Module;

  @AllowNull(true)
  @Column(DataType.STRING(100))
  icon?: string;

  @AllowNull(true)
  @Default(0)
  @Column(DataType.INTEGER)
  sort_order?: number;

  @AllowNull(true)
  @Default(true)
  @Column(DataType.BOOLEAN)
  is_active?: boolean;

  @HasMany(() => Module, {
    foreignKey: "parent_id",
    onDelete: "CASCADE",
  })
  subModules: Module[];

  @HasOne(() => RolePermission, {
    foreignKey: "module_id",
    onDelete: "CASCADE",
  })
  rolePermissions: RolePermission;
}

export default Module;
