"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const subscription_controller_1 = __importDefault(require("../app/subscription/subscription.controller"));
const auth_1 = require("../middlewares/auth");
const middleware_1 = require("../middlewares/middleware");
const subscription_validation_1 = require("../validations/subscription.validation");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.auth);
// Purchase subscription
router.post("/purchase", (0, middleware_1.validate)(subscription_validation_1.purchaseSubscriptionValidation), subscription_controller_1.default.purchaseSubscription);
// Track usage
router.post("/track-usage", (0, middleware_1.validate)(subscription_validation_1.trackUsageValidation), subscription_controller_1.default.trackUsage);
// Get subscription status
router.get("/status", subscription_controller_1.default.getSubscriptionStatus);
// Check if user can perform action
router.get("/can-perform", (0, middleware_1.validate)(subscription_validation_1.canPerformActionValidation), subscription_controller_1.default.canPerformAction);
// Get subscription history
router.get("/history", subscription_controller_1.default.getSubscriptionHistory);
// Get subscription by ID
router.get("/:subscription_id", (0, middleware_1.validate)(subscription_validation_1.getSubscriptionByIdValidation), subscription_controller_1.default.getSubscriptionById);
// Cancel subscription
router.post("/cancel", (0, middleware_1.validate)(subscription_validation_1.cancelSubscriptionValidation), subscription_controller_1.default.cancelSubscription);
// Admin routes (you may want to add admin role check middleware)
router.post("/admin/check-expired", subscription_controller_1.default.checkExpiredSubscriptions);
exports.default = router;
