import Joi from "joi";

export const geolocationValidation = {
  body: Joi.object().keys({
    id: Joi.number().optional().allow(null, ""),
    name: Joi.string().trim().required().messages({
      "string.empty": "Name is required.",
      "string.base": "Name must be a string.",
    }),
    phoneCode: Joi.string().trim().required().messages({
      "string.empty": "Phone code is required.",
      "string.base": "Phone code must be a string.",
        "string.min": "Phone code must be at least 1 characters long.",
    }),
    timezone: Joi.string().trim().optional().allow(null, ""),
    currency: Joi.string().trim().optional().allow(null, ""),
    currency_symbol: Joi.string().trim().optional().allow(null, ""),
    flag: Joi.string().trim().optional().allow(null, ""),
    is_active: Joi.boolean().optional().allow(null, ""),
    cities: Joi.array().items(
      Joi.object().keys({
        id: Joi.number().optional().allow(null, ""),
        name: Joi.string().trim().required().messages({
          "string.empty": "City name is required.",
          "string.base": "City name must be a string.",
        }),
      })
    ).required().messages({
      "array.base": "Cities must be an array.",
      "any.required": "Cities is required.",
    }),
  }),
};
