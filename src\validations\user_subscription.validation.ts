import Joi from "joi";

export const userSubscriptionValidation = {
  body: Joi.object().keys({
    user_id: Joi.number().integer().positive().required().messages({
      "number.base": "User ID must be a number.",
      "number.integer": "User ID must be an integer.",
      "number.positive": "User ID must be a positive number.",
      "any.required": "User ID is required.",
    }),

    plan_id: Joi.number().integer().positive().required().messages({
      "number.base": "Plan ID must be a number.",
      "number.integer": "Plan ID must be an integer.",
      "number.positive": "Plan ID must be a positive number.",
      "any.required": "Plan ID is required.",
    }),

    start_date: Joi.date().allow(null).messages({
      "date.base": "Start date must be a valid date.",
    }),

    end_date: Joi.date().allow(null).min(Joi.ref('start_date')).messages({
      "date.base": "End date must be a valid date.",
      "date.min": "End date must be after start date.",
    }),

    auto_renew: Joi.boolean().allow(null).default(false).messages({
      "boolean.base": "Auto renew must be a boolean.",
    }),

    issued_at: Joi.date().allow(null).messages({
      "date.base": "Issued at must be a valid date.",
    }),

    expires_at: Joi.date().allow(null).min(Joi.ref('issued_at')).messages({
      "date.base": "Expires at must be a valid date.",
      "date.min": "Expires at must be after issued at.",
    }),

    revoked_at: Joi.date().allow(null).messages({
      "date.base": "Revoked at must be a valid date.",
    }),

    usage_limit: Joi.number().integer().min(0).allow(null).messages({
      "number.base": "Usage limit must be a number.",
      "number.integer": "Usage limit must be an integer.",
      "number.min": "Usage limit must be a non-negative number.",
    }),

    usage_count: Joi.number().integer().min(0).allow(null).default(0).messages({
      "number.base": "Usage count must be a number.",
      "number.integer": "Usage count must be an integer.",
      "number.min": "Usage count must be a non-negative number.",
    }),

    is_active: Joi.boolean().allow(null).default(true).messages({
      "boolean.base": "Is active must be a boolean.",
    }),

    token: Joi.string().allow(null, "").messages({
      "string.base": "Token must be a string.",
    }),
  }),
};
