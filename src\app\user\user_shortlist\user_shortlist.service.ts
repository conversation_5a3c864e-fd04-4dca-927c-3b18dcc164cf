import httpStatus from "http-status";
import bcrypt from "bcrypt";
import { Op } from "sequelize";
import { Sequelize } from "sequelize-typescript";
import Role from "../../../database/models/role.model";
import ApiError from "../../../utils/ApiError";
import httpMessages from "../../../config/httpMessages";
import UserShortlist from "../../../database/models/user_shortlist.model";
import UserBasicDetails from "../../../database/models/user_basic_details.model";
import UserLocationDetails from "../../../database/models/user_location_details.model";
import UserEducationCareer from "../../../database/models/user_education_career.model";
import UserLifestyle from "../../../database/models/user_lifestyle.model";
import UserFamilyDetails from "../../../database/models/user_family_details.model";
import UserHobbies from "../../../database/models/user_hobbies.model";
import UserGallery from "../../../database/models/user_gallery.model";
import User from "../../../database/models/user.model";
import Country from "../../../database/models/country.model";
import City from "../../../database/models/city.model";

export default class UserShortlistService {
    constructor() { }

    /**
   * Return Roles
   * @param {Object} options
   * @param {number} [options.page] - Current page number (optional)
   * @param {number} [options.limit] - Number of items per page (optional)
   * @param {string} [options.search] - Search term for filtering (optional)
   * @returns {Promise<Role[]>}
   */
    static getShortlists = async (options: {
        page?: number;
        limit?: number;
        search?: string;
    }, userId?: number) => {
        try {
            const { page, limit, search } = options;
            // const whereCondition = search
            //     ? {
            //         [Op.or]: [
            //             { role_name: { [Op.like]: `%${search.toLowerCase()}%` } },
            //         ],
            //     }
            //     : {};


            // const queryOption: any = {
            //     where: { user_id: userId },
            //     order: [["createdAt", "DESC"]],
            // };
            // const queryOption: any = {
            //     where: { user_id: userId },
            //     distinct: true,
            //     include: [
            //         {
            //             model: User,
            //             as: "shortlisted_user",
                        
            //             include: [
            //                 {
            //                     model: UserBasicDetails,
            //                     as: "basicDetails",
            //                     attributes: ["religion", "caste", "gotra", 'marital_status']
            //                 },
            //                 {
            //                     model: UserLocationDetails,
            //                     as: "locationDetails",
            //                     attributes: ["city", "country_living_in"]
            //                 },
            //                 {
            //                     model: UserEducationCareer,
            //                     as: "educationCareer",
            //                     attributes: ["education", "profession"]
            //                 },
            //                 {
            //                     model: UserLifestyle,
            //                     as: "lifestyle",
            //                     attributes: ["age", "height_cm"]
            //                 },
            //                 {
            //                     model: UserFamilyDetails,
            //                     as: "familyDetails",
            //                     attributes: ["family_type", "father_occupation", "mother_occupation"]
            //                 },
            //                 {
            //                     model: UserHobbies,
            //                     as: "hobbies",
            //                     attributes: ["hobbies", "interests"]
            //                 },
            //                 {
            //                     model: UserProfileBio,
            //                     as: "profileBioDetails",
            //                 },
            //             ]
            //         },
            //     ],
            //     // attributes: {
            //     //   include: [
            //     //     [
            //     //       Sequelize.literal("`lifestyle`.`age`"),
            //     //       "age",
            //     //     ],
            //     //     [
            //     //       Sequelize.literal("`lifestyle`.`height_cm`"),
            //     //       "height_cm",
            //     //     ],
            //     //   ],
            //     // },
            //     // include: [
            //     //   {
            //     //     model: UserBasicDetails,
            //     //     as: "basicDetails",
            //     //     attributes: ["religion", "caste", "gotra", 'marital_status']
            //     //   },
            //     //   {
            //     //     model: UserLocationDetails,
            //     //     as: "locationDetails",
            //     //     attributes: ["city", "country_living_in"]
            //     //   },
            //     //   {
            //     //     model: UserEducationCareer,
            //     //     as: "educationCareer",
            //     //     attributes: ["education", "profession"]
            //     //   },
            //     //   {
            //     //     model: UserLifestyle,
            //     //     as: "lifestyle",
            //     //     attributes: ["age", "height_cm"]
            //     //   },
            //     //   {
            //     //     model: UserFamilyDetails,
            //     //     as: "familyDetails",
            //     //     attributes: ["family_type", "father_occupation", "mother_occupation"]
            //     //   },
            //     //   {
            //     //     model: UserHobbies,
            //     //     as: "hobbies",
            //     //     attributes: ["hobbies", "interests"]
            //     //   },
            //     //   {
            //     //     model: UserProfileBio,
            //     //     as: "profileBioDetails",
            //     //   },
            //     //   {
            //     //     model: UserShortlist,
            //     //     as: "userShortlistDetails",
            //     //   },
            //     // ],
            //     order: [["createdAt", "DESC"]],
            // };
            const shortlistWhere: any = {};

            if (userId) shortlistWhere.user_id = userId;
              const queryOption: any = {
                    // where: whereCondition,
                    distinct: true,
                    // attributes: {
                    //   include: [
                    //     [
                    //       Sequelize.literal("`lifestyle`.`age`"),
                    //       "age",
                    //     ],
                    //     [
                    //       Sequelize.literal("`lifestyle`.`height_cm`"),
                    //       "height_cm",
                    //     ],
                    //   ],
                    // },
                    include: [
                      {
                        model: UserBasicDetails,
                        as: "basicDetails",
                        attributes: ["religion", "caste", "gotra", 'marital_status']
                      },
                      {
                        model: UserLocationDetails,
                        as: "locationDetails",
                        attributes: ["city", "country_living_in"],
                        include: [
                            {
                                model: Country,
                                as: "country",
                                attributes: ['name'],
                            },
                            {
                                model: City,
                                as: "cities",
                                attributes: ['name'],
                            },
                        ],
                      },
                      {
                        model: UserEducationCareer,
                        as: "educationCareer",
                        attributes: ["education", "profession"]
                      },
                      {
                        model: UserLifestyle,
                        as: "lifestyle",
                        attributes: ["age", "height_cm"]
                      },
                      {
                        model: UserFamilyDetails,
                        as: "familyDetails",
                        attributes: ["family_type", "father_occupation", "mother_occupation"]
                      },
                      {
                        model: UserHobbies,
                        as: "hobbies",
                        attributes: ["hobbies", "interests"]
                      },
                      {
                        model: UserGallery,
                        as: "userGallery",
                      },
                      {
                        model: UserShortlist,
                        where: Object.keys(shortlistWhere).length ? shortlistWhere : undefined,
                        as:"shortlisted_user"
                      }
                    ],
                    order: [["createdAt", "DESC"]],
                  };
            // If pagination is provided, apply pagination
            if (page && limit) {
                const offset = (page - 1) * limit;
                queryOption.limit = limit;
                queryOption.offset = offset;
            }
            const shortlists = await User.findAndCountAll(queryOption);
            if (page && limit) {
                return {
                    totalItems: shortlists.count,
                    totalPages: Math.ceil(shortlists.count / limit),
                    currentPage: page,
                    shortlists: shortlists.rows,
                };
            } else {
                return shortlists.rows;
            }
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Get role by rolename
     * @param {string} shortlisted_user_id
     * @param {any} options
     * @returns {Promise<Role>}
     */
    static getShortlistByUserId = async (shortlisted_user_id: number) => {
        return UserShortlist.findOne({
            where: { shortlisted_user_id: shortlisted_user_id },
        });
    };

    /**
     * Create a Role
     * @param {Object} shortlistBody
     * @returns {Promise<Role>}
     */
    static createShortlist = async (shortlistBody: any) => {
        try {
            if (await this.getShortlistByUserId(shortlistBody.shortlisted_user_id)) {
                throw new ApiError(
                    httpStatus.BAD_REQUEST,
                   "User already shortlisted"
                );
            }

            const shortlist: UserShortlist = await UserShortlist.create(shortlistBody);

            return await this.getShortlistById(shortlist.id);
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };



    /**
     * Get role by id
     * @param {Number} id
     * @returns {Promise<Role>}
     */
    static getShortlistById = async (id: number) => {
        try {
            return UserShortlist.findOne({
                where: { id },
            }).then((data: any) => data?.toJSON());
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Update role by id
     * @param {Number} shortlistId
     * @param {Object} updateBody
     * @returns {Promise<Role>}
     */
    static updateShortlistById = async (shortlistId: number, updateBody: any) => {
        const shortlist = await UserShortlist.findByPk(shortlistId);
        if (!shortlist) {
            throw new ApiError(httpStatus.NOT_FOUND, 'Shortlist not found');
        }

        Object.assign(shortlist, updateBody);
        await shortlist.save();
        return shortlist;
    };

    /**
     * Delete role by id
     * @param {Number} shortlistId
     * @returns {Promise<Role>}
     */
    static deleteShortlistById = async (shortlistId: number) => {
        try {
            const shortlist: any = await UserShortlist.findByPk(shortlistId);
            if (!shortlist) {
                throw new ApiError(httpStatus.NOT_FOUND, 'Shortlist not found');
            }
            await shortlist.destroy();
            return shortlist;
        } catch (error: any) {
            if (error.name === "SequelizeForeignKeyConstraintError") {
                throw new ApiError(
                    httpStatus.BAD_REQUEST,
                    "Cannot delete this Record as it is referenced in another table."
                );
            } else {
                throw new ApiError(
                    error.status || httpStatus.BAD_REQUEST,
                    error.message || "Error deleting Role."
                );
            }
        }
    };
}
