"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const help_category_controller_1 = __importDefault(require("../app/help/help_category/help_category.controller"));
const help_question_controller_1 = __importDefault(require("../app/help/help_question/help_question.controller"));
const auth_1 = require("../middlewares/auth");
const middleware_1 = require("../middlewares/middleware");
const help_validation_1 = require("../validations/help.validation");
const router = express_1.default.Router();
router.get("/categories", help_category_controller_1.default.getAll);
router.post("/categories", auth_1.auth, (0, middleware_1.validate)(help_validation_1.createHelpCategoryValidation), help_category_controller_1.default.create);
router.get("/categories/:id", help_category_controller_1.default.showById);
router.put("/categories/:id", auth_1.auth, (0, middleware_1.validate)(help_validation_1.createHelpCategoryValidation), help_category_controller_1.default.update);
router.delete("/categories/:id", auth_1.auth, help_category_controller_1.default.delete);
router.get("/questions", help_question_controller_1.default.getAll);
router.post("/questions", auth_1.auth, (0, middleware_1.validate)(help_validation_1.createHelpQuestionValidation), help_question_controller_1.default.create);
router.get("/questions/:id", help_question_controller_1.default.showById);
router.put("/questions/:id", auth_1.auth, (0, middleware_1.validate)(help_validation_1.createHelpQuestionValidation), help_question_controller_1.default.update);
router.delete("/questions/:id", auth_1.auth, help_question_controller_1.default.delete);
exports.default = router;
