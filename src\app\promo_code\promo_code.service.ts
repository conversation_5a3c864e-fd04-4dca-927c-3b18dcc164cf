import httpStatus from "http-status";
import ApiError from "../../utils/ApiError";
import PromoCode from "../../database/models/promo_codes.model";
import PromoCodeUsage from "../../database/models/promo_code_usage.model";
import UserSubscription from "../../database/models/user_subscriptions.model";
import SubscriptionPlan from "../../database/models/subscription_plans.mode";
import { Op } from "sequelize";

interface ValidatePromoCodeParams {
    code: string;
    user_id: number;
    plan_id: number;
    plan_price: number;
}

interface ApplyPromoCodeParams {
    promo_code_id: number;
    user_id: number;
    subscription_id?: number;
    original_amount: number;
    discount_amount: number;
    final_amount: number;
}

interface PromoCodeValidationResult {
    isValid: boolean;
    promoCode?: PromoCode;
    discountAmount?: number;
    finalAmount?: number;
    message?: string;
}

export default class PromoCodeService {
    /**
     * Validate a promo code for a specific user and plan
     * @param {ValidatePromoCodeParams} params
     * @returns {Promise<PromoCodeValidationResult>}
     */
    static validatePromoCode = async (params: ValidatePromoCodeParams): Promise<PromoCodeValidationResult> => {
        try {
            const { code, user_id, plan_id, plan_price } = params;

            // Find the promo code
            const promoCode = await PromoCode.findOne({
                where: {
                    code: code.toUpperCase(),
                    is_active: true
                }
            });

            if (!promoCode) {
                return {
                    isValid: false,
                    message: "Invalid promo code"
                };
            }

            // Check if promo code has started
            if (promoCode.start_date && new Date() < promoCode.start_date) {
                return {
                    isValid: false,
                    message: "Promo code is not yet active"
                };
            }

            // Check if promo code has expired
            if (promoCode.expiry_date && new Date() > promoCode.expiry_date) {
                return {
                    isValid: false,
                    message: "Promo code has expired"
                };
            }

            // Check usage limit
            if (promoCode.usage_limit && promoCode.used_count >= promoCode.usage_limit) {
                return {
                    isValid: false,
                    message: "Promo code usage limit exceeded"
                };
            }

            // Check if user has already used this promo code
            const existingUsage = await PromoCodeUsage.findOne({
                where: {
                    promo_code_id: promoCode.id,
                    user_id
                }
            });

            if (existingUsage) {
                return {
                    isValid: false,
                    message: "You have already used this promo code"
                };
            }

            // Check if promo code is applicable to the selected plan
            if (promoCode.applicable_plans) {
                const applicablePlans = JSON.parse(promoCode.applicable_plans);
                if (!applicablePlans.includes(plan_id)) {
                    return {
                        isValid: false,
                        message: "Promo code is not applicable to this plan"
                    };
                }
            }

            // Check minimum purchase amount
            if (promoCode.minimum_purchase_amount && plan_price < promoCode.minimum_purchase_amount) {
                return {
                    isValid: false,
                    message: `Minimum purchase amount of $${promoCode.minimum_purchase_amount} required`
                };
            }

            // Check if it's for first-time users only
            if (promoCode.first_time_users_only) {
                const existingSubscription = await UserSubscription.findOne({
                    where: {
                        user_id,
                        payment_status: "completed"
                    }
                });

                if (existingSubscription) {
                    return {
                        isValid: false,
                        message: "This promo code is only for first-time users"
                    };
                }
            }

            // Calculate discount
            const { discountAmount, finalAmount } = this.calculateDiscount(
                plan_price,
                promoCode.discount_type,
                promoCode.discount_value,
                promoCode.maximum_discount_amount
            );

            return {
                isValid: true,
                promoCode,
                discountAmount,
                finalAmount,
                message: "Promo code is valid"
            };

        } catch (error: any) {
            console.error('Validate promo code error:', error);
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error.message);
        }
    };

    /**
     * Calculate discount amount based on discount type and value
     * @param {number} originalAmount
     * @param {string} discountType
     * @param {number} discountValue
     * @param {number} maxDiscountAmount
     * @returns {object}
     */
    static calculateDiscount = (
        originalAmount: number,
        discountType: "percentage" | "fixed_amount",
        discountValue: number,
        maxDiscountAmount?: number
    ) => {
        let discountAmount = 0;

        if (discountType === "percentage") {
            discountAmount = (originalAmount * discountValue) / 100;
            
            // Apply maximum discount limit if specified
            if (maxDiscountAmount && discountAmount > maxDiscountAmount) {
                discountAmount = maxDiscountAmount;
            }
        } else if (discountType === "fixed_amount") {
            discountAmount = Math.min(discountValue, originalAmount);
        }

        const finalAmount = Math.max(0, originalAmount - discountAmount);

        return {
            discountAmount: Math.round(discountAmount * 100) / 100, // Round to 2 decimal places
            finalAmount: Math.round(finalAmount * 100) / 100
        };
    };

    /**
     * Apply promo code and record usage
     * @param {ApplyPromoCodeParams} params
     * @returns {Promise<PromoCodeUsage>}
     */
    static applyPromoCode = async (params: ApplyPromoCodeParams): Promise<PromoCodeUsage> => {
        try {
            const { promo_code_id, user_id, subscription_id, original_amount, discount_amount, final_amount } = params;

            // Record promo code usage
            let body :any = {
                promo_code_id,
                user_id,
                subscription_id,
                discount_amount,
                original_amount,
                final_amount,
                used_at: new Date()
            }
            const usage = await PromoCodeUsage.create(body);

            // Increment used count for the promo code
            await PromoCode.increment('used_count', {
                where: { id: promo_code_id }
            });

            return usage;

        } catch (error: any) {
            console.error('Apply promo code error:', error);
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error.message);
        }
    };

    /**
     * Get promo code by code
     * @param {string} code
     * @returns {Promise<PromoCode | null>}
     */
    static getPromoCodeByCode = async (code: string): Promise<PromoCode | null> => {
        try {
            return await PromoCode.findOne({
                where: {
                    code: code.toUpperCase(),
                    is_active: true
                }
            });
        } catch (error: any) {
            console.error('Get promo code error:', error);
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error.message);
        }
    };

    /**
     * Get user's promo code usage history
     * @param {number} user_id
     * @returns {Promise<PromoCodeUsage[]>}
     */
    static getUserPromoCodeUsage = async (user_id: number): Promise<PromoCodeUsage[]> => {
        try {
            return await PromoCodeUsage.findAll({
                where: { user_id },
                include: [
                    {
                        model: PromoCode,
                        attributes: ['code', 'description', 'discount_type', 'discount_value']
                    }
                ],
                order: [['used_at', 'DESC']]
            });
        } catch (error: any) {
            console.error('Get user promo code usage error:', error);
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error.message);
        }
    };
}
