"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.encryptResponseMiddleware = void 0;
exports.encryptData = encryptData;
const crypto_js_1 = __importDefault(require("crypto-js"));
const response_1 = __importDefault(require("../utils/response"));
const ApiError_1 = __importDefault(require("../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../config/httpMessages"));
const iv = "v@XI!kaW3BK,@8ki";
const secretKey = "Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn";
function insertSubstring(original, substring, position) {
    return original.slice(0, position) + substring + original.slice(position);
}
function encryptData(data, key, iv) {
    try {
        if (key.length !== 32) {
            throw new Error('Key must be 32 characters for AES-256-CBC');
        }
        if (iv.length !== 16) {
            throw new Error('IV must be 16 characters for AES-CBC');
        }
        const keyWordArray = crypto_js_1.default.enc.Utf8.parse(key);
        const ivWordArray = crypto_js_1.default.enc.Utf8.parse(iv);
        const encrypted = crypto_js_1.default.AES.encrypt(data, keyWordArray, { iv: ivWordArray, mode: crypto_js_1.default.mode.CBC });
        let finalData = encrypted.toString(); // Base64 encoded ciphertext
        const first_fifteen = 'h&EKZdsBUkRaG%3';
        const second_seven = 'xejTbcK';
        const third_eight = 'j#pEcft4';
        const fourth_six = 't^FusM';
        const fifth_seven = 'rD9$b#Y';
        const last_fifteen = 'aez964DZn%$BhF^';
        if (finalData.length > 29) {
            finalData = insertSubstring(finalData, last_fifteen, finalData.length - 3);
            finalData = insertSubstring(finalData, fifth_seven, 28);
            finalData = insertSubstring(finalData, fourth_six, 22);
            finalData = insertSubstring(finalData, third_eight, 14);
            finalData = insertSubstring(finalData, second_seven, 8);
            finalData = insertSubstring(finalData, first_fifteen, 0);
        }
        return finalData;
    }
    catch (error) {
        throw new Error(`Encryption failed: ${error.message}`);
    }
}
const encryptResponseMiddleware = (request, response, next) => __awaiter(void 0, void 0, void 0, function* () {
    return new Promise((resolve, reject) => {
        (() => __awaiter(void 0, void 0, void 0, function* () {
            try {
                const originalSend = response.send;
                response.send = function (body) {
                    try {
                        if (typeof body === 'string') {
                            const iv = 'v@XI!kaW3BK,@8ki'; // Replace with your secret key
                            const secretKey = 'Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn'; // Replace with your IV
                            body = encryptData(body, secretKey, iv);
                        }
                        return originalSend.call(this, body);
                    }
                    catch (error) {
                        reject(error);
                    }
                };
                resolve();
            }
            catch (e) {
                reject((0, response_1.default)(response, new ApiError_1.default(httpStatus.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED)));
            }
        }))();
    })
        .then(() => next())
        .catch((err) => next(err));
});
exports.encryptResponseMiddleware = encryptResponseMiddleware;
