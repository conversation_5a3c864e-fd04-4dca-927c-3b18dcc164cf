import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  AllowNull,
  ForeignKey,
  BelongsTo,
  Default,
  CreatedAt,
  UpdatedAt
} from "sequelize-typescript";
import User from "./user.model";
import Chat from "./chat.model";

export interface MessageI {
  id: number;
  chat_id: number;
  sender_id: number;
  receiver_id: number;
  content: string;
  is_encrypted: boolean;
  is_delivered: boolean;
  is_read: boolean;
  delivered_at: Date | null;
  read_at: Date | null;
  createdAt?: Date;
  updatedAt?: Date;
}

@Table({
  tableName: "messages",
  timestamps: true,
})
class Message extends Model implements MessageI {
  @PrimaryKey
  @AutoIncrement
  @Column
  id: number;

  @ForeignKey(() => Chat)
  @AllowNull(false)
  @Column
  chat_id: number;

  @BelongsTo(() => Chat, { foreignKey: 'chat_id' })
  chat: Chat;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column
  sender_id: number;

  @BelongsTo(() => User, { foreignKey: 'sender_id', as: 'sender' })
  sender: User;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column
  receiver_id: number;

  @BelongsTo(() => User, { foreignKey: 'receiver_id', as: 'receiver' })
  receiver: User;

  @AllowNull(false)
  @Column(DataType.TEXT)
  content: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  is_encrypted: boolean;

  @Default(false)
  @Column(DataType.BOOLEAN)
  is_delivered: boolean;

  @Default(false)
  @Column(DataType.BOOLEAN)
  is_read: boolean;

  @AllowNull(true)
  @Column(DataType.DATE)
  delivered_at: Date | null;

  @AllowNull(true)
  @Column(DataType.DATE)
  read_at: Date | null;

}

export default Message;
