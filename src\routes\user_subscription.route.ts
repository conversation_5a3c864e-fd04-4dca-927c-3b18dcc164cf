import express from "express";
import { auth } from "../middlewares/auth";
import UserSubscriptionController from "../app/subscription/user_subscription/user_subscription.controller";
import { validate } from "../middlewares/middleware";
import { userSubscriptionValidation } from "../validations/user_subscription.validation";

const router = express.Router();
router.put("/:id/status", auth, UserSubscriptionController.updateStatus);

router.get("", auth, UserSubscriptionController.getAll);
router.post("", auth, validate(userSubscriptionValidation), UserSubscriptionController.create);
router.put("/:id", auth, validate(userSubscriptionValidation), UserSubscriptionController.update);
router.get("/:id", auth, UserSubscriptionController.showById);
router.delete("/:id", auth, UserSubscriptionController.delete);

export default router;
