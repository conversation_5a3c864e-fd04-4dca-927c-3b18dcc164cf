"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const response_1 = __importStar(require("../../utils/response"));
const subscription_service_1 = __importDefault(require("./subscription.service"));
class SubscriptionController {
    constructor() { }
}
_a = SubscriptionController;
SubscriptionController.subscriptionService = subscription_service_1.default;
// Purchase subscription
SubscriptionController.purchaseSubscription = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { plan_id, payment_id } = request.body;
        const user_id = request.decoded;
        const result = yield _a.subscriptionService.purchaseSubscription({
            user_id,
            plan_id,
            payment_id
        });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: result.message,
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Track usage
SubscriptionController.trackUsage = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { action_type, target_user_id } = request.body;
        const user_id = request.decoded.id;
        const result = yield _a.subscriptionService.trackUsage({
            user_id,
            action_type,
            target_user_id
        });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: result.message,
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Get subscription status
SubscriptionController.getSubscriptionStatus = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user_id = request.decoded.id;
        const status = yield _a.subscriptionService.getActiveSubscription(user_id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: status.message,
            data: status,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Check if user can perform action
SubscriptionController.canPerformAction = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { action_type, target_user_id } = request.query;
        const user_id = request.decoded.id;
        const result = yield _a.subscriptionService.canPerformAction(user_id, action_type, target_user_id ? Number(target_user_id) : undefined);
        console.log('result: ', result);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: result.message,
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Get subscription history
SubscriptionController.getSubscriptionHistory = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user_id = request.decoded.id;
        const history = yield _a.subscriptionService.getUserSubscriptionHistory(user_id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Subscription history retrieved successfully",
            data: { subscriptions: history },
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Admin: Check expired subscriptions
SubscriptionController.checkExpiredSubscriptions = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = yield _a.subscriptionService.checkAndUpdateExpiredSubscriptions();
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: `Updated ${result.updatedCount} expired subscriptions`,
            data: result,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Get user subscription details by ID
SubscriptionController.getSubscriptionById = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { subscription_id } = request.params;
        const user_id = request.decoded.id;
        // This would need to be implemented in the service
        // For now, just return the active subscription
        const status = yield _a.subscriptionService.getActiveSubscription(user_id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Subscription details retrieved successfully",
            data: status,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Cancel subscription
SubscriptionController.cancelSubscription = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user_id = request.decoded.id;
        const { reason } = request.body;
        // This would need to be implemented in the service
        // For now, just deactivate the subscription
        const status = yield _a.subscriptionService.getActiveSubscription(user_id);
        if (!status.isActive || !status.subscription) {
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.BAD_REQUEST,
                message: "No active subscription to cancel",
                data: null,
            });
        }
        yield status.subscription.update({
            is_active: false,
            revoked_at: new Date()
        });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Subscription cancelled successfully",
            data: { cancelled: true, reason },
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = SubscriptionController;
