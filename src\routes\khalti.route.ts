import express from "express";
import KhaltiController from "../app/khalti/khalti.controller";
import { auth } from "../middlewares/auth";
import { validate } from "../middlewares/middleware";
import {
    initiatePaymentValidation,
    verifyPaymentValidation,
    getPaymentByIdValidation,
    getUserPaymentsValidation,
    getTransactionsValidation,
    getAnalyticsValidation,
    checkPaymentStatusValidation,
    webhookValidation
} from "../validations/khalti.validation";

const router = express.Router();

// Public routes
router.get("/config", KhaltiController.getPaymentConfig);
// router.post("/webhook", validate(webhookValidation), KhaltiController.handleWebhook);

// Protected routes (require authentication)
router.use(auth);

// Payment operations
router.post("/initiate", validate(initiatePaymentValidation), KhaltiController.initiatePayment);
router.post("/verify", validate(verifyPaymentValidation), KhaltiController.verifyPayment);
router.get("/status/:pidx", validate(checkPaymentStatusValidation), KhaltiController.checkPaymentStatus);

// User payment management
router.get("/payments", validate(getUserPaymentsValidation), KhaltiController.getUserPayments);
router.get("/payments/:payment_id", validate(getPaymentByIdValidation), KhaltiController.getPaymentById);

// Admin routes (you may want to add admin role check middleware)
router.get("/admin/transactions", validate(getTransactionsValidation), KhaltiController.getTransactions);
router.get("/admin/analytics", validate(getAnalyticsValidation), KhaltiController.getAnalytics);

export default router;
