import {
    AllowNull,
    AutoIncrement,
    BelongsTo,
    BelongsToMany,
    Column,
    DataType,
    Default,
    HasMany,
    Model,
    NotEmpty,
    PrimaryKey,
    Table
} from "sequelize-typescript";
import City from "./city.model";

export interface CountryI {
    id: number;
    name: string;
    phoneCode: string;
    timezone: string;
    currency: string;
    currency_symbol: string;
    flag: string;
    is_active: boolean;
}

@Table({
    tableName: "countries",
    timestamps: true
})
class Country extends Model<CountryI> implements CountryI {
    @AutoIncrement
    @PrimaryKey
    @Column
    id: number;
  
    @AllowNull(false)
    @NotEmpty
    @Column(DataType.STRING)
    name: string;
  
    @AllowNull(false)
    @Column(DataType.STRING)
    phoneCode: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    timezone: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    currency: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    currency_symbol: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    flag: string;

    @AllowNull(false)
    @Default(true)
    @Column(DataType.BOOLEAN)
    is_active: boolean;
  
    @HasMany(() => City, { foreignKey: "country_id", onDelete: "CASCADE", })
    cities: City[];
}

export default Country;


// CREATE TABLE countries (
//     id INT AUTO_INCREMENT PRIMARY KEY,
//     name VARCHAR(100) NOT NULL,
//     iso_code VARCHAR(10),
//     dial_code VARCHAR(10),
//     code VARCHAR(10),
//     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
//     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
//   );
