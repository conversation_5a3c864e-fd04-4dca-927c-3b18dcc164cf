{"name": "Bar-Badhu", "version": "1.0.0", "main": "index.js", "scripts": {"start": "npx nodemon", "migrate:seed": "npx sequelize-cli db:seed:all", "migrate:undo": "npx sequelize-cli db:migrate:undo:all", "migrate:reset": "npm run migrate:undo && npm run migrate", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "node version : 22.5.0", "devDependencies": {"@types/basic-auth": "^1.1.8", "@types/crypto-js": "^4.2.2", "@types/express": "^5.0.0", "@types/express-fileupload": "^1.5.1", "@types/http-status": "^0.2.30", "@types/node": "^22.10.2", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.7", "@types/uuid": "^10.0.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.2"}, "dependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.13", "@types/node-cron": "^3.0.11", "@types/sequelize": "^4.28.20", "@types/socket.io": "^3.0.1", "@types/validator": "^13.12.2", "basic-auth": "^2.0.1", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-fileupload": "^1.5.1", "express-session": "^1.18.1", "http-status": "^2.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "mysql2": "^3.12.0", "node-cron": "^4.1.0", "nodemailer": "^6.10.0", "nodemon": "^3.1.9", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.2", "sequelize-typescript": "^2.1.6", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.5.2"}}