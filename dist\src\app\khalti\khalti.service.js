"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const http_status_1 = __importDefault(require("http-status"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const khalti_payment_model_1 = __importDefault(require("../../database/models/khalti_payment.model"));
const khalti_webhook_event_model_1 = __importDefault(require("../../database/models/khalti_webhook_event.model"));
const user_model_1 = __importDefault(require("../../database/models/user.model"));
const subscription_plans_mode_1 = __importDefault(require("../../database/models/subscription_plans.mode"));
const user_subscriptions_model_1 = __importDefault(require("../../database/models/user_subscriptions.model"));
const sequelize_1 = require("sequelize");
const crypto_1 = __importDefault(require("crypto"));
const khalti_secret_key = process.env.KHALTI_SECRET_KEY;
const khalti_public_key = process.env.KHALTI_PUBLIC_KEY;
const khalti_api_url = process.env.KHALTI_API_URL || "https://a.khalti.com/api/v2";
class KhaltiService {
    constructor() { }
}
_a = KhaltiService;
// Initiate payment using Khalti Payment Gateway
KhaltiService.initiatePayment = (params) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c, _d, _e;
    try {
        const { user_id, plan_id, amount, mobile, product_identity, product_name, product_url, return_url, website_url } = params;
        // Get subscription plan details if plan_id is provided
        let planDetails = null;
        if (plan_id) {
            planDetails = yield subscription_plans_mode_1.default.findByPk(plan_id);
            if (!planDetails) {
                throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Subscription plan not found");
            }
        }
        // Prepare payment data
        const paymentData = {
            return_url: return_url || `${process.env.FRONTEND_URL}/payment/success`,
            website_url: website_url || process.env.FRONTEND_URL,
            amount: amount * 100, // Khalti expects amount in paisa
            purchase_order_id: `ORDER_${Date.now()}_${user_id}`,
            purchase_order_name: planDetails ?
                `Subscription: ${planDetails.name}` :
                product_name || "BarBadhu Payment",
            customer_info: {
                name: "Customer",
                email: "<EMAIL>",
                phone: mobile || "9800000000"
            },
            product_details: [
                {
                    identity: product_identity || `PRODUCT_${Date.now()}`,
                    name: planDetails ? planDetails.name : (product_name || "BarBadhu Service"),
                    total_price: amount * 100,
                    quantity: 1,
                    unit_price: amount * 100
                }
            ]
        };
        // Make request to Khalti API
        console.log('khalti_api_url: ', khalti_api_url);
        console.log('khalti_secret_key: ', khalti_secret_key);
        const response = yield axios_1.default.post(`${khalti_api_url}/epayment/initiate/`, paymentData, {
            headers: {
                'Authorization': `Key ${khalti_secret_key}`,
                'Content-Type': 'application/json',
            },
        });
        // Save payment record to database
        const paymentRecord = yield khalti_payment_model_1.default.create({
            user_id,
            plan_id,
            pidx: response.data.pidx,
            khalti_order_id: paymentData.purchase_order_id,
            amount: amount,
            currency: "NPR",
            status: "pending",
            payment_method: "khalti",
            description: paymentData.purchase_order_name,
            khalti_response: response.data,
            mobile,
            product_identity: paymentData.product_details[0].identity,
            product_name: paymentData.product_details[0].name,
            product_url,
            return_url: paymentData.return_url,
            website_url: paymentData.website_url,
        });
        return Object.assign(Object.assign({}, response.data), { payment_id: paymentRecord.id, order_id: paymentData.purchase_order_id });
    }
    catch (error) {
        console.error('Khalti initiate payment error:', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, ((_c = (_b = error.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.detail) || ((_e = (_d = error.response) === null || _d === void 0 ? void 0 : _d.data) === null || _e === void 0 ? void 0 : _e.message) || error.message);
    }
});
// Verify payment status
KhaltiService.verifyPayment = (pidx, user_id) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c, _d, _e;
    try {
        // Find the payment record
        const paymentRecord = yield khalti_payment_model_1.default.findOne({
            where: { pidx, user_id }
        });
        if (!paymentRecord) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Payment record not found");
        }
        // Verify payment with Khalti
        const response = yield axios_1.default.post(`${khalti_api_url}/epayment/lookup/`, { pidx }, {
            headers: {
                'Authorization': `Key ${khalti_secret_key}`,
                'Content-Type': 'application/json',
            },
        });
        const paymentStatus = response.data.status;
        let status = "pending";
        switch (paymentStatus) {
            case "Completed":
                status = "completed";
                break;
            case "Pending":
                status = "pending";
                break;
            case "Refunded":
                status = "refunded";
                break;
            case "Canceled":
                status = "cancelled";
                break;
            case "Expired":
                status = "expired";
                break;
            default:
                status = "failed";
        }
        // Update payment record
        yield paymentRecord.update({
            status,
            khalti_transaction_id: response.data.transaction_id,
            khalti_payment_id: response.data.pidx,
            khalti_response: response.data,
        });
        // Activate subscription if payment is completed and plan_id exists
        if (status === "completed" && paymentRecord.plan_id) {
            yield _a.activateUserSubscription(paymentRecord.user_id, paymentRecord.plan_id);
        }
        return Object.assign(Object.assign({}, response.data), { payment_id: paymentRecord.id, local_status: status });
    }
    catch (error) {
        console.error('Khalti verify payment error:', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, ((_c = (_b = error.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.detail) || ((_e = (_d = error.response) === null || _d === void 0 ? void 0 : _d.data) === null || _e === void 0 ? void 0 : _e.message) || error.message);
    }
});
// Process webhook
KhaltiService.processWebhook = (webhookData, signature) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        // Verify webhook signature
        const isValid = _a.verifyWebhookSignature(webhookData, signature);
        if (!isValid) {
            throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, "Invalid webhook signature");
        }
        // Save webhook event
        const webhookEvent = yield khalti_webhook_event_model_1.default.create({
            event_id: webhookData.event_id || `WEBHOOK_${Date.now()}`,
            event_type: webhookData.type || "payment.status.changed",
            resource_type: "payment",
            resource_id: (_b = webhookData.data) === null || _b === void 0 ? void 0 : _b.pidx,
            summary: `Khalti webhook: ${webhookData.type}`,
            event_data: webhookData,
            processed: false,
        });
        // Process the webhook based on event type
        try {
            switch (webhookData.type) {
                case "payment.completed":
                    yield _a.handlePaymentCompleted(webhookData);
                    break;
                case "payment.failed":
                    yield _a.handlePaymentFailed(webhookData);
                    break;
                case "payment.refunded":
                    yield _a.handlePaymentRefunded(webhookData);
                    break;
                default:
                    console.log(`Unhandled webhook event type: ${webhookData.type}`);
            }
            // Mark webhook as processed
            yield webhookEvent.update({
                processed: true,
                processed_at: new Date(),
            });
        }
        catch (processingError) {
            // Mark webhook as failed
            yield webhookEvent.update({
                processed: false,
                error_message: processingError.message,
            });
            throw processingError;
        }
        return { success: true, webhook_id: webhookEvent.id };
    }
    catch (error) {
        console.error('Khalti webhook processing error:', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
// Verify webhook signature
KhaltiService.verifyWebhookSignature = (payload, signature) => {
    try {
        const payloadString = JSON.stringify(payload);
        const expectedSignature = crypto_1.default
            .createHmac('sha256', khalti_secret_key)
            .update(payloadString)
            .digest('hex');
        return crypto_1.default.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature));
    }
    catch (error) {
        console.error('Webhook signature verification error:', error);
        return false;
    }
};
// Get payment by ID
KhaltiService.getPaymentById = (payment_id, user_id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const payment = yield khalti_payment_model_1.default.findOne({
            where: { id: payment_id, user_id },
            include: [
                {
                    model: user_model_1.default,
                    as: 'user',
                    attributes: ['id', 'first_name', 'last_name', 'email']
                },
                {
                    model: subscription_plans_mode_1.default,
                    as: 'subscriptionPlan',
                    attributes: ['id', 'name', 'price', 'duration_days']
                }
            ]
        });
        if (!payment) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Payment not found");
        }
        return payment;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
// Get user payments with pagination
KhaltiService.getUserPayments = (user_id, filters) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, status } = filters;
        const offset = (page - 1) * limit;
        const whereCondition = { user_id };
        if (status) {
            whereCondition.status = status;
        }
        const { count, rows } = yield khalti_payment_model_1.default.findAndCountAll({
            where: whereCondition,
            include: [
                {
                    model: subscription_plans_mode_1.default,
                    as: 'subscriptionPlan',
                    attributes: ['id', 'name', 'price', 'duration_days']
                }
            ],
            order: [["created_at", "DESC"]],
            limit,
            offset,
        });
        return {
            payments: rows,
            pagination: {
                total: count,
                page,
                limit,
                totalPages: Math.ceil(count / limit),
            },
        };
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
// Get all transactions (admin)
KhaltiService.getTransactions = (filters) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, status, search } = filters;
        const offset = (page - 1) * limit;
        const whereCondition = {};
        if (status) {
            whereCondition.status = status;
        }
        const queryOption = {
            where: whereCondition,
            include: [
                {
                    model: user_model_1.default,
                    as: 'user',
                    attributes: ['id', 'first_name', 'last_name', 'email']
                },
                {
                    model: subscription_plans_mode_1.default,
                    as: 'subscriptionPlan',
                    attributes: ['id', 'name', 'price', 'duration_days']
                }
            ],
            order: [["created_at", "DESC"]],
            limit,
            offset,
        };
        if (search) {
            queryOption.include[0].where = {
                [sequelize_1.Op.or]: [
                    { first_name: { [sequelize_1.Op.like]: `%${search}%` } },
                    { last_name: { [sequelize_1.Op.like]: `%${search}%` } },
                    { email: { [sequelize_1.Op.like]: `%${search}%` } }
                ]
            };
        }
        const { count, rows } = yield khalti_payment_model_1.default.findAndCountAll(queryOption);
        return {
            transactions: rows,
            pagination: {
                total: count,
                page,
                limit,
                totalPages: Math.ceil(count / limit),
            },
        };
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
// Get payment analytics
KhaltiService.getAnalytics = (params) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { start_date, end_date, user_id } = params;
        const whereCondition = {};
        if (start_date && end_date) {
            whereCondition.created_at = {
                [sequelize_1.Op.between]: [new Date(start_date), new Date(end_date)]
            };
        }
        if (user_id) {
            whereCondition.user_id = user_id;
        }
        // Total payments
        const totalPayments = yield khalti_payment_model_1.default.count({ where: whereCondition });
        // Total amount
        const totalAmountResult = yield khalti_payment_model_1.default.findOne({
            where: whereCondition,
            attributes: [[sequelize_1.Sequelize.fn('SUM', sequelize_1.Sequelize.col('amount')), 'total']],
            raw: true,
        });
        // Status breakdown
        const statusBreakdown = yield khalti_payment_model_1.default.findAll({
            where: whereCondition,
            attributes: [
                'status',
                [sequelize_1.Sequelize.fn('COUNT', sequelize_1.Sequelize.col('id')), 'count'],
                [sequelize_1.Sequelize.fn('SUM', sequelize_1.Sequelize.col('amount')), 'amount']
            ],
            group: ['status'],
            raw: true,
        });
        // Monthly breakdown (if date range is provided)
        let monthlyBreakdown = [];
        if (start_date && end_date) {
            monthlyBreakdown = yield khalti_payment_model_1.default.findAll({
                where: whereCondition,
                attributes: [
                    [sequelize_1.Sequelize.fn('DATE_FORMAT', sequelize_1.Sequelize.col('created_at'), '%Y-%m'), 'month'],
                    [sequelize_1.Sequelize.fn('COUNT', sequelize_1.Sequelize.col('id')), 'count'],
                    [sequelize_1.Sequelize.fn('SUM', sequelize_1.Sequelize.col('amount')), 'amount']
                ],
                group: [sequelize_1.Sequelize.fn('DATE_FORMAT', sequelize_1.Sequelize.col('created_at'), '%Y-%m')],
                order: [[sequelize_1.Sequelize.fn('DATE_FORMAT', sequelize_1.Sequelize.col('created_at'), '%Y-%m'), 'ASC']],
                raw: true,
            });
        }
        return {
            totalPayments,
            totalAmount: (totalAmountResult === null || totalAmountResult === void 0 ? void 0 : totalAmountResult.total) || 0,
            statusBreakdown,
            monthlyBreakdown,
        };
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
// Helper methods for webhook processing
KhaltiService.handlePaymentCompleted = (webhookEvent) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c, _d;
    try {
        const pidx = (_b = webhookEvent.data) === null || _b === void 0 ? void 0 : _b.pidx;
        if (pidx) {
            const paymentRecord = yield khalti_payment_model_1.default.findOne({
                where: { pidx }
            });
            if (paymentRecord) {
                yield paymentRecord.update({
                    status: "completed",
                    khalti_transaction_id: (_c = webhookEvent.data) === null || _c === void 0 ? void 0 : _c.transaction_id,
                    khalti_payment_id: (_d = webhookEvent.data) === null || _d === void 0 ? void 0 : _d.pidx,
                    khalti_response: webhookEvent.data,
                });
                // Activate subscription if applicable
                if (paymentRecord.plan_id) {
                    yield _a.activateUserSubscription(paymentRecord.user_id, paymentRecord.plan_id);
                }
            }
        }
    }
    catch (error) {
        console.error('Handle payment completed error:', error);
    }
});
KhaltiService.handlePaymentFailed = (webhookEvent) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        const pidx = (_b = webhookEvent.data) === null || _b === void 0 ? void 0 : _b.pidx;
        if (pidx) {
            const paymentRecord = yield khalti_payment_model_1.default.findOne({
                where: { pidx }
            });
            if (paymentRecord) {
                yield paymentRecord.update({
                    status: "failed",
                    khalti_response: webhookEvent.data,
                });
            }
        }
    }
    catch (error) {
        console.error('Handle payment failed error:', error);
    }
});
KhaltiService.handlePaymentRefunded = (webhookEvent) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c, _d;
    try {
        const pidx = (_b = webhookEvent.data) === null || _b === void 0 ? void 0 : _b.pidx;
        if (pidx) {
            const paymentRecord = yield khalti_payment_model_1.default.findOne({
                where: { pidx }
            });
            if (paymentRecord) {
                yield paymentRecord.update({
                    status: "refunded",
                    refund_id: (_c = webhookEvent.data) === null || _c === void 0 ? void 0 : _c.refund_id,
                    refund_amount: ((_d = webhookEvent.data) === null || _d === void 0 ? void 0 : _d.refund_amount) ? webhookEvent.data.refund_amount / 100 : 0,
                    khalti_response: webhookEvent.data,
                });
                // Deactivate subscription if applicable
                if (paymentRecord.plan_id) {
                    yield user_subscriptions_model_1.default.update({ is_active: false, revoked_at: new Date() }, { where: { user_id: paymentRecord.user_id, plan_id: paymentRecord.plan_id, is_active: true } });
                }
            }
        }
    }
    catch (error) {
        console.error('Handle payment refunded error:', error);
    }
});
// Activate user subscription
KhaltiService.activateUserSubscription = (user_id, plan_id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const plan = yield subscription_plans_mode_1.default.findByPk(plan_id);
        if (!plan) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Subscription plan not found");
        }
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(startDate.getDate() + plan.duration_days);
        // Deactivate any existing active subscriptions
        yield user_subscriptions_model_1.default.update({ is_active: false, revoked_at: new Date() }, { where: { user_id, is_active: true } });
        // Create new subscription
        const subscription = yield user_subscriptions_model_1.default.create({
            user_id,
            plan_id,
            payment_status: "completed",
            start_date: startDate,
            end_date: endDate,
            auto_renew: false,
            issued_at: startDate,
            expires_at: endDate,
            interest_sent_count: 0,
            contact_viewed_count: 0,
            profile_viewed_count: 0,
            chat_initiated_count: 0,
            is_active: true,
            token: `SUB_${Date.now()}_${user_id}`,
        });
        return subscription;
    }
    catch (error) {
        console.error('Activate user subscription error:', error);
        throw error;
    }
});
exports.default = KhaltiService;
