import { Op } from "sequelize";
import Country from "../../database/models/country.model";
import City, { CityI } from "../../database/models/city.model";
import httpMessages from "../../config/httpMessages";
import { sequelize } from "../../database/database";
import ApiError from "../../utils/ApiError";
import httpStatus from "http-status";

export default class GeolocationService {
  constructor() {}

  /**
   * Return Countries With Cities
   * @param {Object} options
   * @param {number} [options.page] - Current page number (optional)
   * @param {number} [options.limit] - Number of items per page (optional)
   * @param {string} [options.search] - Search term for filtering (optional)
   * @param {number[]} [options.countryIds] - Array of country IDs to filter by (optional)
   * @returns {Promise<Role[]>}
   */
  static getCountriesWithCities = async (options: {
    page?: number;
    limit?: number;
    search?: string;
    countryIds?: number[];
  }) => {
    try {
      const { page, limit, search, countryIds } = options;

      // Build the where condition
      let whereCondition: any = {};

      // Add search condition if provided
      if (search) {
        whereCondition = {
          [Op.or]: [
            {
              name: {
                [Op.like]: `%${search.toLowerCase()}%`,
              },
            },
          ],
        };
      }

      // Add country IDs filter if provided
      if (countryIds && Array.isArray(countryIds) && countryIds.length > 0) {
        whereCondition.id = {
          [Op.in]: countryIds,
        };
      }

      const queryOption: any = {
        where: whereCondition,
        attributes: {
          exclude: ["timezone", "is_active", "createdAt", "updatedAt"],
        },
        include: [
          {
            model: City,
            as: "cities",
            attributes: {
              exclude: ["createdAt", "updatedAt"],
            },
          },
        ],
        order: [["name", "ASC"]],
      };
      // If pagination is provided, apply pagination
      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }
      // Count total items
      const totalItems: any = await Country.count({
        where: whereCondition,
      });

      // Fetch paginated staff data
      const countries = await Country.findAll(queryOption);

      if (page && limit) {
        return {
          totalItems: totalItems,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
          countries: countries,
        };
      } else {
        return countries;
      }
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Create a Countries With Cities
   * @param {Object} countryBody
   * @returns {Promise<Role>}
   */
  static createCountriesWithCities = async (countryBody: any) => {
    const t = await sequelize.transaction();
    try {
      if (await this.getCountryByName(countryBody.name)) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.GEOLOCATION.COUNTRY.NAME_ALREADY_TAKEN
        );
      }

      const country: Country = await Country.create(countryBody, {
        transaction: t,
      });

      if (countryBody.cities) {
        const cityDataArray = countryBody.cities.map((city: any) => ({
          ...city,
          country_id: country.id,
        }));

        const cities: City[] = await City.bulkCreate(cityDataArray, {
          returning: true,
          transaction: t,
        });
      }

      await t.commit();

      return await this.getCountryById(country.id);
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Update a Countries With Cities
   * @param {Object} countryBody
   * @returns {Promise<Role>}
   */
  static updateCountriesWithCities = async (
    countryId: number,
    countryBody: any
  ) => {
    try {
      const existingCountry = await Country.findByPk(countryId);
      if (!existingCountry) {
        throw new ApiError(httpStatus.NOT_FOUND, "Country not found");
      }

      Object.assign(existingCountry, countryBody);
      await existingCountry.save();

      if (countryBody.cities.length) {
        const cityIds: any[] = [];
        for (let index = 0; index < countryBody.cities.length; index++) {
          const city = countryBody.cities[index];
          let dbCity: CityI;
          let cityById = await City.findByPk(city.id);
          if (cityById) {
            Object.assign(cityById, city);
            dbCity = await cityById.save();
            cityIds.push(cityById.id);
          } else {
            city["country_id"] = existingCountry.id;
            dbCity = await City.create(city);
            cityIds.push(dbCity.id);
          }
        }

        if (cityIds.length) {
          await City.destroy({
            where: {
              country_id: existingCountry.id,
              id: {
                [Op.notIn]: cityIds,
              },
            },
          });
        }
      }

      return await this.getCountryById(countryId);
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get Country by id
   * @param {Number} id
   * @returns {Promise<Role>}
   */
  static getCountryById = async (id: number) => {
    try {
      return Country.findOne({
        where: {
          id,
        },
        include: [
          {
            model: City,
            as: "cities",
            attributes: {
              exclude: ["createdAt", "updatedAt"],
            },
          },
        ],
      }).then((data: any) => data?.toJSON());
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get role by rolename
   * @param {string} country_Name
   * @returns {Promise<Role>}
   */
  static getCountryByName = async (country_Name: string) => {
    return Country.findOne({
      where: {
        name: country_Name,
      },
    });
  };

  /**
   * Return countries
   * @returns {Promise<Country[]>}
   */
  static getCountries = async () => {
    try {
      return await Country.findAll({
        attributes: {
          exclude: ["timezone", "is_active", "createdAt", "updatedAt"],
        },
      });
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Delete role by id
   * @param {Number} countryId
   * @returns {Promise<Role>}
   */
  static deleteCountryById = async (countryId: number) => {
    try {
      const country: any = await Country.findByPk(countryId);
      if (!country) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.GEOLOCATION.COUNTRY.NOT_FOUND);
      }
      await country.destroy();
      return country;
    } catch (error: any) {
      if (error.name === "SequelizeForeignKeyConstraintError") {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          "Cannot delete this Record as it is referenced in another table."
        );
      } else {
        throw new ApiError(
          error.status || httpStatus.BAD_REQUEST,
          error.message || "Error deleting Role."
        );
      }
    }
  };

  /**
   * Return cities
   * @returns {Promise<City[]>}
   */
  static getCitiesByCountry = async (countryId: number) => {
    try {
      return await City.findAll({
        where: {
          country_id: countryId,
        },
      });
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };
}
