"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const sequelize_1 = require("sequelize");
const subscription_plans_mode_1 = __importDefault(require("../../database/models/subscription_plans.mode"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
class SubscriptionPlanService {
    constructor() { }
}
_a = SubscriptionPlanService;
/**
 * Create a SubscriptionPlan
 * @param {Object} body
 * @returns {Promise<SubscriptionPlan>}
 */
SubscriptionPlanService.createSubscriptionPlan = (body) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const details = yield subscription_plans_mode_1.default.create(body);
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Return SubscriptionPlans
 * @param {Object} options
 * @param {number} [options.page] - Current page number (optional)
 * @param {number} [options.limit] - Number of items per page (optional)
 * @param {string} [options.search] - Search term for filtering (optional)
 * @returns {Promise<SubscriptionPlan[]>}
 */
SubscriptionPlanService.getSubscriptionPlans = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search } = options;
        const whereCondition = search
            ? {
                [sequelize_1.Op.or]: [
                    { name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                ],
            }
            : {};
        const queryOption = {
            where: whereCondition,
            order: [["createdAt", "DESC"]],
        };
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const data = yield subscription_plans_mode_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: data.count,
                totalPages: Math.ceil(data.count / limit),
                currentPage: page,
                subscription_plans: data.rows,
            };
        }
        else {
            return data.rows;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get SubscriptionPlan by id
 * @param {Number} id
 * @returns {Promise<SubscriptionPlan>}
 */
SubscriptionPlanService.getSubscriptionPlanById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return subscription_plans_mode_1.default.findOne({
            where: { id },
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update role by id
 * @param {Number} Id
 * @param {Object} updateBody
 * @returns {Promise<Role>}
 */
SubscriptionPlanService.updateSubscriptionPlanById = (Id, updateBody) => __awaiter(void 0, void 0, void 0, function* () {
    const details = yield subscription_plans_mode_1.default.findByPk(Id);
    if (!details) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.SUBSCRIPTION_PLAN.NOT_FOUND);
    }
    Object.assign(details, updateBody);
    yield details.save();
    return details;
});
/**
 * Delete role by id
 * @param {Number} Id
 * @returns {Promise<Role>}
 */
SubscriptionPlanService.deleteSubscriptionPlanById = (Id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const details = yield subscription_plans_mode_1.default.findByPk(Id);
        if (!details) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.ROLES.NOT_FOUND);
        }
        yield details.destroy();
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting Role.");
    }
});
exports.default = SubscriptionPlanService;
