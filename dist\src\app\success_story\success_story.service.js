"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const success_story_model_1 = __importDefault(require("../../database/models/success_story.model"));
const success_story_media_model_1 = __importDefault(require("../../database/models/success_story_media.model"));
const user_model_1 = __importDefault(require("../../database/models/user.model"));
const sequelize_1 = require("sequelize");
const email_service_1 = __importDefault(require("../../common/services/email.service"));
class SuccessStoryService {
}
_a = SuccessStoryService;
/**
 * Get all success stories with pagination and search
 * @param {Object} options - Query options
 * @returns {Promise<Object>}
 */
SuccessStoryService.getSuccessStories = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, search = "", status } = options;
        const offset = (page - 1) * limit;
        const whereClause = {};
        if (search) {
            whereClause[sequelize_1.Op.or] = [
                { bride_name: { [sequelize_1.Op.like]: `%${search}%` } },
                { groom_name: { [sequelize_1.Op.like]: `%${search}%` } },
                { bride_email: { [sequelize_1.Op.like]: `%${search}%` } },
                { groom_email: { [sequelize_1.Op.like]: `%${search}%` } },
            ];
        }
        if (status) {
            whereClause.status = status;
        }
        const { count, rows } = yield success_story_model_1.default.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: user_model_1.default,
                    attributes: ["id", "first_name", "last_name", "email"],
                },
                {
                    model: success_story_media_model_1.default,
                    attributes: ["id", "media_type", "media_path"],
                },
            ],
            limit,
            offset,
            order: [["createdAt", "DESC"]],
        });
        if (page && limit) {
            return {
                totalItems: count,
                totalPages: Math.ceil(count / limit),
                currentPage: page,
                success_stories: rows,
            };
        }
        else {
            return rows;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get approved success stories for public display
 * @param {Object} options - Query options
 * @returns {Promise<Object>}
 */
SuccessStoryService.getApprovedSuccessStories = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10 } = options;
        const offset = (page - 1) * limit;
        const { count, rows } = yield success_story_model_1.default.findAndCountAll({
            where: { status: "approved" },
            include: [
                {
                    model: success_story_media_model_1.default,
                    attributes: ["id", "media_type", "media_path"],
                },
            ],
            limit,
            offset,
            order: [["createdAt", "DESC"]],
        });
        return {
            total: count,
            totalPages: Math.ceil(count / limit),
            currentPage: page,
            data: rows,
        };
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get success stories by user ID
 * @param {number} userId - User ID
 * @param {Object} options - Query options
 * @returns {Promise<Object>}
 */
SuccessStoryService.getUserSuccessStories = (userId, options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, status } = options;
        const whereCondition = search
            ? {
                [sequelize_1.Op.or]: [
                    { bride_name: { [sequelize_1.Op.like]: `%${search}%` } },
                    { groom_name: { [sequelize_1.Op.like]: `%${search}%` } },
                    { bride_email: { [sequelize_1.Op.like]: `%${search}%` } },
                    { groom_email: { [sequelize_1.Op.like]: `%${search}%` } },
                ],
            }
            : {};
        if (status) {
            whereCondition.status = status;
        }
        const queryOption = {
            where: whereCondition,
            include: [
                {
                    model: success_story_media_model_1.default,
                    attributes: ["id", "media_type", "media_path"],
                },
            ],
            order: [["createdAt", "DESC"]],
        };
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const data = yield success_story_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: data.count,
                totalPages: Math.ceil(data.count / limit),
                currentPage: page,
                success_stories: data.rows,
            };
        }
        else {
            return data.rows[0];
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Create a new success story
 * @param {Object} data - Success story data
 * @param {Array} mediaFiles - Media files
 * @returns {Promise<SuccessStory>}
 */
SuccessStoryService.createSuccessStory = (data, mediaFiles) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const successStory = yield success_story_model_1.default.create(data);
        if (mediaFiles && mediaFiles.length > 0) {
            const mediaPromises = mediaFiles.map((file) => {
                return success_story_media_model_1.default.create({
                    story_id: successStory.id,
                    media_type: "image", // Assuming all are images for now
                    media_path: file,
                });
            });
            yield Promise.all(mediaPromises);
        }
        // Send confirmation email
        const user = yield user_model_1.default.findByPk(data.user_id);
        if (user) {
            yield email_service_1.default.sendSuccessStorySubmissionEmail(user.email, {
                userName: `${user.first_name} ${user.last_name}`,
                storyId: successStory.id,
            });
        }
        return successStory;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get success story by ID
 * @param {number} id - Success story ID
 * @returns {Promise<SuccessStory>}
 */
SuccessStoryService.getSuccessStoryById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const successStory = yield success_story_model_1.default.findByPk(id, {
            include: [
                {
                    model: user_model_1.default,
                    attributes: ["id", "first_name", "last_name", "email"],
                },
                {
                    model: success_story_media_model_1.default,
                    attributes: ["id", "media_type", "media_path"],
                },
            ],
        });
        if (!successStory) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Success story not found");
        }
        return successStory;
    }
    catch (error) {
        throw new ApiError_1.default(error.statusCode || http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update success story status
 * @param {number} id - Success story ID
 * @param {Object} data - Status data
 * @returns {Promise<SuccessStory>}
 */
SuccessStoryService.updateSuccessStoryStatus = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const successStory = yield success_story_model_1.default.findByPk(id, {
            include: [
                {
                    model: user_model_1.default,
                    attributes: ["id", "first_name", "last_name", "email"],
                },
            ],
        });
        if (!successStory) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Success story not found");
        }
        yield successStory.update(data);
        // Send status update email
        if (successStory.user && successStory.user.email) {
            yield email_service_1.default.sendSuccessStoryStatusUpdateEmail(successStory.user.email, {
                userName: `${successStory.user.first_name} ${successStory.user.last_name}`,
                storyId: successStory.id,
                status: data.status,
                adminNotes: data.admin_notes || "",
            });
        }
        return successStory;
    }
    catch (error) {
        throw new ApiError_1.default(error.statusCode || http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Delete success story
 * @param {number} id - Success story ID
 * @returns {Promise<boolean>}
 */
SuccessStoryService.deleteSuccessStory = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const successStory = yield success_story_model_1.default.findByPk(id);
        if (!successStory) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Success story not found");
        }
        yield success_story_media_model_1.default.destroy({ where: { story_id: id } });
        yield successStory.destroy();
        return true;
    }
    catch (error) {
        throw new ApiError_1.default(error.statusCode || http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = SuccessStoryService;
