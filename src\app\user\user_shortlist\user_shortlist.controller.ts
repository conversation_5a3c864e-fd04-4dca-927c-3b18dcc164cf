import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../../utils/catchAsync";
import UserShortlistService from "./user_shortlist.service";
import errorResponse, { sentResponse } from "../../../utils/response";
import httpMessages from "../../../config/httpMessages";
import Role from "../../../database/models/role.model";
import ApiError from "../../../utils/ApiError";
import UserShortlist from "../../../database/models/user_shortlist.model";

export default class UserShortlistController {
    static userShortlistService = UserShortlistService;
    constructor() { }


    static getAll = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page, limit, search } = request.query;
            const option = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
                search: search ? (search as string) : "",
            };
            const userId = request.decoded;
            const shortlists = await this.userShortlistService.getShortlists(option,userId);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Shortlists retrieved successfully",
                data: shortlists,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static create = catchAsync(async (request: Request, response: Response) => {
        try {
            let shortlistnData = { ...request.body };
            const userId = request.decoded;
            shortlistnData['user_id'] = userId
            const shortlist: UserShortlist = await this.userShortlistService.createShortlist(shortlistnData);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Shortlist created successfully',
                data: shortlist,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static showById = catchAsync(async (request: Request, response: Response) => {
        try {
            const roleId: number = parseInt(request.params.id, 10);
            const role = await this.userShortlistService.getShortlistById(roleId);
            if (!role) {
                throw new ApiError(httpStatus.NOT_FOUND, httpMessages.ROLES.NOT_FOUND);
            }
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Shortlist retrieved successfully',
                data: role,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static update = catchAsync(async (request: Request, response: Response) => {
        try {
            const roleId: number = parseInt(request.params.id, 10);
            const roleData = { ...request.body };

            const role = await this.userShortlistService.updateShortlistById(roleId, roleData);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Shortlist updated successfully',
                data: role,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static delete = catchAsync(async (request: Request, response: Response) => {
        try {
            const id: number = parseInt(request.params.id, 10);
            await this.userShortlistService.deleteShortlistById(id);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: 'Shortlist deleted successfully',
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });


}
