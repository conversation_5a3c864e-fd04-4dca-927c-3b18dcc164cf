"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middlewares/auth");
const fileUploadMiddleware_1 = require("../middlewares/fileUploadMiddleware");
const user_gallery_controller_1 = __importDefault(require("../app/user/user_gallery/user_gallery.controller"));
const router = express_1.default.Router();
router.get("", auth_1.auth, user_gallery_controller_1.default.getAll);
router.post("", auth_1.auth, fileUploadMiddleware_1.fileUploadMiddleware, user_gallery_controller_1.default.create);
router.put("/:id", auth_1.auth, fileUploadMiddleware_1.fileUploadMiddleware, user_gallery_controller_1.default.update);
router.get("/:id", auth_1.auth, user_gallery_controller_1.default.showById);
router.delete("/:id", auth_1.auth, user_gallery_controller_1.default.delete);
exports.default = router;
