import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import sentResponse from "../../utils/response";
import errorResponse from "../../utils/response";
import MessageService from "./message.service";
import ChatService from "../chat/chat.service";
import { getActiveUsers } from "../../socket";

export default class MessageController {
  static messageService = MessageService;
  static chatService = ChatService;

  /**
   * Send a new message
   * @route POST /api/messages
   */
  static sendMessage = catchAsync(async (request: Request, response: Response) => {
    try {
      const { chatId, receiverId, content } = request.body;
      const senderId = request.decoded;
      
      // Verify chat exists and user has access
      const chat = await this.chatService.getChatById(chatId,senderId);
      if (chat.user1_id !== senderId && chat.user2_id !== senderId) {
        return errorResponse(response, {
          statusCode: httpStatus.FORBIDDEN,
          message: "You do not have permission to send messages in this chat",
        });
      }
      
      const message = await this.messageService.sendMessage(
        chatId,
        senderId,
        receiverId,
        content
      );
      
      return sentResponse(response, {
        statusCode: httpStatus.CREATED,
        message: "Message sent successfully",
        data: message,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  /**
   * Mark message as delivered
   * @route PUT /api/messages/:id/delivered
   */
  static markAsDelivered = catchAsync(async (request: Request, response: Response) => {
    try {
      const messageId = parseInt(request.params.id);
      
      const message = await this.messageService.markAsDelivered(messageId);
      
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Message marked as delivered",
        data: message,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  /**
   * Mark message as read
   * @route PUT /api/messages/:id/read
   */
  static markAsRead = catchAsync(async (request: Request, response: Response) => {
    try {
      const messageId = parseInt(request.params.id);
      
      const message = await this.messageService.markAsRead(messageId);
      
      // Notify sender through socket if they're online
      const activeUsers = getActiveUsers();
      const senderSocketId = activeUsers.get(message.sender_id);
      
      if (senderSocketId) {
        // Socket notification is handled in socket/index.ts
      }
      
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Message marked as read",
        data: message,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  /**
   * Mark all messages in a chat as read
   * @route PUT /api/chats/:id/read-all
   */
  static markAllAsRead = catchAsync(async (request: Request, response: Response) => {
    try {
      const chatId = parseInt(request.params.id);
      const userId = request.decoded;
      
      // Verify chat exists and user has access
      const chat = await this.chatService.getChatById(chatId,userId);
      if (chat.user1_id !== userId && chat.user2_id !== userId) {
        return errorResponse(response, {
          statusCode: httpStatus.FORBIDDEN,
          message: "You do not have permission to access this chat",
        });
      }
      
      const count = await this.messageService.markAllAsRead(chatId, userId);
      
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: `${count} messages marked as read`,
        data: { count },
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });
}
