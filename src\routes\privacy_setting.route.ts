import express from "express";
import { auth } from "../middlewares/auth";
import PrivacySettingController from "../app/privacy_setting/privacy_setting.controller";
import { validate } from "../middlewares/middleware";
import { privacySettingValidation } from "../validations/privacy_setting.validation";


const router = express.Router();

router.get("",auth, PrivacySettingController.getAll);
router.post("",auth,validate(privacySettingValidation), PrivacySettingController.create);
router.post("/hide-profile",auth, PrivacySettingController.hideProfile);
router.post("/delete-profile",auth, PrivacySettingController.deleteProfile);

router.post("/resend-email-otp",auth, PrivacySettingController.resendEmailOtp);
router.post("/verify-email-otp",auth, PrivacySettingController.verifyEmailOtp);

router.post("/send-phone-otp", auth, PrivacySettingController.sendPhoneOtp);
router.post("/verify-phone-otp", auth, PrivacySettingController.verifyPhoneOtp);

router.get("/:id",auth, PrivacySettingController.showById);
router.put("/:id",auth, PrivacySettingController.update);
router.delete("/:id",auth, PrivacySettingController.delete);

export default router;
