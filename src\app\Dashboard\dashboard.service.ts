import httpStatus from "http-status";
import ApiError from "../../utils/ApiError";
import httpMessages from "../../config/httpMessages";
import User from "../../database/models/user.model";
import Inquiry from "../../database/models/inquiries.model";
import Chat from "../../database/models/chat.model";
import Message from "../../database/models/message.model";
import SuccessStory from "../../database/models/success_story.model";
import HelpQuestion from "../../database/models/help_question.model";
import UserBasicDetails from "../../database/models/user_basic_details.model";
import UserLocationDetails from "../../database/models/user_location_details.model";
import UserEducationCareer from "../../database/models/user_education_career.model";
import UserLifestyle from "../../database/models/user_lifestyle.model";
import UserFamilyDetails from "../../database/models/user_family_details.model";
import UserAstroDetails from "../../database/models/user_astro_details.model";
import UserHobbies from "../../database/models/user_hobbies.model";
import UserGallery from "../../database/models/user_gallery.model";
import UserInvitation from "../../database/models/user_invitation.model";
import { Op } from "sequelize";
import UserPreference from "../../database/models/user_preferences.model";
import UserVerification from "../../database/models/user_verifications.model";
import UserSubscription from "../../database/models/user_subscriptions.model";

export default class DashboardService {
  constructor() {}

  static getDashboardData = async (userId: number) => {
    try {
      // Get user profile data
      const user = await User.findOne({
        where: { id: userId },
        attributes: ["id", "first_name", "last_name", "email", "profile_image"],
        include: [
          {
            model: UserBasicDetails,
            as: "basicDetails",
            required: false,
          },
          {
            model: UserLocationDetails,
            as: "locationDetails",
            required: false,
          },
          {
            model: UserEducationCareer,
            as: "educationCareer",
            required: false,
          },
          {
            model: UserLifestyle,
            as: "lifestyle",
            required: false,
          },
          {
            model: UserFamilyDetails,
            as: "familyDetails",
            required: false,
          },
          {
            model: UserAstroDetails,
            as: "astroDetails",
            required: false,
          },
          {
            model: UserHobbies,
            as: "hobbies",
            required: false,
          },
          {
            model: UserGallery,
            as: "userGallery",
            required: false,
          },
          {
            model: UserPreference,
            as: "userPreference",
            required: false,
          },
        ],
      });

      // Calculate profile completion
      const profileCompletion = this.calculateProfileCompletion(user);

      // Get invitation counts
      const invitationCounts = await this.getInvitationCounts(userId);

      return {
        user,
        profileCompletion,
        invitationCounts,
      };
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  static calculateProfileCompletion = (user: any) => {
    const sections = [
      {
        name: "Basic Details",
        isComplete: !!user.basicDetails,
        weight: 20,
        description: "Add your personal details",
        icon: "fas fa-user",
      },
      {
        name: "Location Details",
        isComplete: !!user.locationDetails,
        weight: 15,
        description: "Add your location details",
        icon: "fas fa-map-marker-alt",
      },
      {
        name: "Education & Career",
        isComplete: !!user.educationCareer,
        weight: 15,
        description: "Add your education and career details",
        icon: "fas fa-graduation-cap",
      },
      {
        name: "Lifestyle",
        isComplete: !!user.lifestyle,
        weight: 15,
        description: "Add your lifestyle details",
        icon: "fas fa-shield-heart",
      },
      {
        name: "Family Details",
        isComplete: !!user.familyDetails,
        weight: 15,
        description: "Add your family details",
        icon: "fas fa-users",
      },
      {
        name: "Astro Details",
        isComplete: !!user.astroDetails,
        weight: 10,
        description: "Add your astrological details",
        icon: "fas fa-venus-mars",
      },
      {
        name: "Preferences",
        isComplete: !!user.userPreference,
        weight: 10,
        description: "Add your preferences",
        icon: "fas fa-cog",
      },
      // { name: 'Hobbies & Interests', isComplete: !!user.hobbies, weight: 10,description: 'Add your hobbies and interests',icon:'interests' },
      // { name: 'Profile Bio & Photos', isComplete: !!(user.profileBioDetails && user.profileBioDetails.profile_image), weight: 10,description: 'Add your profile bio and photos',icon:'face' }
    ];

    // Calculate total completion percentage
    let completedWeight = 0;
    sections.forEach((section) => {
      if (section.isComplete) {
        completedWeight += section.weight;
      }
    });

    // Round to nearest whole number
    const completionPercentage = Math.round(completedWeight);

    return {
      completionPercentage,
      sections: sections.map((section) => ({
        name: section.name,
        isComplete: section.isComplete,
        weight: section.weight,
        description: section.description,
        icon: section.icon,
      })),
      pendingSections: sections
        .filter((section) => !section.isComplete)
        .map((section) => section.name),
    };
  };

  static getInvitationCounts = async (userId: number) => {
    // Count invitations by status
    const sentCount = await UserInvitation.count({
      where: { sender_id: userId },
    });

    const receivedCount = await UserInvitation.count({
      where: { receiver_id: userId },
    });

    const acceptedCount = await UserInvitation.count({
      where: {
        [Op.or]: [
          { sender_id: userId, status: "accepted" },
          { receiver_id: userId, status: "accepted" },
        ],
      },
    });

    const rejectedCount = await UserInvitation.count({
      where: {
        [Op.or]: [
          { sender_id: userId, status: "declined" },
          { receiver_id: userId, status: "declined" },
        ],
      },
    });

    const pendingCount = await UserInvitation.count({
      where: {
        [Op.or]: [
          { sender_id: userId, status: "pending" },
          { receiver_id: userId, status: "pending" },
        ],
      },
    });

    return {
      sent: sentCount,
      received: receivedCount,
      accepted: acceptedCount,
      rejected: rejectedCount,
      pending: pendingCount,
    };
  };

  static getAdminDashboardData = async () => {
    try {
      const totalUsers = await User.count();
      const totalNewUsers = await User.count({
        where: { status: "new" },
      });
      const totalPendingUsers = await User.count({
        where: { status: "pending" },
      });
      const totalApprovedUsers = await User.count({
        where: { status: "approved" },
      });
      const totalRejectedUsers = await User.count({
        where: { status: "rejected" },
      });
      const totalBlockedUsers = await User.count({
        where: { status: "blocked" },
      });
      const totalDeactivatedUsers = await User.count({
        where: { status: "deactivated" },
      });
     
      const totalActiveUserSubscription = await UserSubscription.count({
        where: { is_active: true },
      });
      const totalInquiries = await Inquiry.count();
      const totalSuccessStories = await SuccessStory.count();
      return {
        totalUsers,
        totalNewUsers,
        totalPendingUsers,
        totalApprovedUsers,
        totalRejectedUsers,
        totalBlockedUsers,
        totalDeactivatedUsers,
        totalInquiries,
        totalSuccessStories,
        totalActiveUserSubscription,
      };
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };
}
