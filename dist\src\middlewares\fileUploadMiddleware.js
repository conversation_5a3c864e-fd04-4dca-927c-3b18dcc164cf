"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileUploadMiddleware = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const response_1 = __importDefault(require("../utils/response"));
const ApiError_1 = __importDefault(require("../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../config/httpMessages"));
const http_status_1 = __importDefault(require("http-status"));
const fileUploadMiddleware = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        if ((_a = req.headers["content-type"]) === null || _a === void 0 ? void 0 : _a.includes("multipart/form-data")) {
            if (req.files) {
                const uploadDir = path_1.default.join(__dirname, "../../uploads");
                if (!fs_1.default.existsSync(uploadDir)) {
                    fs_1.default.mkdirSync(uploadDir, { recursive: true });
                }
                const processFiles = (files, fieldName) => __awaiter(void 0, void 0, void 0, function* () {
                    if (Array.isArray(files)) {
                        for (const file of files) {
                            const savedFileName = yield saveFile(file);
                            if (fieldName) {
                                req.body[fieldName] = req.body[fieldName]
                                    ? [...req.body[fieldName], savedFileName]
                                    : [savedFileName];
                            }
                        }
                    }
                    else if (typeof files === "object" && (files === null || files === void 0 ? void 0 : files.name)) {
                        const savedFileName = yield saveFile(files);
                        if (fieldName) {
                            req.body[fieldName] = savedFileName;
                        }
                    }
                    else {
                        for (const key in files) {
                            yield processFiles(files[key], key); // Pass the field name
                        }
                    }
                });
                const saveFile = (file) => __awaiter(void 0, void 0, void 0, function* () {
                    const fileName = `${Date.now()}_${file.name}`;
                    const filePath = path_1.default.join(uploadDir, fileName);
                    yield file.mv(filePath);
                    console.log(`File saved: ${filePath}`);
                    // const form = new FormData();
                    // form.append("image", uploadedFile.data, uploadedFile.name);
                    // const response = await axios.post("https://ubsoi.com/images", form, {
                    //   headers: {
                    //     ...form.getHeaders(), // Set necessary headers for FormData
                    //   },
                    // });
                    // req.uploadedImageUrl = response.data.imageUrl; // Customize based on actual response
                    // req.uploadedLocalFilePath = localFilePath;
                    return fileName; // Return the saved file name
                });
                yield processFiles(req.files);
                next();
            }
            else {
                next();
            }
        }
        else {
            next();
        }
    }
    catch (error) {
        console.error("Error uploading file:", error);
        return (0, response_1.default)(res, new ApiError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, httpMessages_1.default.DOCUMENT.ERROR));
    }
});
exports.fileUploadMiddleware = fileUploadMiddleware;
