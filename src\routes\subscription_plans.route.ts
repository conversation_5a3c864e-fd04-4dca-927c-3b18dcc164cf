import express from "express";
import { auth } from "../middlewares/auth";
import SubscriptionPlanController from "../app/subscription/subscription_plan.controller";
import { validate } from "../middlewares/middleware";
import { subscriptionPlanValidation } from "../validations/subscription_plan.validation";

const router = express.Router();

router.get("", auth, SubscriptionPlanController.getAll);
router.post("", auth, validate(subscriptionPlanValidation), SubscriptionPlanController.create);
router.put("/:id", auth, validate(subscriptionPlanValidation), SubscriptionPlanController.update);
router.get("/:id", auth, SubscriptionPlanController.showById);
router.delete("/:id", auth, SubscriptionPlanController.delete);

export default router;
