"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runAutoDeleteMessages = void 0;
const message_service_1 = __importDefault(require("../app/message/message.service"));
/**
 * Function to delete old messages based on chat auto-delete settings
 * This should be called by a cron job or scheduled task
 */
const runAutoDeleteMessages = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const deletedCount = yield message_service_1.default.deleteOldMessages(1); // Pass 1 as a placeholder, actual days are taken from chat settings
        console.log(`Auto-deleted ${deletedCount} old messages`);
        return deletedCount;
    }
    catch (error) {
        console.error('Error auto-deleting messages:', error);
        throw error;
    }
});
exports.runAutoDeleteMessages = runAutoDeleteMessages;
