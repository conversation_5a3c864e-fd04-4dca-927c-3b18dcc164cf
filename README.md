# BarBadhu_nodejs

## Requirements

- **Node.js** version 20 or above
- **MySQL** database
- **PM2** for process management

---

# 1. Clone the Git Repository

First, clone the Git repository to your local machine using the following command:

- git clone <repository_url>
- cd <repository_directory>

Replace <repository_url> with the actual repository URL and <repository_directory> with the directory name of your project.

# 2. Set Up the .env File

Copy the .env.example file to create the .env file which contains your environment variables:

- `cp .env.example .env`

# 3. Create MySQL Database and Set Credentials

Create Mysql database and set credentials respectively.

> set up the credentials in your .env file (replace placeholders with actual values):

- DATABASE_NAME=
- DATABASE_USER=
- DATABASE_PASSWORD=
- DATABASE_HOST=
- DATABASE_PORT=
- DATABASE_DIALECT=


# 4. Install Dependencies

Install the necessary dependencies for the project using npm:

- npm install
  This will install the dependencies listed in your package.json file.

# 5. Compile TypeScript (if needed)

If your project uses TypeScript, compile it to JavaScript using the following command:

- npx tsc

This will transpile the TypeScript files into JavaScript and place them in the dist/ directory (or wherever yourtsconfig.json is configured to output).

# 6. Start the Application

Once everything is set up, you can start the application. If you're using PM2 to manage the process (for production environments), use the following command:

- pm2 start dist/index.js --name "BarBadhu" --log-date-format="YYYY-MM-DD HH:mm:ss Z"

This command will start the app with PM2, name the process "epost", and format the logs with the specified date format.

>> Using npm start (For development):
- If you're not using PM2 and just want to run it locally for development:
- npm start

- Keep working 🔥
#