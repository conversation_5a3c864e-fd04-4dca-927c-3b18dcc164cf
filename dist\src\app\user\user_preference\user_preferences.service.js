"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const sequelize_1 = require("sequelize");
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../../config/httpMessages"));
const user_preferences_model_1 = __importDefault(require("../../../database/models/user_preferences.model"));
const user_basic_details_model_1 = __importDefault(require("../../../database/models/user_basic_details.model"));
const country_model_1 = __importDefault(require("../../../database/models/country.model"));
const user_location_details_model_1 = __importDefault(require("../../../database/models/user_location_details.model"));
const city_model_1 = __importDefault(require("../../../database/models/city.model"));
const user_education_career_model_1 = __importDefault(require("../../../database/models/user_education_career.model"));
const user_lifestyle_model_1 = __importDefault(require("../../../database/models/user_lifestyle.model"));
const user_family_details_model_1 = __importDefault(require("../../../database/models/user_family_details.model"));
const user_astro_details_model_1 = __importDefault(require("../../../database/models/user_astro_details.model"));
const user_gallery_model_1 = __importDefault(require("../../../database/models/user_gallery.model"));
const user_model_1 = __importDefault(require("../../../database/models/user.model"));
const user_hobbies_model_1 = __importDefault(require("../../../database/models/user_hobbies.model"));
const user_shortlist_model_1 = __importDefault(require("../../../database/models/user_shortlist.model"));
class UserPreferenceService {
    constructor() { }
}
_a = UserPreferenceService;
/**
 * Create a UserPreference
 * @param {Object} body
 * @returns {Promise<UserPreference>}
 */
UserPreferenceService.createUserPreference = (body) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const details = yield user_preferences_model_1.default.create(body);
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Return UserPreferences
 * @param {Object} options
 * @param {number} [options.page] - Current page number (optional)
 * @param {number} [options.limit] - Number of items per page (optional)
 * @param {string} [options.search] - Search term for filtering (optional)
 * @returns {Promise<UserPreference[]>}
 */
UserPreferenceService.getUserPreferences = (options) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
    try {
        const { page, limit, search, currentUserId } = options;
        // 1. Get user preferences
        const userPreference = yield user_preferences_model_1.default.findOne({
            where: { user_id: currentUserId }
        });
        if (!userPreference) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "User preferences not found");
        }
        const whereCondition = search
            ? {
                [sequelize_1.Op.or]: [
                    { first_name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                    { last_name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                ],
            }
            : {};
        whereCondition.id = { [sequelize_1.Op.ne]: currentUserId };
        whereCondition.status = 'active';
        const basicWhere = {};
        const locationWhere = {};
        const educationWhere = {};
        const lifestyleWhere = {};
        const familyWhere = {};
        const astroWhere = {};
        const hobbiesWhere = {};
        // Apply basic details filters
        if ((_b = userPreference.religion) === null || _b === void 0 ? void 0 : _b.length) {
            basicWhere.religion = { [sequelize_1.Op.in]: userPreference.religion };
        }
        if ((_c = userPreference.caste) === null || _c === void 0 ? void 0 : _c.length) {
            basicWhere.caste = { [sequelize_1.Op.in]: userPreference.caste };
        }
        if (userPreference.gotra) {
            basicWhere.gotra = userPreference.gotra;
        }
        if (userPreference.marital_status) {
            basicWhere.marital_status = userPreference.marital_status;
        }
        if ((_d = userPreference.country_of_citizenship) === null || _d === void 0 ? void 0 : _d.length) {
            basicWhere.country_of_citizenship = { [sequelize_1.Op.in]: userPreference.country_of_citizenship };
        }
        // Apply location filters
        if ((_e = userPreference.country_living_in) === null || _e === void 0 ? void 0 : _e.length) {
            locationWhere.country_living_in = { [sequelize_1.Op.in]: userPreference.country_living_in };
        }
        if ((_f = userPreference.city) === null || _f === void 0 ? void 0 : _f.length) {
            locationWhere.city = { [sequelize_1.Op.in]: userPreference.city };
        }
        if ((_g = userPreference.residency_status) === null || _g === void 0 ? void 0 : _g.length) {
            locationWhere.residency_status = { [sequelize_1.Op.in]: userPreference.residency_status };
        }
        // Apply education filters
        if ((_h = userPreference.education) === null || _h === void 0 ? void 0 : _h.length) {
            educationWhere.education = { [sequelize_1.Op.in]: userPreference.education };
        }
        if ((_j = userPreference.profession) === null || _j === void 0 ? void 0 : _j.length) {
            educationWhere.profession = { [sequelize_1.Op.in]: userPreference.profession };
        }
        if ((_k = userPreference.employment_status) === null || _k === void 0 ? void 0 : _k.length) {
            educationWhere.employment_status = { [sequelize_1.Op.in]: userPreference.employment_status };
        }
        if ((_l = userPreference.working_for) === null || _l === void 0 ? void 0 : _l.length) {
            educationWhere.working_for = { [sequelize_1.Op.in]: userPreference.working_for };
        }
        // Apply lifestyle filters
        if (userPreference.age_range) {
            lifestyleWhere.age = {};
            if (userPreference.age_range.start) {
                lifestyleWhere.age[sequelize_1.Op.gte] = userPreference.age_range.start;
            }
            if (userPreference.age_range.end) {
                lifestyleWhere.age[sequelize_1.Op.lte] = userPreference.age_range.end;
            }
        }
        if (userPreference.height_range) {
            lifestyleWhere.height_cm = {};
            if (userPreference.height_range.start) {
                lifestyleWhere.height_cm[sequelize_1.Op.gte] = userPreference.height_range.start;
            }
            if (userPreference.height_range.end) {
                lifestyleWhere.height_cm[sequelize_1.Op.lte] = userPreference.height_range.end;
            }
        }
        if ((_m = userPreference.body_type) === null || _m === void 0 ? void 0 : _m.length) {
            lifestyleWhere.body_type = { [sequelize_1.Op.in]: userPreference.body_type };
        }
        if ((_o = userPreference.complexion) === null || _o === void 0 ? void 0 : _o.length) {
            lifestyleWhere.complexion = { [sequelize_1.Op.in]: userPreference.complexion };
        }
        if ((_p = userPreference.diet) === null || _p === void 0 ? void 0 : _p.length) {
            lifestyleWhere.diet = { [sequelize_1.Op.in]: userPreference.diet };
        }
        if (userPreference.smoke) {
            lifestyleWhere.smoking_habit = userPreference.smoke;
        }
        if (userPreference.drink) {
            lifestyleWhere.drinking_habit = userPreference.drink;
        }
        if (userPreference.disability) {
            lifestyleWhere.any_disability = userPreference.disability;
        }
        // Apply astro details filters
        if ((_q = userPreference.countryOfBirth) === null || _q === void 0 ? void 0 : _q.length) {
            astroWhere.countryOfBirth = { [sequelize_1.Op.in]: userPreference.countryOfBirth };
        }
        if ((_r = userPreference.birthCity) === null || _r === void 0 ? void 0 : _r.length) {
            astroWhere.birthCity = { [sequelize_1.Op.in]: userPreference.birthCity };
        }
        // Apply family details filters
        if ((_s = userPreference.family_type) === null || _s === void 0 ? void 0 : _s.length) {
            familyWhere.family_type = { [sequelize_1.Op.in]: userPreference.family_type };
        }
        // 3. Build query options
        const queryOption = {
            where: whereCondition,
            distinct: true,
            include: [
                {
                    model: user_basic_details_model_1.default,
                    as: "basicDetails",
                    where: Object.keys(basicWhere).length ? basicWhere : undefined,
                    attributes: ["religion", "caste", "gotra", "marital_status", "country_of_citizenship"],
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ["id", "name"],
                        },
                    ],
                },
                {
                    model: user_location_details_model_1.default,
                    as: "locationDetails",
                    where: Object.keys(locationWhere).length ? locationWhere : undefined,
                    attributes: ["city", "country_living_in", "residency_status"],
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ["id", "name"],
                        },
                        {
                            model: city_model_1.default,
                            as: "cities",
                            attributes: ["id", "name"],
                        },
                    ],
                },
                {
                    model: user_education_career_model_1.default,
                    as: "educationCareer",
                    where: Object.keys(educationWhere).length ? educationWhere : undefined,
                    attributes: ["education", "profession", "employment_status", "working_for"]
                },
                {
                    model: user_lifestyle_model_1.default,
                    as: "lifestyle",
                    where: Object.keys(lifestyleWhere).length ? lifestyleWhere : undefined,
                    attributes: ["age", "height_cm", "body_type", "complexion", "diet", "smoking_habit", "drinking_habit", "any_disability"]
                },
                {
                    model: user_family_details_model_1.default,
                    as: "familyDetails",
                    where: Object.keys(familyWhere).length ? familyWhere : undefined,
                    attributes: ["family_type", "father_occupation", "mother_occupation"]
                },
                {
                    model: user_astro_details_model_1.default,
                    as: "astroDetails",
                    where: Object.keys(astroWhere).length ? astroWhere : undefined,
                    attributes: ["countryOfBirth", "birthCity"],
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ["id", "name"],
                        },
                        {
                            model: city_model_1.default,
                            as: "cities",
                            attributes: ["id", "name"],
                        },
                    ],
                },
            ],
            order: [["createdAt", "DESC"]],
        };
        // Apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        // 4. Execute query
        const totalItems = yield user_model_1.default.count({
            where: whereCondition,
            include: queryOption.include.map((include) => ({
                model: include.model,
                as: include.as,
                where: include.where
            }))
        });
        const users = yield user_model_1.default.findAll(queryOption);
        // 5. Format and return results
        if (page && limit) {
            return {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                users
            };
        }
        else {
            return users;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get UserPreference by id with enriched reference data
 * @param {Number} id
 * @returns {Promise<UserPreference>}
 */
UserPreferenceService.getUserPreferenceById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user_preference = yield user_preferences_model_1.default.findByPk(id);
        const country_of_citizenship_names = yield (user_preference === null || user_preference === void 0 ? void 0 : user_preference.country_of_citizenship_names);
        const country_living_in_names = yield (user_preference === null || user_preference === void 0 ? void 0 : user_preference.country_living_in_names);
        const city_names = yield (user_preference === null || user_preference === void 0 ? void 0 : user_preference.city_names);
        const countryOfBirth_names = yield (user_preference === null || user_preference === void 0 ? void 0 : user_preference.countryOfBirth_names);
        const birthCity_names = yield (user_preference === null || user_preference === void 0 ? void 0 : user_preference.birthCity_names);
        return Object.assign(Object.assign({}, user_preference === null || user_preference === void 0 ? void 0 : user_preference.toJSON()), { country_of_citizenship_names, country_living_in_names, city_names, countryOfBirth_names, birthCity_names });
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update user preference by id
 * @param {Number} Id
 * @param {Object} updateBody
 * @returns {Promise<UserPreference>}
 */
UserPreferenceService.updateUserPreferenceById = (Id, updateBody) => __awaiter(void 0, void 0, void 0, function* () {
    const details = yield user_preferences_model_1.default.findByPk(Id);
    if (!details) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USERPRE.NOT_FOUND);
    }
    Object.assign(details, updateBody);
    yield details.save();
    return details;
});
/**
 * Delete role by id
 * @param {Number} Id
 * @returns {Promise<Role>}
 */
UserPreferenceService.deleteUserPreferenceById = (Id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const details = yield user_preferences_model_1.default.findByPk(Id);
        if (!details) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USERPRE.NOT_FOUND);
        }
        yield details.destroy();
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting Role.");
    }
});
/**
 * Get matching users based on user preferences with flexible matching
 * @param {Number} userId - The ID of the user whose preferences to use
 * @param {Object} options - Pagination and search options
 * @returns {Promise<Object>} - Matching users with pagination info
 */
UserPreferenceService.getMatchingUsersByPreference = (userId, options) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
    try {
        // 1. Get user preferences
        const userPreference = yield user_preferences_model_1.default.findOne({
            where: { user_id: userId }
        });
        if (!userPreference) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "User preferences not found");
        }
        // 2. Build filter conditions based on preferences
        const { page, limit, search } = options;
        const whereCondition = search
            ? {
                [sequelize_1.Op.or]: [
                    { first_name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                    { last_name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                ],
            }
            : {};
        // Exclude the current user
        whereCondition.id = { [sequelize_1.Op.ne]: userId };
        whereCondition.status = 'active';
        whereCondition.is_hide_profile = false;
        if (userId) {
            const currentUser = yield user_model_1.default.findOne({
                where: { id: userId },
                attributes: ['gender']
            });
            if ((currentUser === null || currentUser === void 0 ? void 0 : currentUser.gender) === 'male') {
                whereCondition.gender = 'female';
            }
            else {
                whereCondition.gender = 'male';
            }
        }
        // Create a main OR condition for flexible matching
        const orConditions = [];
        // Add basic details conditions
        if ((_b = userPreference.religion) === null || _b === void 0 ? void 0 : _b.length) {
            orConditions.push({
                '$basicDetails.religion$': { [sequelize_1.Op.in]: userPreference.religion }
            });
        }
        if ((_c = userPreference.caste) === null || _c === void 0 ? void 0 : _c.length) {
            orConditions.push({
                '$basicDetails.caste$': { [sequelize_1.Op.in]: userPreference.caste }
            });
        }
        if (userPreference.gotra) {
            orConditions.push({
                '$basicDetails.gotra$': userPreference.gotra
            });
        }
        if (userPreference.marital_status) {
            orConditions.push({
                '$basicDetails.marital_status$': userPreference.marital_status
            });
        }
        if ((_d = userPreference.country_of_citizenship) === null || _d === void 0 ? void 0 : _d.length) {
            orConditions.push({
                '$basicDetails.country_of_citizenship$': { [sequelize_1.Op.in]: userPreference.country_of_citizenship }
            });
        }
        // Add location conditions
        if ((_e = userPreference.country_living_in) === null || _e === void 0 ? void 0 : _e.length) {
            orConditions.push({
                '$locationDetails.country_living_in$': { [sequelize_1.Op.in]: userPreference.country_living_in }
            });
        }
        if ((_f = userPreference.city) === null || _f === void 0 ? void 0 : _f.length) {
            orConditions.push({
                '$locationDetails.city$': { [sequelize_1.Op.in]: userPreference.city }
            });
        }
        if ((_g = userPreference.residency_status) === null || _g === void 0 ? void 0 : _g.length) {
            orConditions.push({
                '$locationDetails.residency_status$': { [sequelize_1.Op.in]: userPreference.residency_status }
            });
        }
        // Add education conditions
        if ((_h = userPreference.education) === null || _h === void 0 ? void 0 : _h.length) {
            orConditions.push({
                '$educationCareer.education$': { [sequelize_1.Op.in]: userPreference.education }
            });
        }
        if ((_j = userPreference.profession) === null || _j === void 0 ? void 0 : _j.length) {
            orConditions.push({
                '$educationCareer.profession$': { [sequelize_1.Op.in]: userPreference.profession }
            });
        }
        if ((_k = userPreference.employment_status) === null || _k === void 0 ? void 0 : _k.length) {
            orConditions.push({
                '$educationCareer.employment_status$': { [sequelize_1.Op.in]: userPreference.employment_status }
            });
        }
        if ((_l = userPreference.working_for) === null || _l === void 0 ? void 0 : _l.length) {
            orConditions.push({
                '$educationCareer.working_for$': { [sequelize_1.Op.in]: userPreference.working_for }
            });
        }
        // Add lifestyle conditions
        if (userPreference.age_range) {
            if (userPreference.age_range.start) {
                orConditions.push({
                    '$lifestyle.age$': { [sequelize_1.Op.gte]: userPreference.age_range.start }
                });
            }
            if (userPreference.age_range.end) {
                orConditions.push({
                    '$lifestyle.age$': { [sequelize_1.Op.lte]: userPreference.age_range.end }
                });
            }
        }
        if (userPreference.height_range) {
            if (userPreference.height_range.start) {
                orConditions.push({
                    '$lifestyle.height_cm$': { [sequelize_1.Op.gte]: userPreference.height_range.start }
                });
            }
            if (userPreference.height_range.end) {
                orConditions.push({
                    '$lifestyle.height_cm$': { [sequelize_1.Op.lte]: userPreference.height_range.end }
                });
            }
        }
        if ((_m = userPreference.body_type) === null || _m === void 0 ? void 0 : _m.length) {
            orConditions.push({
                '$lifestyle.body_type$': { [sequelize_1.Op.in]: userPreference.body_type }
            });
        }
        if ((_o = userPreference.complexion) === null || _o === void 0 ? void 0 : _o.length) {
            orConditions.push({
                '$lifestyle.complexion$': { [sequelize_1.Op.in]: userPreference.complexion }
            });
        }
        if ((_p = userPreference.diet) === null || _p === void 0 ? void 0 : _p.length) {
            orConditions.push({
                '$lifestyle.diet$': { [sequelize_1.Op.in]: userPreference.diet }
            });
        }
        if (userPreference.smoke) {
            orConditions.push({
                '$lifestyle.smoking_habit$': userPreference.smoke
            });
        }
        if (userPreference.drink) {
            orConditions.push({
                '$lifestyle.drinking_habit$': userPreference.drink
            });
        }
        if (userPreference.disability) {
            orConditions.push({
                '$lifestyle.any_disability$': userPreference.disability
            });
        }
        // Add astro details conditions
        if ((_q = userPreference.countryOfBirth) === null || _q === void 0 ? void 0 : _q.length) {
            orConditions.push({
                '$astroDetails.countryOfBirth$': { [sequelize_1.Op.in]: userPreference.countryOfBirth }
            });
        }
        if ((_r = userPreference.birthCity) === null || _r === void 0 ? void 0 : _r.length) {
            orConditions.push({
                '$astroDetails.birthCity$': { [sequelize_1.Op.in]: userPreference.birthCity }
            });
        }
        // Add family details conditions
        if ((_s = userPreference.family_type) === null || _s === void 0 ? void 0 : _s.length) {
            orConditions.push({
                '$familyDetails.family_type$': { [sequelize_1.Op.in]: userPreference.family_type }
            });
        }
        // Add the OR conditions to the main where clause if there are any
        if (orConditions.length > 0) {
            whereCondition[sequelize_1.Op.or] = orConditions;
        }
        // 3. Build query options
        const queryOption = {
            where: whereCondition,
            distinct: true,
            include: [
                {
                    model: user_basic_details_model_1.default,
                    as: "basicDetails",
                    required: false,
                    attributes: ["religion", "caste", "gotra", "marital_status", "country_of_citizenship"],
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ["id", "name"],
                        },
                    ],
                },
                {
                    model: user_location_details_model_1.default,
                    as: "locationDetails",
                    required: false,
                    attributes: ["city", "country_living_in", "residency_status"],
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ["id", "name"],
                        },
                        {
                            model: city_model_1.default,
                            as: "cities",
                            attributes: ["id", "name"],
                        },
                    ],
                },
                {
                    model: user_education_career_model_1.default,
                    as: "educationCareer",
                    required: false,
                    attributes: ["education", "profession", "employment_status", "working_for"]
                },
                {
                    model: user_lifestyle_model_1.default,
                    as: "lifestyle",
                    required: false,
                    attributes: ["age", "height_cm", "body_type", "complexion", "diet", "smoking_habit", "drinking_habit", "any_disability"]
                },
                {
                    model: user_family_details_model_1.default,
                    as: "familyDetails",
                    required: false,
                    attributes: ["family_type", "father_occupation", "mother_occupation"]
                },
                {
                    model: user_astro_details_model_1.default,
                    as: "astroDetails",
                    required: false,
                    attributes: ["countryOfBirth", "birthCity"],
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ["id", "name"],
                        },
                        {
                            model: city_model_1.default,
                            as: "cities",
                            attributes: ["id", "name"],
                        },
                    ],
                },
                {
                    model: user_hobbies_model_1.default,
                    as: "hobbies",
                    attributes: ["hobbies", "interests"]
                },
                {
                    model: user_gallery_model_1.default,
                    as: "userGallery",
                },
                {
                    model: user_shortlist_model_1.default,
                    as: "shortlisted_user"
                }
            ],
            order: [["createdAt", "DESC"]],
        };
        // Apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        // 4. Execute query
        const totalItems = yield user_model_1.default.count({
            where: whereCondition,
            include: queryOption.include.map((include) => ({
                model: include.model,
                as: include.as,
                required: false
            })),
            distinct: true
        });
        const users = yield user_model_1.default.findAll(queryOption);
        // 5. Format and return results
        if (page && limit) {
            return {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                users
            };
        }
        else {
            return users;
        }
    }
    catch (error) {
        throw new ApiError_1.default(error.statusCode || http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = UserPreferenceService;
