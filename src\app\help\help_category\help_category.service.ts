 import httpStatus from "http-status";
import { Op } from "sequelize";
import ApiError from "../../../utils/ApiError";
import httpMessages from "../../../config/httpMessages";
import HelpCategory from "../../../database/models/help_category.model";
import HelpQuestion from "../../../database/models/help_question.model";

export default class HelpCategoryService {
  constructor() { }

  /**
   * Create a HelpCategory
   * @param {Object} body
   * @returns {Promise<HelpCategory>}
   */
  static createHelpCategory = async (body: any) => {
    try {
      if (await this.getHelpCategoryByTitle(body.title)) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.HELP.NAME_ALREADY_TAKEN
        );
      }

      const helpCategory: HelpCategory = await HelpCategory.create(body);

      return await this.getHelpCategoryById(helpCategory.id);
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get HelpCategory by title
   * @param {String} title
   * @returns {Promise<HelpCategory>}
   */
  static getHelpCategoryByTitle = async (title: string) => {
    try {
      return HelpCategory.findOne({
        where: { title },
      }).then((data: any) => data?.toJSON());
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get all HelpCategories
   * @param {Object} option
   * @returns {Promise<HelpCategory[]>}
   */
  static getHelpCategories = async (option: any) => {
    try {
      const { page, limit, search } = option;
      const whereCondition: any = {};      
      if (search) {
        whereCondition.title = { [Op.like]: `%${search.toLowerCase()}%` };
      }
      const queryOption: any = {
        where: whereCondition,
        order: [["createdAt", "DESC"]],
      };
      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }
      const helpCategories = await HelpCategory.findAndCountAll(queryOption);
      if (page && limit) {
        return {
          totalItems: helpCategories.count,
          totalPages: Math.ceil(helpCategories.count / limit),
          currentPage: page,
          helpCategories: helpCategories.rows,
        };
      } else {
        return helpCategories.rows;
      }
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get HelpCategory by id
   * @param {Number} id
   * @returns {Promise<HelpCategory>}
   */
  static getHelpCategoryById = async (id: number) => {
    try {
      return HelpCategory.findOne({
        where: { id },
        include: [
          {
            model: HelpQuestion,
            as: "questions",
            attributes: ["question", "description"],
          },
        ],
      }).then((data: any) => data?.toJSON());
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Update role by id
   * @param {Number} Id
   * @param {Object} updateBody
   * @returns {Promise<Role>}
   */
  static updateHelpCategoryById = async (Id: number, updateBody: any) => {
    const details = await HelpCategory.findByPk(Id);
    if (!details) {
      throw new ApiError(httpStatus.NOT_FOUND, httpMessages.HELP.NOT_FOUND);
    }

    Object.assign(details, updateBody);
    await details.save();
    return details;
  };

  /**
   * Delete role by id
   * @param {Number} Id
   * @returns {Promise<Role>}
   */
  static deleteHelpCategoryById = async (Id: number) => {
    try {
      const details: any = await HelpCategory.findByPk(Id);
      if (!details) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.HELP.NOT_FOUND);
      }
      await details.destroy();
      return details;
    } catch (error: any) {
      throw new ApiError(
        error.status || httpStatus.BAD_REQUEST,
        error.message || "Error deleting Role."
      );
    }
  };
}








