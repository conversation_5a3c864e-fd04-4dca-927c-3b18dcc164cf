import httpStatus from "http-status";
import { Op } from "sequelize";
import SubscriptionPlan from "../../database/models/subscription_plans.mode";
import ApiError from "../../utils/ApiError";
import httpMessages from "../../config/httpMessages";

export default class SubscriptionPlanService {
    constructor() { }

    /**
     * Create a SubscriptionPlan
     * @param {Object} body
     * @returns {Promise<SubscriptionPlan>}
     */
    static createSubscriptionPlan = async (body: any) => {
        try {
            const details: SubscriptionPlan = await SubscriptionPlan.create(body);
            return details;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Return SubscriptionPlans
     * @param {Object} options
     * @param {number} [options.page] - Current page number (optional)
     * @param {number} [options.limit] - Number of items per page (optional)
     * @param {string} [options.search] - Search term for filtering (optional)
     * @returns {Promise<SubscriptionPlan[]>}
     */
    static getSubscriptionPlans = async (options: {
        page?: number;
        limit?: number;
        search?: string;
    }) => {
        try {
            const { page, limit, search } = options;
            const whereCondition = search
                ? {
                    [Op.or]: [
                        { name: { [Op.like]: `%${search.toLowerCase()}%` } },
                    ],
                }
                : {};

            const queryOption: any = {
                where: whereCondition,
                order: [["createdAt", "DESC"]],
            };
            // If pagination is provided, apply pagination
            if (page && limit) {
                const offset = (page - 1) * limit;
                queryOption.limit = limit;
                queryOption.offset = offset;
            }
            const data = await SubscriptionPlan.findAndCountAll(queryOption);
            if (page && limit) {
                return {
                    totalItems: data.count,
                    totalPages: Math.ceil(data.count / limit),
                    currentPage: page,
                    subscription_plans: data.rows,
                };
            } else {
                return data.rows;
            }
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Get SubscriptionPlan by id
     * @param {Number} id
     * @returns {Promise<SubscriptionPlan>}
     */
    static getSubscriptionPlanById = async (id: number) => {
        try {
            return SubscriptionPlan.findOne({
                where: { id },
            }).then((data: any) => data?.toJSON());
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Update role by id
     * @param {Number} Id
     * @param {Object} updateBody
     * @returns {Promise<Role>}
     */
    static updateSubscriptionPlanById = async (Id: number, updateBody: any) => {
        const details = await SubscriptionPlan.findByPk(Id);
        if (!details) {
            throw new ApiError(httpStatus.NOT_FOUND, httpMessages.SUBSCRIPTION_PLAN.NOT_FOUND);
        }

        Object.assign(details, updateBody);
        await details.save();
        return details;
    };

    /**
     * Delete role by id
     * @param {Number} Id
     * @returns {Promise<Role>}
     */
    static deleteSubscriptionPlanById = async (Id: number) => {
        try {
            const details: any = await SubscriptionPlan.findByPk(Id);
            if (!details) {
                throw new ApiError(httpStatus.NOT_FOUND, httpMessages.ROLES.NOT_FOUND);
            }
            await details.destroy();
            return details;
        } catch (error: any) {
            throw new ApiError(
                error.status || httpStatus.BAD_REQUEST,
                error.message || "Error deleting Role."
            );
        }
    };
}
