/**
 * @swagger
 * /geolocation:
 *   get:
 *     tags:
 *       - Geolocation
 *     summary: Get all countries with states and cities
 *     description: Retrieve a list of countries including their states and corresponding cities.
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           example: 1
 *         description: Page number for pagination.
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Number of records per page.
 *     responses:
 *       200:
 *         description: Successfully retrieved the country list.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Get countries successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                       example: 1
 *                     totalPages:
 *                       type: integer
 *                       example: 1
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     countries:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 157
 *                           name:
 *                             type: string
 *                             example: Nepal
 *                           phoneCode:
 *                             type: string
 *                             example: "977"
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                             example: 2025-05-06T06:18:06.000Z
 *                           updatedAt:
 *                             type: string
 *                             format: date-time
 *                             example: 2025-05-06T06:18:06.000Z
 *                           states:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: integer
 *                                   example: 2872
 *                                 name:
 *                                   type: string
 *                                   example: Central Region
 *                                 stateCode:
 *                                   type: string
 *                                   example: "1"
 *                                 isServiceAvailable:
 *                                   type: boolean
 *                                   example: false
 *                                 country_id:
 *                                   type: integer
 *                                   example: 157
 *                                 cities:
 *                                   type: array
 *                                   items:
 *                                     type: object
 *                                     properties:
 *                                       id:
 *                                         type: integer
 *                                         example: 76014
 *                                       name:
 *                                         type: string
 *                                         example: Banepā
 *                                       state_id:
 *                                         type: integer
 *                                         example: 2872
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Unable to retrieve geolocation data due to a server error.
 */

/**
 * @swagger
 * /geolocation:
 *   post:
 *     tags:
 *       - Geolocation
 *     summary: Add a new country with states and cities
 *     description: Create a new country along with its states and nested cities.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object 
 *             required:
 *               - name
 *               - phoneCode
 *             properties:
 *               name:
 *                 type: string
 *                 example: Nepal
 *               phoneCode:
 *                 type: string
 *                 example: "977"
 *               states:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       example: Central Region
 *                     stateCode:
 *                       type: string
 *                       example: "1"
 *                     isServiceAvailable:
 *                       type: boolean
 *                       example: false
 *                     cities:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                             example: Banepā
 *     responses:
 *       201:
 *         description: Country created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Country created successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 157
 *                     name:
 *                       type: string
 *                       example: Nepal
 *                     phoneCode:
 *                       type: string
 *                       example: "977"
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-06T06:18:06.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-06T06:18:06.000Z
 *                     states:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 2872
 *                           name:
 *                             type: string
 *                             example: Central Region
 *                           stateCode:
 *                             type: string
 *                             example: "1"
 *                           isServiceAvailable:
 *                             type: boolean
 *                             example: false
 *                           country_id:
 *                             type: integer
 *                             example: 157
 *                           cities:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: integer
 *                                   example: 76014
 *                                 name:
 *                                   type: string
 *                                   example: Banepā
 *                                 state_id:
 *                                   type: integer
 *                                   example: 2872
 *       400:
 *         description: Invalid input data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid request payload.
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Failed to create country due to a server error.
 */

/**
 * @swagger
 * /geolocation/{id}:
 *   get:
 *     tags:
 *       - Geolocation
 *     summary: Get country details by ID
 *     description: Retrieve a country's details, including its states and nested cities, by the country's ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the country to retrieve
 *     responses:
 *       200:
 *         description: Country retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Country retrieved successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 157
 *                     name:
 *                       type: string
 *                       example: Nepal
 *                     phoneCode:
 *                       type: string
 *                       example: "977"
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-06T06:18:06.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-06T06:18:06.000Z
 *                     states:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 2872
 *                           name:
 *                             type: string
 *                             example: Central Region
 *                           stateCode:
 *                             type: string
 *                             example: "1"
 *                           isServiceAvailable:
 *                             type: boolean
 *                             example: false
 *                           country_id:
 *                             type: integer
 *                             example: 157
 *                           cities:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: integer
 *                                   example: 76014
 *                                 name:
 *                                   type: string
 *                                   example: Banepā
 *                                 state_id:
 *                                   type: integer
 *                                   example: 2872
 *       404:
 *         description: Country not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Country not found.
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Failed to retrieve country due to a server error.
 */

/**
 * @swagger
 * /geolocation/{id}:
 *   put:
 *     tags:
 *       - Geolocation
 *     summary: Update country details by ID
 *     description: Update a country's information, including its states and nested cities, by the country's ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the country to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: Nepal
 *               phoneCode:
 *                 type: string
 *                 example: "977"
 *               states:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 2872
 *                     name:
 *                       type: string
 *                       example: Central Region
 *                     stateCode:
 *                       type: string
 *                       example: "1"
 *                     isServiceAvailable:
 *                       type: boolean
 *                       example: true
 *                     cities:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 76014
 *                           name:
 *                             type: string
 *                             example: Banepā
 *     responses:
 *       200:
 *         description: Country updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Country updated successfully.
 *                 data:
 *                   type: object
 *                   $ref: '#/components/schemas/Country'
 *       400:
 *         description: Invalid request payload.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid input.
 *       404:
 *         description: Country not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Country not found.
 *       500:
 *         description: Server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Failed to update country due to a server error.
 */

/**
 * @swagger
 * /geolocation/{id}:
 *   delete:
 *     tags:
 *       - Geolocation
 *     summary: Delete country by ID
 *     description: Permanently delete a country along with its associated states and cities using the country ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the country to delete
 *     responses:
 *       200:
 *         description: Country deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Country deleted successfully.
 *       404:
 *         description: Country not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Country not found.
 *       500:
 *         description: Server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Failed to delete country due to a server error.
 */

/**
 * @swagger
 * /geolocation/countries:
 *   get:
 *     tags:
 *       - Geolocation
 *     summary: Get all countries
 *     description: Returns a list of all countries with ID, name, phone code, and timestamps.
 *     responses:
 *       200:
 *         description: Get countries successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Get countries successfully.
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: Afghanistan
 *                       phoneCode:
 *                         type: string
 *                         example: "93"
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-05-06T06:17:53.000Z"
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-05-06T06:17:53.000Z"
 *       500:
 *         description: Internal server error while fetching countries.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Internal server error.
 */

/**
 * @swagger
 * /geolocation/states/{countryId}:
 *   get:
 *     tags:
 *       - Geolocation
 *     summary: Get all states by country ID
 *     description: Returns a list of states that belong to the specified country.
 *     parameters:
 *       - in: path
 *         name: countryId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the country
 *         example: 1
 *     responses:
 *       200:
 *         description: List of states retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: States fetched successfully.
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: Maharashtra
 *                       stateCode:
 *                         type: string
 *                         example: MH
 *                       country_id:
 *                         type: integer
 *                         example: 1
 *                       isServiceAvailable:
 *                         type: boolean
 *                         example: true
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-05-06T06:17:53.000Z"
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-05-06T06:17:53.000Z"
 *       404:
 *         description: No states found for the given country ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: States not found.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Internal server error.
 */


/**
 * @swagger
 * /geolocation/cities/{stateId}:
 *   get:
 *     tags:
 *       - Geolocation
 *     summary: Get all cities by state ID
 *     description: Returns a list of cities that belong to the specified state.
 *     parameters:
 *       - in: path
 *         name: stateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the state
 *         example: 10
 *     responses:
 *       200:
 *         description: List of cities retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Cities fetched successfully.
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 101
 *                       name:
 *                         type: string
 *                         example: Mumbai
 *                       state_id:
 *                         type: integer
 *                         example: 10
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-05-06T06:17:53.000Z"
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-05-06T06:17:53.000Z"
 *       404:
 *         description: No cities found for the given state ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Cities not found.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Internal server error.
 */
