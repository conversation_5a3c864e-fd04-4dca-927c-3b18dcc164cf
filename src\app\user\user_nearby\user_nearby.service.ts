import httpStatus from "http-status";
import bcrypt from "bcrypt";
import { Op } from "sequelize";
import { Sequelize } from "sequelize-typescript";
import Role from "../../../database/models/role.model";
import ApiError from "../../../utils/ApiError";
import httpMessages from "../../../config/httpMessages";
import UserShortlist from "../../../database/models/user_shortlist.model";
import UserBasicDetails from "../../../database/models/user_basic_details.model";
import UserLocationDetails from "../../../database/models/user_location_details.model";
import UserEducationCareer from "../../../database/models/user_education_career.model";
import UserLifestyle from "../../../database/models/user_lifestyle.model";
import UserFamilyDetails from "../../../database/models/user_family_details.model";
import UserHobbies from "../../../database/models/user_hobbies.model";
import UserGallery from "../../../database/models/user_gallery.model";
import User from "../../../database/models/user.model";
import Country from "../../../database/models/country.model";
import City from "../../../database/models/city.model";

export default class UserNearbyService {
    constructor() { }

    /**
   * Return Users
   * @param {Object} options
   * @param {number} [options.page] - Current page number (optional)
   * @param {number} [options.limit] - Number of items per page (optional)
   * @param {string} [options.search] - Search term for filtering (optional)
   * @returns {Promise<Role[]>}
   */
    static getUsersByLocation = async (options: {
        page?: number;
        limit?: number;
        search?: string;
    }, userId?: number) => {
        try {
            const { page, limit, search } = options;
            const whereCondition: any = {};

            const currentUser = await User.findOne({
                where: { id: userId },
                attributes: ["gender"],
                include: [
                    {
                        model: UserLocationDetails,
                        as: "locationDetails",
                        attributes: ["city", "country_living_in"]
                    },
                ],
            });
            if (!currentUser) {
                throw new ApiError(httpStatus.BAD_REQUEST, httpMessages.USER.NOT_FOUND);
            }
            whereCondition.id = { [Op.ne]: userId };
            if (currentUser?.gender === 'male') {
                whereCondition.gender = 'female';
            } else {
                whereCondition.gender = 'male';
            }
            whereCondition.status = 'active';
            whereCondition.is_hide_profile = false;
            const locationWhere: any = {};

            if (currentUser?.locationDetails?.city) {
                locationWhere.city = currentUser?.locationDetails?.city;
            }

            const queryOption: any = {
                where: whereCondition,
                distinct: true,
                include: [
                    {
                        model: UserBasicDetails,
                        as: "basicDetails",
                        attributes: ["religion", "caste", "gotra", 'marital_status']
                    },
                    {
                        model: UserLocationDetails,
                        as: "locationDetails",
                        where: Object.keys(locationWhere).length ? locationWhere : undefined,
                        attributes: ["city", "country_living_in"],
                        include: [
                            {
                                model: Country,
                                as: "country",
                                attributes: ['name'],
                            },
                            {
                                model: City,
                                as: "cities",
                                attributes: ['name'],
                            },
                        ],

                    },
                    {
                        model: UserEducationCareer,
                        as: "educationCareer",
                        attributes: ["education", "profession"]
                    },
                    {
                        model: UserLifestyle,
                        as: "lifestyle",
                        attributes: ["age", "height_cm"]
                    },
                    {
                        model: UserFamilyDetails,
                        as: "familyDetails",
                        attributes: ["family_type", "father_occupation", "mother_occupation"]
                    },
                    {
                        model: UserHobbies,
                        as: "hobbies",
                        attributes: ["hobbies", "interests"]
                    },
                    {
                        model: UserGallery,
                        as: "userGallery",
                    },
                    {
                        model: UserShortlist,
                        as: "shortlisted_user"
                    }
                ],
                order: [["createdAt", "DESC"]],
            };
            // If pagination is provided, apply pagination
            if (page && limit) {
                const offset = (page - 1) * limit;
                queryOption.limit = limit;
                queryOption.offset = offset;
            }
            const shortlists = await User.findAndCountAll(queryOption);
            if (page && limit) {
                return {
                    totalItems: shortlists.count,
                    totalPages: Math.ceil(shortlists.count / limit),
                    currentPage: page,
                    shortlists: shortlists.rows,
                };
            } else {
                return shortlists.rows;
            }
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

}
