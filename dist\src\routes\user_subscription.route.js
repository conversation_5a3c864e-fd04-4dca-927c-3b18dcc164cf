"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middlewares/auth");
const user_subscription_controller_1 = __importDefault(require("../app/subscription/user_subscription/user_subscription.controller"));
const middleware_1 = require("../middlewares/middleware");
const user_subscription_validation_1 = require("../validations/user_subscription.validation");
const router = express_1.default.Router();
router.put("/:id/status", auth_1.auth, user_subscription_controller_1.default.updateStatus);
router.get("", auth_1.auth, user_subscription_controller_1.default.getAll);
router.post("", auth_1.auth, (0, middleware_1.validate)(user_subscription_validation_1.userSubscriptionValidation), user_subscription_controller_1.default.create);
router.put("/:id", auth_1.auth, (0, middleware_1.validate)(user_subscription_validation_1.userSubscriptionValidation), user_subscription_controller_1.default.update);
router.get("/:id", auth_1.auth, user_subscription_controller_1.default.showById);
router.delete("/:id", auth_1.auth, user_subscription_controller_1.default.delete);
exports.default = router;
