
"use strict";
const bcrypt = require("bcrypt");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const roles = await queryInterface.bulkInsert(
      "roles",
      [
        {
          role_name: "Super_Admin",
          description:
            "Has full access to all system features and functionalities",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Admin",
          description:
            "Has access to most system features with some restrictions",
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      ],
      { returning: true }
    );

    const admins = await queryInterface.bulkInsert(
      "admins",
      [
        {
          first_name: "<PERSON>",
          last_name: "Ad<PERSON>",
          email: "<EMAIL>",
          mobile_number: "1234567890",
          username: "admin",
          password: await bcrypt.hash("Admin@123", 10),
          role_id: roles,
          is_super_admin: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      { returning: true, validate: true, individualHooks: true }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("roles", null, {});
    await queryInterface.bulkDelete("admins", null, {});
  },
};