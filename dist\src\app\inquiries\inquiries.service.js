"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const sequelize_1 = require("sequelize");
const inquiries_model_1 = __importDefault(require("../../database/models/inquiries.model"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
class InquiryService {
    constructor() { }
}
_a = InquiryService;
/**
 * Create a Inquiry
 * @param {Object} body
 * @returns {Promise<Inquiry>}
 */
InquiryService.createInquiry = (body) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const inquiry = yield inquiries_model_1.default.create(body);
        return inquiry;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get all inquiries with pagination and search
 * @param {Object} options - Query options
 * @returns {Promise<Object>}
 */
InquiryService.getInquiries = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, search = "", status } = options;
        const offset = (page - 1) * limit;
        const whereClause = {};
        if (status) {
            whereClause.status = status;
        }
        if (search) {
            whereClause[sequelize_1.Op.or] = [
                { name: { [sequelize_1.Op.like]: `%${search}%` } },
                { email: { [sequelize_1.Op.like]: `%${search}%` } },
                { subject: { [sequelize_1.Op.like]: `%${search}%` } },
                { message: { [sequelize_1.Op.like]: `%${search}%` } },
            ];
        }
        const { count, rows } = yield inquiries_model_1.default.findAndCountAll({
            where: whereClause,
            limit,
            offset,
            order: [["createdAt", "DESC"]],
        });
        if (page && limit) {
            return {
                totalItems: count,
                totalPages: Math.ceil(count / limit),
                currentPage: page,
                inquiries: rows,
            };
        }
        else {
            return rows;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get inquiry by id
 * @param {Number} id
 * @returns {Promise<Inquiry>}
 */
InquiryService.getInquiryById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return inquiries_model_1.default.findOne({
            where: { id },
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update inquiry status
 * @param {Number} id
 * @param {String} status
 * @param {String} admin_notes
 * @returns {Promise<Inquiry>}
 */
InquiryService.updateInquiryStatus = (id, status, admin_notes, changed_by_admin_id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const inquiry = yield inquiries_model_1.default.findByPk(id);
        if (!inquiry) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Inquiry not found");
        }
        if (!['pending', 'in_progress', 'resolved', 'closed'].includes(status)) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Invalid status");
        }
        inquiry.status = status;
        inquiry.admin_notes = admin_notes;
        inquiry.changed_by_admin_id = changed_by_admin_id;
        inquiry.changed_at = new Date();
        yield inquiry.save();
        return inquiry;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Delete inquiry
 * @param {Number} id
 * @returns {Promise<Inquiry>}
 */
InquiryService.deleteInquiry = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const inquiry = yield inquiries_model_1.default.findByPk(id);
        if (!inquiry) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Inquiry not found");
        }
        yield inquiry.destroy();
        return inquiry;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = InquiryService;
