import Jo<PERSON> from "joi";

export const successStoryValidation = {
  body: Joi.object().keys({
    bride_name: Joi.string().required().max(100).messages({
      "string.base": "Bride's name must be a string.",
      "string.empty": "Bride's name is required.",
      "string.max": "Bride's name cannot exceed 100 characters.",
      "any.required": "Bride's name is required.",
    }),

    bride_email: Joi.string().email().required().max(100).messages({
      "string.base": "Bride's email must be a string.",
      "string.empty": "Bride's email is required.",
      "string.email": "Bride's email must be a valid email address.",
      "string.max": "Bride's email cannot exceed 100 characters.",
      "any.required": "Bride's email is required.",
    }),

    groom_name: Joi.string().required().max(100).messages({
      "string.base": "<PERSON><PERSON>'s name must be a string.",
      "string.empty": "<PERSON><PERSON>'s name is required.",
      "string.max": "<PERSON><PERSON>'s name cannot exceed 100 characters.",
      "any.required": "<PERSON><PERSON>'s name is required.",
    }),

    groom_email: Joi.string().email().required().max(100).messages({
      "string.base": "Groom's email must be a string.",
      "string.empty": "Groom's email is required.",
      "string.email": "Groom's email must be a valid email address.",
      "string.max": "Groom's email cannot exceed 100 characters.",
      "any.required": "Groom's email is required.",
    }),

    bride_country: Joi.string().required().max(100).messages({
      "string.base": "Bride's country must be a string.",
      "string.empty": "Bride's country is required.",
      "string.max": "Bride's country cannot exceed 100 characters.",
      "any.required": "Bride's country is required.",
    }),

    groom_country: Joi.string().required().max(100).messages({
      "string.base": "Groom's country must be a string.",
      "string.empty": "Groom's country is required.",
      "string.max": "Groom's country cannot exceed 100 characters.",
      "any.required": "Groom's country is required.",
    }),

    meeting_date: Joi.date().required().messages({
      "date.base": "Meeting date must be a valid date.",
      "any.required": "Meeting date is required.",
    }),

    engagement_date: Joi.date().required().messages({
      "date.base": "Engagement date must be a valid date.",
      "any.required": "Engagement date is required.",
    }),

    marriage_date: Joi.date().required().messages({
      "date.base": "Marriage date must be a valid date.",
      "any.required": "Marriage date is required.",
    }),

    current_country: Joi.string().required().max(100).messages({
      "string.base": "Current country must be a string.",
      "string.empty": "Current country is required.",
      "string.max": "Current country cannot exceed 100 characters.",
      "any.required": "Current country is required.",
    }),

    contact_number: Joi.string().allow('').max(20).messages({
      "string.base": "Contact number must be a string.",
      "string.max": "Contact number cannot exceed 20 characters.",
    }),

    story_text: Joi.string().required().messages({
      "string.base": "Story text must be a string.",
      "string.empty": "Story text is required.",
      "any.required": "Story text is required.",
    }),

    allow_in_ads: Joi.boolean().default(false).messages({
      "boolean.base": "Allow in ads must be a boolean.",
    }),
    media_files: Joi.array().items(Joi.string()).messages({
      "array.base": "Media files must be an array of strings.",
    }),
    
  }),
};

export const updateSuccessStoryStatusValidation = {
  params: Joi.object().keys({
    id: Joi.number().required().messages({
      "number.base": "ID must be a number.",
      "any.required": "ID is required.",
    }),
  }),
  body: Joi.object().keys({
    status: Joi.string().valid("pending", "approved", "rejected").required().messages({
      "string.base": "Status must be a string.",
      "string.empty": "Status is required.",
      "string.valid": "Status must be one of: pending, approved, rejected.",
      "any.required": "Status is required.",
    }),
    admin_notes: Joi.string().allow('').messages({
      "string.base": "Admin notes must be a string.",
    }),
  }),
};

export const getSuccessStoryByIdValidation = {
  params: Joi.object().keys({
    id: Joi.number().required().messages({
      "number.base": "ID must be a number.",
      "any.required": "ID is required.",
    }),
  }),
};

export const deleteSuccessStoryValidation = {
  params: Joi.object().keys({
    id: Joi.number().required().messages({
      "number.base": "ID must be a number.",
      "any.required": "ID is required.",
    }),
  }),
};
