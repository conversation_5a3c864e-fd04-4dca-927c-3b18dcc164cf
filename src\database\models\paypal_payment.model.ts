import {
    AllowNull,
    AutoIncrement,
    BelongsTo,
    Column,
    DataType,
    Default,
    ForeignKey,
    Model,
    PrimaryKey,
    Table,
    Index,
} from "sequelize-typescript";
import User from "./user.model";
import SubscriptionPlan from "./subscription_plans.mode";

export interface PaypalPaymentI {
    id?: number;
    user_id: number;
    plan_id?: number;
    paypal_order_id: string;
    paypal_payment_id?: string;
    paypal_payer_id?: string;
    amount: number;
    currency: string;
    status: "pending" | "completed" | "failed" | "cancelled" | "refunded";
    payment_method: string;
    description?: string;
    paypal_response?: any;
    refund_id?: string;
    refund_amount?: number;
    refund_reason?: string;
    webhook_event_id?: string;
    created_at?: Date;
    updated_at?: Date;
}

@Table({
    tableName: "paypal_payments",
    timestamps: true,
    underscored: true,
})
class PaypalPayment extends Model<PaypalPaymentI> implements PaypalPaymentI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @ForeignKey(() => SubscriptionPlan)
    @AllowNull(true)
    @Column
    plan_id?: number;

    @AllowNull(false)
    @Column(DataType.STRING(100))
    paypal_order_id: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    paypal_payment_id?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    paypal_payer_id?: string;

    @AllowNull(false)
    @Column(DataType.DECIMAL(10, 2))
    amount: number;

    @AllowNull(false)
    @Default("USD")
    @Column(DataType.STRING(3))
    currency: string;

    @AllowNull(false)
    @Default("pending")
    @Column(DataType.ENUM("pending", "completed", "failed", "cancelled", "refunded"))
    status: "pending" | "completed" | "failed" | "cancelled" | "refunded";

    @AllowNull(false)
    @Default("paypal")
    @Column(DataType.STRING(50))
    payment_method: string;

    @AllowNull(true)
    @Column(DataType.TEXT)
    description?: string;

    @AllowNull(true)
    @Column(DataType.JSON)
    paypal_response?: any;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    refund_id?: string;

    @AllowNull(true)
    @Column(DataType.DECIMAL(10, 2))
    refund_amount?: number;

    @AllowNull(true)
    @Column(DataType.TEXT)
    refund_reason?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    webhook_event_id?: string;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE" })
    user: User;

    @BelongsTo(() => SubscriptionPlan, { foreignKey: "plan_id", onDelete: "SET NULL" })
    subscriptionPlan: SubscriptionPlan;
}

export default PaypalPayment;
