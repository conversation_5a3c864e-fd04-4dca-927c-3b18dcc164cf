# Khalti Payment Gateway Integration

## Overview
This document provides comprehensive information about the Khalti payment gateway integration in the BarBadhu application.

## Environment Variables
Add the following environment variables to your `.env` file:

```env
# Khalti Payment Gateway Configuration
KHALTI_SECRET_KEY=your_khalti_secret_key_here
KHALTI_PUBLIC_KEY=your_khalti_public_key_here
KHALTI_API_URL=https://a.khalti.com/api/v2

# Frontend URLs for payment callbacks
FRONTEND_URL=http://localhost:3000
```

## Database Tables

### khalti_payments
Stores all Khalti payment transactions with the following key fields:
- `pidx`: Khalti payment index (unique identifier)
- `khalti_transaction_id`: Transaction ID from Khalti
- `amount`: Payment amount in NPR
- `status`: Payment status (pending, completed, failed, cancelled, refunded, expired)
- `mobile`: Customer mobile number
- `product_identity`: Product identifier
- `return_url`: URL to redirect after payment

### khalti_webhook_events
Stores webhook events from Khalti for audit and processing:
- `event_id`: Unique event identifier
- `event_type`: Type of webhook event
- `event_data`: Complete webhook payload
- `processed`: Whether the event has been processed

## API Endpoints

### Public Endpoints
- `GET /api/khalti/config` - Get payment configuration for frontend

### Protected Endpoints (Require Authentication)
- `POST /api/khalti/initiate` - Initiate a new payment
- `POST /api/khalti/verify` - Verify payment status
- `GET /api/khalti/status/:pidx` - Check payment status by pidx
- `GET /api/khalti/payments` - Get user's payment history
- `GET /api/khalti/payments/:payment_id` - Get specific payment details

### Admin Endpoints
- `GET /api/khalti/admin/transactions` - Get all transactions (admin only)
- `GET /api/khalti/admin/analytics` - Get payment analytics (admin only)

### Webhook Endpoint
- `POST /api/khalti/webhook` - Handle Khalti webhooks

## Usage Examples

### 1. Initiate Payment
```javascript
const response = await fetch('/api/khalti/initiate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_jwt_token'
  },
  body: JSON.stringify({
    plan_id: 1, // Optional: for subscription payments
    amount: 1000, // Required if plan_id not provided
    mobile: "9800000000", // Optional
    product_name: "Premium Subscription",
    return_url: "http://localhost:3000/payment/success"
  })
});

const data = await response.json();
// Redirect user to data.payment_url
```

### 2. Verify Payment
```javascript
const response = await fetch('/api/khalti/verify', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_jwt_token'
  },
  body: JSON.stringify({
    pidx: "payment_index_from_khalti"
  })
});

const data = await response.json();
// Check data.local_status for payment status
```

### 3. Get Payment History
```javascript
const response = await fetch('/api/khalti/payments?page=1&limit=10&status=completed', {
  headers: {
    'Authorization': 'Bearer your_jwt_token'
  }
});

const data = await response.json();
// data.payments contains the payment history
```

## Payment Flow

1. **Initiate Payment**: Call `/api/khalti/initiate` with payment details
2. **Redirect User**: Redirect user to Khalti payment page using the returned URL
3. **User Completes Payment**: User completes payment on Khalti's secure page
4. **Return to App**: User is redirected back to your return_url
5. **Verify Payment**: Call `/api/khalti/verify` to confirm payment status
6. **Activate Subscription**: If payment is for a subscription, it's automatically activated

## Webhook Processing

Khalti sends webhooks for payment status changes. The webhook endpoint:
- Verifies webhook signature for security
- Processes payment status updates
- Automatically activates subscriptions for completed payments
- Handles refunds and cancellations

## Error Handling

The API returns standardized error responses:
```json
{
  "success": false,
  "message": "Error description",
  "statusCode": 400
}
```

Common error scenarios:
- Invalid payment details
- Payment not found
- Payment already processed
- Subscription plan not found
- Insufficient permissions

## Security Features

1. **Webhook Signature Verification**: All webhooks are verified using HMAC-SHA256
2. **User Authorization**: All endpoints require valid JWT tokens
3. **Payment Validation**: Payments are validated against user ownership
4. **Secure Configuration**: Sensitive keys are stored in environment variables

## Testing

For testing, use Khalti's sandbox environment:
- Use test credentials provided by Khalti
- Test with various payment scenarios
- Verify webhook processing with test events

## Integration with Subscription System

The Khalti integration is fully integrated with the subscription system:
- Payments automatically activate subscriptions
- Usage tracking is maintained
- Subscription expiry is handled
- Refunds deactivate subscriptions

## Monitoring and Analytics

The system provides comprehensive analytics:
- Total payments and amounts
- Status breakdown
- Monthly trends
- User-specific analytics
- Admin dashboard data
