"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const sequelize_1 = require("sequelize");
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const chat_model_1 = __importDefault(require("../../database/models/chat.model"));
const message_model_1 = __importDefault(require("../../database/models/message.model"));
const user_model_1 = __importDefault(require("../../database/models/user.model"));
const sequelize_2 = __importDefault(require("sequelize"));
class ChatService {
}
_a = ChatService;
/**
 * Create a new chat between two users
 * @param {number} user1Id - First user ID
 * @param {number} user2Id - Second user ID
 * @returns {Promise<Chat>}
 */
ChatService.createChat = (user1Id, user2Id) => __awaiter(void 0, void 0, void 0, function* () {
    // Check if chat already exists
    try {
        const existingChat = yield chat_model_1.default.findOne({
            where: {
                [sequelize_1.Op.or]: [
                    { user1_id: user1Id, user2_id: user2Id },
                    { user1_id: user2Id, user2_id: user1Id }
                ]
            }
        });
        if (existingChat) {
            return existingChat;
        }
        const chat = yield chat_model_1.default.create({
            user1_id: user1Id,
            user2_id: user2Id,
            is_active: true,
            last_message_at: new Date(),
            auto_delete_days: null
        });
        // Create new chat
        return yield _a.getChatById(chat.id, user1Id);
    }
    catch (error) {
        console.log('error: ', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get chat by ID
 * @param {number} chatId - Chat ID
 * @returns {Promise<Chat>}
 */
ChatService.getChatById = (chatId, userId) => __awaiter(void 0, void 0, void 0, function* () {
    const chat = yield chat_model_1.default.findByPk(chatId, {
        include: [
            {
                model: user_model_1.default,
                as: 'user1',
                attributes: ['id', 'first_name', 'last_name', 'email', 'profile_image', 'member_id'],
            },
            {
                model: user_model_1.default,
                as: 'user2',
                attributes: ['id', 'first_name', 'last_name', 'email', 'profile_image', 'member_id'],
            },
            {
                model: message_model_1.default,
                separate: true,
                order: [['createdAt', 'ASC']],
            },
        ],
        attributes: {
            include: [
                [
                    sequelize_2.default.literal(`CASE 
              WHEN user1_id = ${userId} THEN 'user2'
              ELSE 'user1' 
            END`),
                    'other_user'
                ]
            ]
        },
    });
    if (!chat) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, 'Chat not found');
    }
    return chat;
});
/**
 * Get all chats for a user
 * @param {number} userId - User ID
 * @returns {Promise<Chat[]>}
 */
ChatService.getUserChats = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const chats = yield chat_model_1.default.findAll({
            where: {
                [sequelize_1.Op.or]: [
                    { user1_id: userId },
                    { user2_id: userId }
                ],
                is_active: true
            },
            include: [
                {
                    model: user_model_1.default,
                    as: 'user1',
                    attributes: ['id', 'first_name', 'last_name', 'email', 'profile_image', 'member_id'],
                },
                {
                    model: user_model_1.default,
                    as: 'user2',
                    attributes: ['id', 'first_name', 'last_name', 'email', 'profile_image', 'member_id'],
                },
                {
                    model: message_model_1.default,
                    limit: 1,
                    order: [['createdAt', 'DESC']],
                }
            ],
            attributes: {
                include: [
                    [
                        sequelize_2.default.literal(`CASE 
              WHEN user1_id = ${userId} THEN 'user2'
              ELSE 'user1' 
            END`),
                        'other_user'
                    ]
                ]
            },
            order: [['last_message_at', 'DESC']]
        });
        return chats;
    }
    catch (error) {
        console.log('error: ', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Set auto-delete days for a chat
 * @param {number} chatId - Chat ID
 * @param {number} days - Number of days after which messages will be auto-deleted
 * @returns {Promise<Chat>}
 */
ChatService.setAutoDeleteDays = (chatId, days) => __awaiter(void 0, void 0, void 0, function* () {
    const chat = yield chat_model_1.default.findByPk(chatId);
    if (!chat) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, 'Chat not found');
    }
    yield chat.update({ auto_delete_days: days });
    return chat;
});
/**
 * Delete a chat (soft delete by setting is_active to false)
 * @param {number} chatId - Chat ID
 * @param {number} userId - User ID requesting the deletion
 * @returns {Promise<boolean>}
 */
ChatService.deleteChat = (chatId, userId) => __awaiter(void 0, void 0, void 0, function* () {
    const chat = yield chat_model_1.default.findByPk(chatId);
    if (!chat) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, 'Chat not found');
    }
    // Check if user is part of the chat
    if (chat.user1_id !== userId && chat.user2_id !== userId) {
        throw new ApiError_1.default(http_status_1.default.FORBIDDEN, 'You do not have permission to delete this chat');
    }
    yield chat.update({ is_active: false });
    return true;
});
exports.default = ChatService;
