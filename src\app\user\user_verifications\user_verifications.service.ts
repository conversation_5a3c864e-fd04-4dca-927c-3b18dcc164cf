import httpStatus from "http-status";
import { Op } from "sequelize";
import UserVerification from "../../../database/models/user_verifications.model";
import ApiError from "../../../utils/ApiError";
import httpMessages from "../../../config/httpMessages";
import User from "../../../database/models/user.model";
import axios from "axios";

export default class UserVerificationService {
    constructor() { }

    /**
     * Create a UserVerification
     * @param {Object} body
     * @returns {Promise<UserVerification>}
     */
    static createUserVerification = async (body: any) => {
        try {
            const details: UserVerification = await UserVerification.create(body);
            return details;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Return UserVerifications
     * @param {Object} options
     * @param {number} [options.page] - Current page number (optional)
     * @param {number} [options.limit] - Number of items per page (optional)
     * @param {string} [options.search] - Search term for filtering (optional)
     * @returns {Promise<UserVerification[]>}
     */
    static getUserVerifications = async (options: {
        page?: number;
        limit?: number;
        search?: string;
    }) => {
        try {
            const { page, limit, search } = options;
            const whereCondition = search
                ? {
                    [Op.or]: [
                        { role_name: { [Op.like]: `%${search.toLowerCase()}%` } },
                    ],
                }
                : {};

            const queryOption: any = {
                where: whereCondition,
                // order: [["createdAt", "DESC"]],
            };
            // If pagination is provided, apply pagination
            if (page && limit) {
                const offset = (page - 1) * limit;
                queryOption.limit = limit;
                queryOption.offset = offset;
            }
            const data = await UserVerification.findAndCountAll(queryOption);
            if (page && limit) {
                return {
                    totalItems: data.count,
                    totalPages: Math.ceil(data.count / limit),
                    currentPage: page,
                    user_verifications: data.rows,
                };
            } else {
                return data.rows;
            }
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Get UserVerification by id
     * @param {Number} id
     * @returns {Promise<UserVerification>}
     */
    static getUserVerificationById = async (id: number) => {
        try {
            return await User.findOne({
                where: { id: id },
                attributes: { exclude: ["password"] },
                include: [
                    {
                        model: UserVerification,
                        as: "verification",
                    },
                ],
            }).then((data: any) => data?.toJSON());
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Update role by id
     * @param {Number} Id
     * @param {Object} updateBody
     * @returns {Promise<Role>}
     */
    static updateUserVerificationById = async (Id: number, updateBody: any) => {
        const details = await UserVerification.findByPk(Id);
        if (!details) {
            throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER_VERIFICATION.NOT_FOUND);
        }

        Object.assign(details, updateBody);
        await details.save();
        return details;
    };

    /**
     * Delete role by id
     * @param {Number} Id
     * @returns {Promise<Role>}
     */
    static deleteUserVerificationById = async (Id: number) => {
        try {
            const details: any = await UserVerification.findByPk(Id);
            if (!details) {
                throw new ApiError(httpStatus.NOT_FOUND, httpMessages.ROLES.NOT_FOUND);
            }
            await details.destroy();
            return details;
        } catch (error: any) {
            throw new ApiError(
                error.status || httpStatus.BAD_REQUEST,
                error.message || "Error deleting Role."
            );
        }
    };

    /**
     * Send phone otp
     * @param {Number} userId
     * @param {String} phone
     * @returns {Promise<UserVerification>}
     */
    static sendPhoneOtp = async (userId: number, phone: string, phone_code: string) => {
        try {
            const verification = await UserVerification.findOne({
                where: { user_id: userId },
            });
            if (!verification) {
                throw new ApiError(httpStatus.NOT_FOUND, "User not found");
            }
            const otp = Math.floor(100000 + Math.random() * 900000).toString();
            const otpExpiry = new Date(Date.now() + 5 * 60 * 1000);
            if (phone_code === "+977") {
                const url = 'https://api.sparrowsms.com/v2/sms/';
                const fullNumber = phone;
                const localNumber = fullNumber.replace("+977", "");
                const response = await axios.post(url, {
                    token: process.env.SPARROW_TOKEN,
                    from: process.env.SPARROW_FROM,
                    to: localNumber,
                    text: `Your verification code is ${otp}. Please enter the code to verify your mobile. Thank you Barbadhu`,
                });
            } else {
                const client = require('twilio')(
                    process.env.TWILIO_ACCOUNT_SID,
                    process.env.TWILIO_AUTH_TOKEN
                );

                // Send OTP via Twilio
                const message = await client.messages.create({
                    body: `Your verification code is ${otp}. Please enter the code to verify your mobile. Thank you Barbadhu`,
                    to: phone_code + phone,
                    from: process.env.TWILIO_PHONE_NUMBER,
                });
                console.log('message: ', message);
            }

            await verification.update({ phone, phone_code, phone_otp: otp, phone_otp_expiry: otpExpiry });
            return verification;
        } catch (error: any) {
            console.log('error: ', error);
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    static verifyPhoneOtp = async (userId: number, phone: string, otp: string, phone_code: string) => {
        try {
            const verification: any = await UserVerification.findOne({
                where: { user_id: userId, phone, phone_otp: otp },
            });
            if (!verification) {
                throw new ApiError(httpStatus.BAD_REQUEST, "Invalid OTP");
            }

            if (verification.phone_otp !== otp) {
                throw new ApiError(httpStatus.BAD_REQUEST, httpMessages.OTP.INVALID_OTP);
            };

            if (new Date() > new Date(verification.phone_otp_expiry)) {
                throw new ApiError(httpStatus.GONE, httpMessages.OTP.INVALID_OTP_EXPIRED);
            };

            await verification.update({ phone_otp: '', phone_otp_expiry: null, is_phone_verified: true });
            return verification;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    static uploadIdDocuments = async (userId: number, body: any) => {
        try {
            const verification = await UserVerification.findOne({
                where: { user_id: userId },
            });
            if (!verification) {
                throw new ApiError(httpStatus.NOT_FOUND, "User not found");
            }
            await verification.update({ ...body });
            return verification;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

}
