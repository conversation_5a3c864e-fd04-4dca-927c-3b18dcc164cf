"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const bcrypt_1 = __importDefault(require("bcrypt"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
const sequelize_1 = require("sequelize");
const admin_model_1 = __importDefault(require("../../database/models/admin.model"));
const role_model_1 = __importDefault(require("../../database/models/role.model"));
class AdminService {
    constructor() { }
}
_a = AdminService;
AdminService.isPasswordMatch = (user, password) => __awaiter(void 0, void 0, void 0, function* () { return yield bcrypt_1.default.compare(password, user.password); });
/**
 * Login with username and password
 * @param {string} username
 * @param {string} password
 * @returns {Promise<Admin>}
 */
AdminService.loginUserWithUsernameAndPassword = (username, password) => __awaiter(void 0, void 0, void 0, function* () {
    const admin = yield _a.getAdminByusername(username, {
        shouldReturnPassword: true,
    }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    if (!admin) {
        throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.LOGIN.INCORRECT_EMAIL);
    }
    if (!admin || !(yield _a.isPasswordMatch(admin, password))) {
        throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.LOGIN.INCORRECT_PASS);
    }
    admin.password = undefined;
    return admin;
});
/**
 * Get Admin by username
 * @param {string} username
 * @param {any} options
 * @returns {Promise<Admin>}
 */
AdminService.getAdminByusername = (username_1, ...args_1) => __awaiter(void 0, [username_1, ...args_1], void 0, function* (username, options = {}) {
    const { shouldReturnPassword = false } = options;
    let excludeAttr = [];
    if (!shouldReturnPassword) {
        excludeAttr = ["password"];
    }
    return admin_model_1.default.findOne({
        where: { username },
        attributes: {
            exclude: excludeAttr,
        },
    });
});
/**
 * Create a Admin
 * @param {Object} adminBody
 * @returns {Promise<Admin>}
 */
AdminService.createAdmin = (adminBody) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        if (!adminBody.password) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.REGISTER.PASSWORD_MISMATCH);
        }
        else if (yield _a.getAdminByusername(adminBody.username)) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.REGISTER.EMAIL_ALREADY_TAKEN);
        }
        const admin = yield admin_model_1.default.create(adminBody);
        return _a.getAdminByusername(admin.username).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        const errorMessage = ((_b = error.original) === null || _b === void 0 ? void 0 : _b.message) || error.message || 'Unknown error occurred';
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage);
    }
});
/**
 * Return admins
 * @param {Object} options
 * @param {number} [options.page] - Current page number (optional)
 * @param {number} [options.limit] - Number of items per page (optional)
 * @param {string} [options.search] - Search term for filtering (optional)
 * @returns {Promise<Admin[]>}
 */
AdminService.getAdmins = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search } = options;
        const whereCondition = search
            ? {
                [sequelize_1.Op.or]: [
                    { first_name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                    { last_name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                    { email: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                    { username: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                ],
            }
            : {};
        const queryOption = {
            where: whereCondition,
            include: [
                {
                    model: role_model_1.default,
                    as: "role",
                    attributes: ["role_name"],
                },
            ],
            attributes: { exclude: ["password"] },
            order: [["createdAt", "DESC"]],
        };
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const admins = yield admin_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: admins.count,
                totalPages: Math.ceil(admins.count / limit),
                currentPage: page,
                admins: admins.rows,
            };
        }
        else {
            return admins.rows;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get admin by id
 * @param {Number} id
 * @returns {Promise<Admin>}
 */
AdminService.getAdminById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return admin_model_1.default.findOne({
            where: { id },
            attributes: { exclude: ["password"] },
        }).then((data) => {
            if (!data)
                return null;
            const jsonData = data.toJSON();
            jsonData.permissions =
                typeof jsonData.permissions === "string"
                    ? JSON.parse(jsonData.permissions)
                    : jsonData.permissions;
            return jsonData;
        });
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update admin by id
 * @param {Number} adminId
 * @param {Object} updateBody
 * @returns {Promise<Admin>}
 */
AdminService.updateAdminById = (adminId, updateBody) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const admin = yield admin_model_1.default.findByPk(adminId);
        if (!admin) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER.NOT_FOUND);
        }
        if (updateBody.password) {
            updateBody.password = yield bcrypt_1.default.hash(updateBody.password, 10);
        }
        Object.assign(admin, updateBody);
        yield admin.save();
        return admin;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Delete admin by id
 * @param {Number} adminId
 * @returns {Promise<Admin>}
 */
AdminService.deleteAdminById = (adminId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const admin = yield admin_model_1.default.findByPk(adminId);
        if (!admin) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER.NOT_FOUND);
        }
        yield admin.destroy();
        return admin;
    }
    catch (error) {
        throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting admin.");
    }
});
exports.default = AdminService;
