"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middlewares/auth");
const role_controller_1 = __importDefault(require("../app/admin/role/role.controller"));
const router = express_1.default.Router();
router.get("", auth_1.auth, role_controller_1.default.getAll);
router.post("", auth_1.auth, role_controller_1.default.create);
router.get("/:id", auth_1.auth, role_controller_1.default.showById);
router.put("/:id", auth_1.auth, role_controller_1.default.update);
router.delete("/:id", auth_1.auth, role_controller_1.default.delete);
exports.default = router;
