import httpStatus from "http-status";
import ApiError from "../../utils/ApiError";
import UserSubscription from "../../database/models/user_subscriptions.model";
import SubscriptionPlan from "../../database/models/subscription_plans.mode";
import UserActionLog from "../../database/models/user_action_log.model";
import User from "../../database/models/user.model";
import PaypalPayment from "../../database/models/paypal_payment.model";
import KhaltiPayment from "../../database/models/khalti_payment.model";
import PromoCodeService from "../promo_code/promo_code.service";
import { Op } from "sequelize";

interface PurchaseSubscriptionParams {
    user_id: number;
    plan_id: number;
    payment_id?: number;
    payment_method?: "paypal" | "khalti";
    promo_code?: string;
}

interface TrackUsageParams {
    user_id: number;
    action_type: "interest_sent" | "contact_viewed" | "profile_viewed" | "chat_initiated";
    target_user_id: number;
}

interface SubscriptionStatus {
    isActive: boolean;
    subscription?: UserSubscription;
    plan?: SubscriptionPlan;
    message: string;
    remainingUsage?: {
        interest_sent: number;
        contact_viewed: number;
        profile_viewed: number;
        chat_initiated: number;
    };
}

export default class SubscriptionService {
    constructor() { }

    // Purchase subscription with payment integration
    static purchaseSubscription = async (params: PurchaseSubscriptionParams) => {
        try {
            const { user_id, plan_id, payment_id, payment_method = "paypal", promo_code } = params;

            // Validate subscription plan
            const plan = await SubscriptionPlan.findByPk(plan_id);
            if (!plan || !plan.is_active) {
                throw new ApiError(httpStatus.NOT_FOUND, "Subscription plan not found or inactive");
            }

            // Initialize pricing variables
            let originalAmount = plan.price;
            let discountAmount = 0;
            let finalAmount = plan.price;
            let promoCodeId: number | undefined;

            // Validate and apply promo code if provided
            if (promo_code) {
                const promoValidation = await PromoCodeService.validatePromoCode({
                    code: promo_code,
                    user_id,
                    plan_id,
                    plan_price: plan.price
                });

                if (!promoValidation.isValid) {
                    throw new ApiError(httpStatus.BAD_REQUEST, promoValidation.message || "Invalid promo code");
                }

                if (promoValidation.promoCode && promoValidation.discountAmount !== undefined && promoValidation.finalAmount !== undefined) {
                    promoCodeId = promoValidation.promoCode.id;
                    discountAmount = promoValidation.discountAmount;
                    finalAmount = promoValidation.finalAmount;
                }
            }

            // Validate payment if provided
            let paymentStatus = "pending";
            if (payment_id) {
                let payment = null;
                if (payment_method === "paypal") {
                    payment = await PaypalPayment.findOne({
                        where: { id: payment_id, user_id, status: "completed" }
                    });
                } else if (payment_method === "khalti") {
                    payment = await KhaltiPayment.findOne({
                        where: { id: payment_id, user_id, status: "completed" }
                    });
                }

                if (payment) {
                    paymentStatus = "completed";
                } else {
                    throw new ApiError(httpStatus.BAD_REQUEST, "Payment not found or not completed");
                }
            }

            const startDate = new Date();
            const endDate = new Date();
            endDate.setDate(startDate.getDate() + plan.duration_days);

            // Check for existing active subscription
            const existingSubscription = await UserSubscription.findOne({
                where: { user_id, is_active: true }
            });

            let subscription;
            if (existingSubscription) {
                // Update existing subscription
                subscription = await existingSubscription.update({
                    plan_id,
                    payment_id,
                    payment_status: paymentStatus as any,
                    start_date: startDate,
                    end_date: endDate,
                    expires_at: endDate,
                    interest_sent_count: 0,
                    contact_viewed_count: 0,
                    profile_viewed_count: 0,
                    chat_initiated_count: 0,
                    promo_code_id: promoCodeId,
                    discount_amount: discountAmount,
                    original_amount: originalAmount,
                    is_active: paymentStatus === "completed",
                    token: `sub_${Date.now()}_${user_id}`,
                });
            } else {
                // Create new subscription
                subscription = await UserSubscription.create({
                    user_id,
                    plan_id,
                    payment_id,
                    payment_status: paymentStatus as any,
                    start_date: startDate,
                    end_date: endDate,
                    auto_renew: false,
                    issued_at: startDate,
                    expires_at: endDate,
                    interest_sent_count: 0,
                    contact_viewed_count: 0,
                    profile_viewed_count: 0,
                    chat_initiated_count: 0,
                    promo_code_id: promoCodeId,
                    discount_amount: discountAmount,
                    original_amount: originalAmount,
                    is_active: paymentStatus === "completed",
                    token: `sub_${Date.now()}_${user_id}`,
                });
            }

            // Record promo code usage if promo code was applied and payment is completed
            if (promoCodeId && paymentStatus === "completed") {
                await PromoCodeService.applyPromoCode({
                    promo_code_id: promoCodeId,
                    user_id,
                    subscription_id: subscription.id,
                    original_amount: originalAmount,
                    discount_amount: discountAmount,
                    final_amount: finalAmount
                });
            }

            return {
                subscription,
                plan,
                originalAmount,
                discountAmount,
                finalAmount,
                promoCodeApplied: !!promoCodeId,
                message: paymentStatus === "completed" ?
                    "Subscription activated successfully" :
                    "Subscription created, pending payment completion"
            };
        } catch (error: any) {
            console.error('Purchase subscription error:', error);
            throw error;
        }
    };

    // Track usage with duplicate prevention
    static trackUsage = async (params: TrackUsageParams) => {
        try {
            const { user_id, action_type, target_user_id } = params;

            // Get active subscription
            const subscription = await this.getActiveSubscription(user_id);
            if (!subscription.isActive || !subscription.subscription) {
                throw new ApiError(httpStatus.FORBIDDEN, "No active subscription found");
            }

            const userSubscription = subscription.subscription;
            const plan = subscription.plan!;

            // Check if action already performed for this target
            const existingAction = await UserActionLog.findOne({
                where: {
                    user_id,
                    action_type,
                    target_user_id,
                    subscription_id: userSubscription.id!
                }
            });
            
            console.log('existingAction: ', existingAction);
            if (existingAction) {
                return {
                    success: false,
                    message: "Action already performed for this user in current subscription",
                    alreadyPerformed: true
                };
            }

            // Check usage limits
            const currentCount = this.getCurrentUsageCount(userSubscription, action_type);
            const limit = this.getUsageLimit(plan, action_type);

            if (limit > 0 && currentCount > limit) {
                console.log('limit: ', limit);
                throw new ApiError(httpStatus.FORBIDDEN, `${action_type} limit exceeded for current subscription`);
            }

            // Log the action
            await UserActionLog.create({
                user_id,
                action_type,
                target_user_id,
                subscription_id: userSubscription.id!,
                action_date: new Date()
            });

            // Update usage count
            const updateField = this.getUsageFieldName(action_type);
            await userSubscription.update({
                [updateField]: currentCount + 1
            });

            return {
                success: true,
                message: "Usage tracked successfully",
                currentUsage: currentCount + 1,
                remainingUsage: limit > 0 ? limit - (currentCount + 1) : -1
            };
        } catch (error: any) {
            console.error('Track usage error:', error);
            throw error;
        }
    };

    // Get active subscription status
    static getActiveSubscription = async (user_id: number): Promise<SubscriptionStatus> => {
        try {
            const subscription = await UserSubscription.findOne({
                where: {
                    user_id,
                    is_active: true,
                    expires_at: { [Op.gt]: new Date() }
                },
                include: [
                    {
                        model: SubscriptionPlan,
                        attributes: ['id', 'name', 'interest_limit', 'contact_limit', 'view_profiles_limit', 'chat_limit']
                    }
                ]
            });

            if (!subscription) {
                return {
                    isActive: false,
                    message: "No active subscription found"
                };
            }

            const plan = await SubscriptionPlan.findByPk(subscription.plan_id);
            if (!plan) {
                return {
                    isActive: false,
                    message: "Subscription plan not found"
                };
            }

            // Check if subscription is expired
            if (new Date() > subscription.expires_at) {
                await subscription.update({ is_active: false });
                return {
                    isActive: false,
                    subscription,
                    plan,
                    message: "Subscription has expired"
                };
            }

            // Calculate remaining usage
            const remainingUsage = {
                interest_sent: plan.interest_limit > 0 ?
                    Math.max(0, plan.interest_limit - subscription.interest_sent_count) : -1,
                contact_viewed: plan.contact_limit > 0 ?
                    Math.max(0, plan.contact_limit - subscription.contact_viewed_count) : -1,
                profile_viewed: plan.view_profiles_limit > 0 ?
                    Math.max(0, plan.view_profiles_limit - subscription.profile_viewed_count) : -1,
                chat_initiated: plan.chat_limit > 0 ?
                    Math.max(0, plan.chat_limit - subscription.chat_initiated_count) : -1,
            };

            return {
                isActive: true,
                subscription,
                plan,
                message: "Active subscription found",
                remainingUsage
            };
        } catch (error: any) {
            console.error('Get active subscription error:', error);
            throw error;
        }
    };

    // Check if user can perform action
    static canPerformAction = async (user_id: number, action_type: string, target_user_id?: number) => {
        try {
            const subscriptionStatus = await this.getActiveSubscription(user_id);

            if (!subscriptionStatus.isActive) {
                return {
                    canPerform: false,
                    message: subscriptionStatus.message,
                    requiresSubscription: true
                };
            }

            const { subscription, plan } = subscriptionStatus;
            const currentCount = this.getCurrentUsageCount(subscription!, action_type);
            const limit = this.getUsageLimit(plan!, action_type);

            // Check if unlimited (limit = 0 or -1)
            if (limit <= 0) {
                return {
                    canPerform: true,
                    message: "Unlimited usage available"
                };
            }

            // Check for duplicate action if target_user_id provided
            if (target_user_id) {
                console.log('target_user_id: ', target_user_id);
                const existingAction = await UserActionLog.findOne({
                    where: {
                        user_id,
                        action_type: action_type as any,
                        target_user_id,
                        subscription_id: subscription!.id!
                    }
                });

                if (existingAction) {
                    return {
                        canPerform: true,
                        message: "Action already performed for this user",
                        alreadyPerformed: true
                    };
                }
            }

              // Check if limit exceeded
            if (currentCount >= limit) {
                return {
                    canPerform: false,
                    message: `${action_type === 'interest_sent' ? 'Interest' : action_type === 'contact_viewed' ? 'Contact' : action_type === 'profile_viewed' ? 'Profile' : 'Chat'} limit exceeded`,
                    currentUsage: currentCount,
                    limit: limit
                };
            }

            return {
                canPerform: true,
                message: "Action allowed",
                currentUsage: currentCount,
                limit: limit,
                remaining: limit - currentCount
            };
        } catch (error: any) {
            console.error('Can perform action error:', error);
            throw error;
        }
    };

    // Daily expiry check (for cron job)
    static checkAndUpdateExpiredSubscriptions = async () => {
        try {
            const expiredSubscriptions = await UserSubscription.findAll({
                where: {
                    is_active: true,
                    expires_at: { [Op.lt]: new Date() }
                }
            });

            const updatePromises = expiredSubscriptions.map(subscription =>
                subscription.update({
                    is_active: false,
                    revoked_at: new Date()
                })
            );

            await Promise.all(updatePromises);

            console.log(`Updated ${expiredSubscriptions.length} expired subscriptions`);
            return {
                updatedCount: expiredSubscriptions.length,
                expiredSubscriptions: expiredSubscriptions.map(sub => ({
                    id: sub.id,
                    user_id: sub.user_id,
                    expires_at: sub.expires_at
                }))
            };
        } catch (error: any) {
            console.error('Check expired subscriptions error:', error);
            throw error;
        }
    };

    // Get user subscription history
    static getUserSubscriptionHistory = async (user_id: number) => {
        try {
            const subscriptions = await UserSubscription.findAll({
                where: { user_id },
                include: [
                    {
                        model: SubscriptionPlan,
                        attributes: ['id', 'name', 'price', 'duration_days']
                    }
                ],
                order: [['createdAt', 'DESC']]
            });

            return subscriptions;
        } catch (error: any) {
            console.error('Get subscription history error:', error);
            throw error;
        }
    };

    // Helper methods
    private static getCurrentUsageCount(subscription: UserSubscription, action_type: string): number {
        switch (action_type) {
            case 'interest_sent':
                return subscription.interest_sent_count;
            case 'contact_viewed':
                return subscription.contact_viewed_count;
            case 'profile_viewed':
                return subscription.profile_viewed_count;
            case 'chat_initiated':
                return subscription.chat_initiated_count;
            default:
                return 0;
        }
    }

    private static getUsageLimit(plan: SubscriptionPlan, action_type: string): number {
        switch (action_type) {
            case 'interest_sent':
                return plan.interest_limit || 0;
            case 'contact_viewed':
                return plan.contact_limit || 0;
            case 'profile_viewed':
                return plan.view_profiles_limit || 0;
            case 'chat_initiated':
                return plan.chat_limit || 0;
            default:
                return 0;
        }
    }

    private static getUsageFieldName(action_type: string): string {
        switch (action_type) {
            case 'interest_sent':
                return 'interest_sent_count';
            case 'contact_viewed':
                return 'contact_viewed_count';
            case 'profile_viewed':
                return 'profile_viewed_count';
            case 'chat_initiated':
                return 'chat_initiated_count';
            default:
                throw new Error(`Unknown action type: ${action_type}`);
        }
    }
}
