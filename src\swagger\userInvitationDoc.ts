/**
 * @swagger
 * /user_invitation:
 *   get:
 *     tags:
 *       - UserInvitations
 *     summary: Get user invitations
 *     description: Retrieve a list of user invitations based on type (received, sent, accepted, declined).
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [received, sent, accepted, declined]
 *           default: received
 *         description: Type of invitations to retrieve.
 *     responses:
 *       200:
 *         description: Successfully retrieved invitations.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Invitations retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       sender_id:
 *                         type: integer
 *                         example: 101
 *                       receiver_id:
 *                         type: integer
 *                         example: 102
 *                       message:
 *                         type: string
 *                         example: "I would like to connect with you."
 *                       status:
 *                         type: string
 *                         enum: [pending, accepted, declined]
 *                         example: "pending"
 *                       is_read:
 *                         type: boolean
 *                         example: false
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                       sender:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 101
 *                           first_name:
 *                             type: string
 *                             example: "John"
 *                           last_name:
 *                             type: string
 *                             example: "Doe"
 *                           profile_created_for:
 *                             type: string
 *                             example: "Myself"
 *                       receiver:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 102
 *                           first_name:
 *                             type: string
 *                             example: "Jane"
 *                           last_name:
 *                             type: string
 *                             example: "Smith"
 *                           profile_created_for:
 *                             type: string
 *                             example: "Myself"
 *       401:
 *         description: Unauthorized - User not authenticated.
 *       500:
 *         description: Internal server error.
 * 
 * 
 * 
 *   post:
 *     tags:
 *       - UserInvitations
 *     summary: Send an invitation
 *     description: Send an invitation to another user.
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - receiver_id
 *             properties:
 *               receiver_id:
 *                 type: integer
 *                 example: 102
 *               message:
 *                 type: string
 *                 example: "I would like to connect with you."
 *     responses:
 *       201:
 *         description: Invitation sent successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Invitation sent successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     sender_id:
 *                       type: integer
 *                       example: 101
 *                     receiver_id:
 *                       type: integer
 *                       example: 102
 *                     message:
 *                       type: string
 *                       example: "I would like to connect with you."
 *                     status:
 *                       type: string
 *                       example: "pending"
 *                     is_read:
 *                       type: boolean
 *                       example: false
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Bad request - Invalid input or invitation already sent.
 *       401:
 *         description: Unauthorized - User not authenticated.
 *       500:
 *         description: Internal server error.
 * 
 * 
 * 
 * /user_invitation/{id}/status:
 *   put:
 *     tags:
 *       - UserInvitations
 *     summary: Update invitation status
 *     description: Accept or decline an invitation.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Invitation ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [accepted, declined]
 *                 example: "accepted"
 *     responses:
 *       200:
 *         description: Invitation status updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Invitation accepted successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     status:
 *                       type: string
 *                       example: "accepted"
 *       400:
 *         description: Bad request - Invalid status.
 *       401:
 *         description: Unauthorized - User not authenticated.
 *       404:
 *         description: Invitation not found or already processed.
 *       500:
 *         description: Internal server error.
 * 
 * 
 * 
 * /user_invitation/unread-count:
 *   get:
 *     tags:
 *       - UserInvitations
 *     summary: Get unread invitations count
 *     description: Get the count of unread invitations for the authenticated user.
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully retrieved unread count.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Unread count retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     count:
 *                       type: integer
 *                       example: 5
 *       401:
 *         description: Unauthorized - User not authenticated.
 *       500:
 *         description: Internal server error.
 * 
 * 
 * 
 * /user_invitation/{id}/read:
 *   put:
 *     tags:
 *       - UserInvitations
 *     summary: Mark invitation as read
 *     description: Mark an invitation as read.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Invitation ID
 *     responses:
 *       200:
 *         description: Invitation marked as read successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Invitation marked as read"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     is_read:
 *                       type: boolean
 *                       example: true
 *       401:
 *         description: Unauthorized - User not authenticated.
 *       404:
 *         description: Invitation not found or already read.
 *       500:
 *         description: Internal server error.
 */
