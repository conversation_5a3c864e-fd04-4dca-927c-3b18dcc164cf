import httpStatus from "http-status";
import { Op } from "sequelize";
import ApiError from "../../../utils/ApiError";
import httpMessages from "../../../config/httpMessages";
import UserInvitation from "../../../database/models/user_invitation.model";
import User from "../../../database/models/user.model";
import EmailService from "../../../common/services/email.service";
import UserBasicDetails from "../../../database/models/user_basic_details.model";
import UserLifestyle from "../../../database/models/user_lifestyle.model";
import UserEducationCareer from "../../../database/models/user_education_career.model";
import UserLocationDetails from "../../../database/models/user_location_details.model";
import SubscriptionService from "../../subscription/subscription.service";
import sequelize from "sequelize";

export default class UserInvitationService {
  static SubscriptionService = SubscriptionService;
  constructor() { }

  /**
   * Create an invitation
   * @param {Object} invitationData
   * @returns {Promise<UserInvitation>}
   */
  static createInvitation = async (invitationData: any) => {
    try {
      // Check if invitation already exists
      const existingInvitation = await UserInvitation.findOne({
        where: {
          sender_id: invitationData.sender_id,
          receiver_id: invitationData.receiver_id,
          status: "pending"
        }
      });

      if (existingInvitation) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          "Invitation already sent to this user"
        );
      }
      await SubscriptionService.trackUsage({
        user_id: invitationData.sender_id,
        action_type: 'interest_sent',
        target_user_id: invitationData.receiver_id
      });
      const invitation = await UserInvitation.create(invitationData);

      // Get sender and receiver details
      const sender = await User.findByPk(invitationData.sender_id);
      const receiver = await User.findByPk(invitationData.receiver_id);

      if (sender && receiver) {
        // Send email notification
        const inviteLink = `${process.env.FRONTEND_URL}/inbox?type=invitations`;

        let html = await EmailService.sendInvitationEmail(
          receiver.email,
          {
            senderName: `${sender.first_name} ${sender.last_name}`,
            receiverName: `${receiver.first_name} ${receiver.last_name}`,
            message: invitationData.message,
            inviteLink
          }
        );
      }

      return invitation;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get all invitations for a user
   * @param {number} userId
   * @param {string} status - 'received', 'sent', 'accepted', 'declined'
   * @returns {Promise<UserInvitation[]>}
   */
  static getUserInvitations = async (userId: number, status: string) => {
    try {
      console.log('status: ', status);
      const whereClause: any = {};

      if (status === 'pending') {
        whereClause.receiver_id = userId;
        whereClause.status = 'pending';
      } else if (status === 'sent') {
        whereClause.sender_id = userId;
        whereClause.status = 'pending';
      } else if (status === 'accepted') {
        whereClause[Op.or] = [
          { sender_id: userId, status: 'accepted' },
          { receiver_id: userId, status: 'accepted' }
        ];
        whereClause.status = 'accepted';
      } else if (status === 'declined') {
        whereClause[Op.or] = [
          { sender_id: userId, status: 'declined' },
          { receiver_id: userId, status: 'declined' }
        ];
        whereClause.status = 'declined';
      }

      console.log('whereClause: ', whereClause);
      const invitations = await UserInvitation.findAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'sender',
            attributes: ['id', 'first_name', 'last_name', 'profile_created_for', 'member_id', 'profile_image'],
            include: [
              {
                model: UserBasicDetails,
                attributes: ['religion', 'caste', 'gotra', 'marital_status']
              },
              {
                model: UserLifestyle,
                attributes: ['age', 'height_cm']
              },
              {
                model: UserEducationCareer,
                attributes: ['education', 'profession']
              },
              {
                model: UserLocationDetails,
                attributes: ['city', 'country_living_in']
              }
            ]
          },
          {
            model: User,
            as: 'receiver',
            attributes: ['id', 'first_name', 'last_name', 'profile_created_for', 'member_id', 'profile_image'],
            include: [
              {
                model: UserBasicDetails,
                attributes: ['religion', 'caste', 'gotra', 'marital_status']
              },
              {
                model: UserLifestyle,
                attributes: ['age', 'height_cm']
              },
              {
                model: UserEducationCareer,
                attributes: ['education', 'profession']
              },
              {
                model: UserLocationDetails,
                attributes: ['city', 'country_living_in']
              }
            ]
          }
        ],

        order: [['createdAt', 'DESC']]
      });

      const processedInvitations = invitations.map(invitation => {
        const invitationObj: any = invitation.toJSON();

        if (invitation.sender_id === userId) {
          invitationObj.other_user = 'receiver';
        } else {
          invitationObj.other_user = 'sender';
        }

        return invitationObj;
      });

      return processedInvitations;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
 * Get all invitations for a user
 * @param {number} userId
 * @param {string} type - 'received', 'sent', 'accepted', 'declined'
 * @returns {Promise<UserInvitation[]>}
 */
  static all = async (userId: number, type: string = 'received') => {
    try {
      const whereClause = {
        [Op.or]: [
          { sender_id: userId },
          { receiver_id: userId }
        ]
      };

      const invitations = await UserInvitation.findAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'sender',
            attributes: ['id', 'first_name', 'last_name', 'profile_created_for', 'member_id', 'profile_image'],
            include: [
              {
                model: UserBasicDetails,
                attributes: ['religion', 'caste', 'gotra', 'marital_status']
              },
              {
                model: UserLifestyle,
                attributes: ['age', 'height_cm']
              },
              {
                model: UserEducationCareer,
                attributes: ['education', 'profession']
              },
              {
                model: UserLocationDetails,
                attributes: ['city', 'country_living_in']
              }
            ]
          },
          {
            model: User,
            as: 'receiver',
            attributes: ['id', 'first_name', 'last_name', 'profile_created_for', 'member_id', 'profile_image'],
            include: [
              {
                model: UserBasicDetails,
                attributes: ['religion', 'caste', 'gotra', 'marital_status']
              },
              {
                model: UserLifestyle,
                attributes: ['age', 'height_cm']
              },
              {
                model: UserEducationCareer,
                attributes: ['education', 'profession']
              },
              {
                model: UserLocationDetails,
                attributes: ['city', 'country_living_in']
              }
            ]
          }
        ],
        order: [['createdAt', 'DESC']]
      });

      const result = {
        invitationUsers: [] as any[],  // pending received invitations
        acceptedUsers: [] as any[],    // accepted invitations
        sentUsers: [] as any[],        // sent invitations
        declinedUsers: [] as any[]     // declined invitations
      };

      invitations.forEach((invitation) => {
        if (invitation.status === 'pending') {
          result.invitationUsers.push(invitation);
        }
        if (invitation.status === 'accepted') {
          result.acceptedUsers.push(invitation);
        }
        if (invitation.status === 'declined') {
          result.declinedUsers.push(invitation);
        }
        if (invitation.sender_id === userId) {
          result.sentUsers.push(invitation);
        }
      });

      return result;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };


  /**
   * Format height from cm to feet and inches
   * @param {number} heightCm - Height in centimeters
   * @returns {string} - Formatted height string (e.g., "5'2\"")
   */
  static formatHeightToFeetInches = (heightCm: number): string => {
    if (!heightCm) return '';

    // Convert cm to inches
    const totalInches = heightCm / 2.54;

    // Calculate feet and remaining inches
    const feet = Math.floor(totalInches / 12);
    const inches = Math.round(totalInches % 12);

    return `${feet}'${inches}"`;
  };

  /**
   * Format location from separate fields
   * @param {Object} locationDetails - Location details object
   * @returns {string} - Formatted location string
   */
  static formatLocation = (locationDetails: any): string => {
    if (!locationDetails) return '';

    const parts = [];
    if (locationDetails.city) parts.push(locationDetails.city);
    if (locationDetails.country) parts.push(locationDetails.country);

    return parts.join(', ');
  };

  /**
   * Get context message for declined invitations
   * @param {UserInvitation} invitation - The invitation object
   * @param {number} userId - Current user ID
   * @returns {string} - Context message
   */
  static getDeclinedContext = (invitation: any, userId: number): string => {
    if (invitation.status !== 'declined') return '';

    // If current user is the sender and invitation was declined
    if (invitation.sender_id === userId) {
      return 'She declined your invitation. You cannot contact her for 7 days';
    }

    // If current user is the receiver and they declined the invitation
    if (invitation.receiver_id === userId) {
      return 'You declined her Invitation';
    }

    return '';
  };

  /**
   * Update invitation status
   * @param {number} invitationId
   * @param {string} status - 'accepted' or 'declined'
   * @param {number} userId - the user updating the status
   * @returns {Promise<UserInvitation>}
   */
  static updateInvitationStatus = async (invitationId: number, status: string, userId: number, userRole: string) => {
    try {
      let whereClause: any = {};
      if (userRole === 'sender') {
        whereClause = {
          id: invitationId,
          sender_id: userId,
          status: 'pending'
        };
      } else {
        whereClause = {
          id: invitationId,
          receiver_id: userId,
          status: 'pending'
        };
      }

      const invitation = await UserInvitation.findOne({
        where: whereClause,
        include: [
          { model: User, as: 'sender' },
          { model: User, as: 'receiver' }
        ]
      });

      if (!invitation) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          "Invitation not found or already processed"
        );
      }

      invitation.status = status as "accepted" | "declined";
      await invitation.save();

      // Send email notification to sender
      const sender = invitation.sender;
      const receiver = invitation.receiver;

      if (status === 'accepted') {
        await EmailService.sendInvitationAcceptedEmail(
          sender.email,
          {
            senderName: `${sender.first_name} ${sender.last_name}`,
            receiverName: `${receiver.first_name} ${receiver.last_name}`,
            message: invitation.message
          }
        );
      } else {
        await EmailService.sendInvitationDeclinedEmail(
          sender.email,
          {
            senderName: `${sender.first_name} ${sender.last_name}`,
            receiverName: `${receiver.first_name} ${receiver.last_name}`,
            message: invitation.message
          }
        );
      }

      return invitation;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get unread invitation count
   * @param {number} userId
   * @returns {Promise<number>}
   */
  static getUnreadCount = async (userId: number) => {
    try {
      const count = await UserInvitation.count({
        where: {
          receiver_id: userId,
          is_read: false
        }
      });

      return count;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Mark invitation as read
   * @param {number} invitationId
   * @param {number} userId
   * @returns {Promise<UserInvitation>}
   */
  static markAsRead = async (invitationId: number, userId: number) => {
    try {
      const invitation = await UserInvitation.findOne({
        where: {
          id: invitationId,
          receiver_id: userId,
          is_read: false
        }
      });

      if (!invitation) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          "Invitation not found or already read"
        );
      }

      invitation.is_read = true;
      await invitation.save();

      return invitation;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Check if an invitation exists between two users
   * @param {number} userId1 - First user ID
   * @param {number} userId2 - Second user ID
   * @returns {Promise<UserInvitation|null>} - The invitation if it exists, null otherwise
   */
  static checkInvitationExists = async (userId1: number, userId2: number) => {
    try {
      const invitation = await UserInvitation.findOne({
        where: {
          [Op.or]: [
            { sender_id: userId1, receiver_id: userId2 },
            { sender_id: userId2, receiver_id: userId1 }
          ]
        }
      });

      return invitation;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get invitation by ID
   * @param {number} invitationId - Invitation ID
   * @returns {Promise<UserInvitation|null>} - The invitation if it exists, null otherwise
   */
  static getInvitationById = async (invitationId: number) => {
    try {
      const invitation = await UserInvitation.findOne({
        where: { id: invitationId },
        include: [
          { model: User, as: 'sender' },
          { model: User, as: 'receiver' }
        ]
      });

      return invitation;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Remove friend (change invitation status to declined)
   * @param {number} invitationId - Invitation ID
   * @param {number} userId - User ID
   * @returns {Promise<UserInvitation>} - The updated invitation
   */
  static removeFriend = async (invitationId: number, userId: number) => {
    try {
      console.log('invitationId: ', invitationId);
      const invitation = await UserInvitation.findOne({
        where: {
          id: invitationId,
          status: 'accepted',
          // [Op.or]: [
          //   { sender_id: userId },
          //   { receiver_id: userId }
          // ]
        }
      });

      if (!invitation) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          "Accepted invitation not found"
        );
      }

      invitation.status = 'declined';
      await invitation.save();

      return invitation;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };
}