import { Request, Response } from "express";
import httpStatus from "http-status";
import UserVerificationService from "./user_verifications.service";
import catchAsync from "../../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../../utils/response";
import httpMessages from "../../../config/httpMessages";
import UserVerification from "../../../database/models/user_verifications.model";
import ApiError from "../../../utils/ApiError";
import UserService from "../user.service";

export default class UserVerificationController {
    static userVerificationService = UserVerificationService;
    static userService = UserService;
    constructor() { }

    static getAll = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page, limit, search } = request.query;
            const option = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
                search: search ? (search as string) : "",
            };
            const list = await this.userVerificationService.getUserVerifications(option);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.USER_VERIFICATION.SUCCESS,
                data: list,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static create = catchAsync(async (request: Request, response: Response) => {
        try {
            const body = { ...request.body };
            const craetedData: UserVerification = await this.userVerificationService.createUserVerification(body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.USER_VERIFICATION.ADD_SUCCESS,
                data: craetedData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static showById = catchAsync(async (request: Request, response: Response) => {
        try {
            const userId = request.decoded;
            const details = await this.userVerificationService.getUserVerificationById(userId);
            if (!details) {
                throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER_VERIFICATION.NOT_FOUND);
            }
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.USER_VERIFICATION.DETAILS.SUCCESS,
                data: details,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static update = catchAsync(async (request: Request, response: Response) => {
        try {
            const Id: number = parseInt(request.params.id, 10);
            const body = { ...request.body };

            const updatedData = await this.userVerificationService.updateUserVerificationById(Id, body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.USER_VERIFICATION.UPDATE_SUCCESS,
                data: updatedData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static delete = catchAsync(async (request: Request, response: Response) => {
        try {
            const id: number = parseInt(request.params.id, 10);
            await this.userVerificationService.deleteUserVerificationById(id);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.USER_VERIFICATION.DELETE,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static sendPhoneOtp = catchAsync(async (request: Request, response: Response) => {
        try {
            const { phone,phone_code } = request.body;
            const userId = request.decoded;
            const verification = await this.userVerificationService.sendPhoneOtp(userId, phone,phone_code);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "OTP sent successfully",
                data: verification,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static verifyPhoneOtp = catchAsync(async (request: Request, response: Response) => {
        try {
            const { phone, otp,phone_code } = request.body;
            const userId = request.decoded;
            const verification = await this.userVerificationService.verifyPhoneOtp(userId, phone, otp,phone_code);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Phone verified successfully",
                data: verification,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static uploadIdDocuments = catchAsync(async (request: Request, response: Response) => {
        try {
            const userId = request.decoded;
            const verification = await this.userVerificationService.uploadIdDocuments(userId, request.body);
            await this.userService.updateUserById(userId, { status: 'pending' });
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "ID documents uploaded successfully",
                data: verification,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

}
