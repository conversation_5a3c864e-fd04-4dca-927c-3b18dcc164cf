import axios from "axios";
import httpStatus from "http-status";
import ApiError from "../../utils/ApiError";
import KhaltiPayment from "../../database/models/khalti_payment.model";
import KhaltiWebhookEvent from "../../database/models/khalti_webhook_event.model";
import User from "../../database/models/user.model";
import SubscriptionPlan from "../../database/models/subscription_plans.mode";
import UserSubscription from "../../database/models/user_subscriptions.model";
import { Op, Sequelize } from "sequelize";
import crypto from "crypto";

const khalti_secret_key: any = process.env.KHALTI_SECRET_KEY;
const khalti_public_key: any = process.env.KHALTI_PUBLIC_KEY;
const khalti_api_url: any = process.env.KHALTI_API_URL || "https://a.khalti.com/api/v2";

interface InitiatePaymentParams {
    user_id: number;
    plan_id?: number;
    amount: number;
    mobile?: string;
    product_identity?: string;
    product_name?: string;
    product_url?: string;
    return_url?: string;
    website_url?: string;
}

interface PaymentFilters {
    page: number;
    limit: number;
    status?: string;
}

interface AnalyticsParams {
    start_date?: string;
    end_date?: string;
    user_id?: number;
}

interface RefundParams {
    amount?: number;
    reason?: string;
}

export default class KhaltiService {
    constructor() { }

    // Initiate payment using Khalti Payment Gateway
    static initiatePayment = async (params: InitiatePaymentParams) => {
        try {
            const { 
                user_id, 
                plan_id, 
                amount, 
                mobile,
                product_identity,
                product_name,
                product_url,
                return_url,
                website_url
            } = params;

            // Get subscription plan details if plan_id is provided
            let planDetails = null;
            if (plan_id) {
                planDetails = await SubscriptionPlan.findByPk(plan_id);
                if (!planDetails) {
                    throw new ApiError(httpStatus.NOT_FOUND, "Subscription plan not found");
                }
            }

            // Prepare payment data
            const paymentData = {
                return_url: return_url || `${process.env.FRONTEND_URL}/payment/success`,
                website_url: website_url || process.env.FRONTEND_URL,
                amount:  amount * 100, // Khalti expects amount in paisa
                purchase_order_id: `ORDER_${Date.now()}_${user_id}`,
                purchase_order_name: planDetails ? 
                    `Subscription: ${planDetails.name}` : 
                    product_name || "BarBadhu Payment",
                customer_info: {
                    name: "Customer",
                    email: "<EMAIL>",
                    phone: mobile || "9800000000"
                },  
                product_details: [
                    {
                        identity: product_identity || `PRODUCT_${Date.now()}`,
                        name: planDetails ? planDetails.name : (product_name || "BarBadhu Service"),
                        total_price:  amount * 100,
                        quantity: 1,
                        unit_price:  amount * 100
                    }
                ]
            };

            // Make request to Khalti API
            console.log('khalti_api_url: ', khalti_api_url);
            console.log('khalti_secret_key: ', khalti_secret_key);
            const response = await axios.post(
                `${khalti_api_url}/epayment/initiate/`,
                paymentData,
                {
                    headers: {
                        'Authorization': `Key ${khalti_secret_key}`,
                        'Content-Type': 'application/json',
                    },
                }
            );

            // Save payment record to database
            const paymentRecord = await KhaltiPayment.create({
                user_id,
                plan_id,
                pidx: response.data.pidx,
                khalti_order_id: paymentData.purchase_order_id,
                amount:  amount,
                currency: "NPR",
                status: "pending",
                payment_method: "khalti",
                description: paymentData.purchase_order_name,
                khalti_response: response.data,
                mobile,
                product_identity: paymentData.product_details[0].identity,
                product_name: paymentData.product_details[0].name,
                product_url,
                return_url: paymentData.return_url,
                website_url: paymentData.website_url,
            });

            return {
                ...response.data,
                payment_id: paymentRecord.id,
                order_id: paymentData.purchase_order_id,
            };
        } catch (error: any) {
            console.error('Khalti initiate payment error:', error);
            throw new ApiError(
                httpStatus.BAD_REQUEST, 
                error.response?.data?.detail || error.response?.data?.message || error.message
            );
        }
    };

    // Verify payment status
    static verifyPayment = async (pidx: string, user_id: number) => {
        try {
            // Find the payment record
            const paymentRecord = await KhaltiPayment.findOne({
                where: { pidx, user_id }
            });

            if (!paymentRecord) {
                throw new ApiError(httpStatus.NOT_FOUND, "Payment record not found");
            }

            // Verify payment with Khalti
            const response = await axios.post(
                `${khalti_api_url}/epayment/lookup/`,
                { pidx },
                {
                    headers: {
                        'Authorization': `Key ${khalti_secret_key}`,
                        'Content-Type': 'application/json',
                    },
                }
            );

            const paymentStatus = response.data.status;
            let status: "pending" | "completed" | "failed" | "cancelled" | "refunded" | "expired" = "pending";

            switch (paymentStatus) {
                case "Completed":
                    status = "completed";
                    break;
                case "Pending":
                    status = "pending";
                    break;
                case "Refunded":
                    status = "refunded";
                    break;
                case "Canceled":
                    status = "cancelled";
                    break;
                case "Expired":
                    status = "expired";
                    break;
                default:
                    status = "failed";
            }

            // Update payment record
            await paymentRecord.update({
                status,
                khalti_transaction_id: response.data.transaction_id,
                khalti_payment_id: response.data.pidx,
                khalti_response: response.data,
            });

            // Activate subscription if payment is completed and plan_id exists
            if (status === "completed" && paymentRecord.plan_id) {
                await this.activateUserSubscription(paymentRecord.user_id, paymentRecord.plan_id);
            }

            return {
                ...response.data,
                payment_id: paymentRecord.id,
                local_status: status,
            };
        } catch (error: any) {
            console.error('Khalti verify payment error:', error);
            throw new ApiError(
                httpStatus.BAD_REQUEST, 
                error.response?.data?.detail || error.response?.data?.message || error.message
            );
        }
    };

    // Process webhook
    static processWebhook = async (webhookData: any, signature: string) => {
        try {
            // Verify webhook signature
            const isValid = this.verifyWebhookSignature(webhookData, signature);
            if (!isValid) {
                throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid webhook signature");
            }

            // Save webhook event
            const webhookEvent = await KhaltiWebhookEvent.create({
                event_id: webhookData.event_id || `WEBHOOK_${Date.now()}`,
                event_type: webhookData.type || "payment.status.changed",
                resource_type: "payment",
                resource_id: webhookData.data?.pidx,
                summary: `Khalti webhook: ${webhookData.type}`,
                event_data: webhookData,
                processed: false,
            });

            // Process the webhook based on event type
            try {
                switch (webhookData.type) {
                    case "payment.completed":
                        await this.handlePaymentCompleted(webhookData);
                        break;
                    case "payment.failed":
                        await this.handlePaymentFailed(webhookData);
                        break;
                    case "payment.refunded":
                        await this.handlePaymentRefunded(webhookData);
                        break;
                    default:
                        console.log(`Unhandled webhook event type: ${webhookData.type}`);
                }

                // Mark webhook as processed
                await webhookEvent.update({
                    processed: true,
                    processed_at: new Date(),
                });

            } catch (processingError: any) {
                // Mark webhook as failed
                await webhookEvent.update({
                    processed: false,
                    error_message: processingError.message,
                });
                throw processingError;
            }

            return { success: true, webhook_id: webhookEvent.id };
        } catch (error: any) {
            console.error('Khalti webhook processing error:', error);
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    // Verify webhook signature
    static verifyWebhookSignature = (payload: any, signature: string): boolean => {
        try {
            const payloadString = JSON.stringify(payload);
            const expectedSignature = crypto
                .createHmac('sha256', khalti_secret_key)
                .update(payloadString)
                .digest('hex');
            
            return crypto.timingSafeEqual(
                Buffer.from(signature),
                Buffer.from(expectedSignature)
            );
        } catch (error) {
            console.error('Webhook signature verification error:', error);
            return false;
        }
    };

    // Get payment by ID
    static getPaymentById = async (payment_id: number, user_id: number) => {
        try {
            const payment = await KhaltiPayment.findOne({
                where: { id: payment_id, user_id },
                include: [
                    {
                        model: User,
                        as: 'user',
                        attributes: ['id', 'first_name', 'last_name', 'email']
                    },
                    {
                        model: SubscriptionPlan,
                        as: 'subscriptionPlan',
                        attributes: ['id', 'name', 'price', 'duration_days']
                    }
                ]
            });

            if (!payment) {
                throw new ApiError(httpStatus.NOT_FOUND, "Payment not found");
            }

            return payment;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    // Get user payments with pagination
    static getUserPayments = async (user_id: number, filters: PaymentFilters) => {
        try {
            const { page, limit, status } = filters;
            const offset = (page - 1) * limit;

            const whereCondition: any = { user_id };
            if (status) {
                whereCondition.status = status;
            }

            const { count, rows } = await KhaltiPayment.findAndCountAll({
                where: whereCondition,
                include: [
                    {
                        model: SubscriptionPlan,
                        as: 'subscriptionPlan',
                        attributes: ['id', 'name', 'price', 'duration_days']
                    }
                ],
                order: [["created_at", "DESC"]],
                limit,
                offset,
            });

            return {
                payments: rows,
                pagination: {
                    total: count,
                    page,
                    limit,
                    totalPages: Math.ceil(count / limit),
                },
            };
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    // Get all transactions (admin)
    static getTransactions = async (filters: PaymentFilters & { search?: string }) => {
        try {
            const { page, limit, status, search } = filters;
            const offset = (page - 1) * limit;

            const whereCondition: any = {};
            if (status) {
                whereCondition.status = status;
            }

            const queryOption: any = {
                where: whereCondition,
                include: [
                    {
                        model: User,
                        as: 'user',
                        attributes: ['id', 'first_name', 'last_name', 'email']
                    },
                    {
                        model: SubscriptionPlan,
                        as: 'subscriptionPlan',
                        attributes: ['id', 'name', 'price', 'duration_days']
                    }
                ],
                order: [["created_at", "DESC"]],
                limit,
                offset,
            };

            if (search) {
                queryOption.include[0].where = {
                    [Op.or]: [
                        { first_name: { [Op.like]: `%${search}%` } },
                        { last_name: { [Op.like]: `%${search}%` } },
                        { email: { [Op.like]: `%${search}%` } }
                    ]
                };
            }

            const { count, rows } = await KhaltiPayment.findAndCountAll(queryOption);

            return {
                transactions: rows,
                pagination: {
                    total: count,
                    page,
                    limit,
                    totalPages: Math.ceil(count / limit),
                },
            };
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    // Get payment analytics
    static getAnalytics = async (params: AnalyticsParams) => {
        try {
            const { start_date, end_date, user_id } = params;

            const whereCondition: any = {};
            if (start_date && end_date) {
                whereCondition.created_at = {
                    [Op.between]: [new Date(start_date), new Date(end_date)]
                };
            }
            if (user_id) {
                whereCondition.user_id = user_id;
            }

            // Total payments
            const totalPayments = await KhaltiPayment.count({ where: whereCondition });

            // Total amount
            const totalAmountResult :any = await KhaltiPayment.findOne({
                where: whereCondition,
                attributes: [[Sequelize.fn('SUM', Sequelize.col('amount')), 'total']],
                raw: true,
            });

            // Status breakdown
            const statusBreakdown = await KhaltiPayment.findAll({
                where: whereCondition,
                attributes: [
                    'status',
                    [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
                    [Sequelize.fn('SUM', Sequelize.col('amount')), 'amount']
                ],
                group: ['status'],
                raw: true,
            });

            // Monthly breakdown (if date range is provided)
            let monthlyBreakdown:any[] = [];
            if (start_date && end_date) {
                monthlyBreakdown = await KhaltiPayment.findAll({
                    where: whereCondition,
                    attributes: [
                        [Sequelize.fn('DATE_FORMAT', Sequelize.col('created_at'), '%Y-%m'), 'month'],
                        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
                        [Sequelize.fn('SUM', Sequelize.col('amount')), 'amount']
                    ],
                    group: [Sequelize.fn('DATE_FORMAT', Sequelize.col('created_at'), '%Y-%m')],
                    order: [[Sequelize.fn('DATE_FORMAT', Sequelize.col('created_at'), '%Y-%m'), 'ASC']],
                    raw: true,
                });
            }

            return {
                totalPayments,
                totalAmount: totalAmountResult?.total || 0,
                statusBreakdown,
                monthlyBreakdown,
            };
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    // Helper methods for webhook processing
    static handlePaymentCompleted = async (webhookEvent: any) => {
        try {
            const pidx = webhookEvent.data?.pidx;
            if (pidx) {
                const paymentRecord = await KhaltiPayment.findOne({
                    where: { pidx }
                });

                if (paymentRecord) {
                    await paymentRecord.update({
                        status: "completed",
                        khalti_transaction_id: webhookEvent.data?.transaction_id,
                        khalti_payment_id: webhookEvent.data?.pidx,
                        khalti_response: webhookEvent.data,
                    });

                    // Activate subscription if applicable
                    if (paymentRecord.plan_id) {
                        await this.activateUserSubscription(paymentRecord.user_id, paymentRecord.plan_id);
                    }
                }
            }
        } catch (error: any) {
            console.error('Handle payment completed error:', error);
        }
    };

    static handlePaymentFailed = async (webhookEvent: any) => {
        try {
            const pidx = webhookEvent.data?.pidx;
            if (pidx) {
                const paymentRecord = await KhaltiPayment.findOne({
                    where: { pidx }
                });

                if (paymentRecord) {
                    await paymentRecord.update({
                        status: "failed",
                        khalti_response: webhookEvent.data,
                    });
                }
            }
        } catch (error: any) {
            console.error('Handle payment failed error:', error);
        }
    };

    static handlePaymentRefunded = async (webhookEvent: any) => {
        try {
            const pidx = webhookEvent.data?.pidx;
            if (pidx) {
                const paymentRecord = await KhaltiPayment.findOne({
                    where: { pidx }
                });

                if (paymentRecord) {
                    await paymentRecord.update({
                        status: "refunded",
                        refund_id: webhookEvent.data?.refund_id,
                        refund_amount: webhookEvent.data?.refund_amount ? webhookEvent.data.refund_amount / 100 : 0,
                        khalti_response: webhookEvent.data,
                    });

                    // Deactivate subscription if applicable
                    if (paymentRecord.plan_id) {
                        await UserSubscription.update(
                            { is_active: false, revoked_at: new Date() },
                            { where: { user_id: paymentRecord.user_id, plan_id: paymentRecord.plan_id, is_active: true } }
                        );
                    }
                }
            }
        } catch (error: any) {
            console.error('Handle payment refunded error:', error);
        }
    };

    // Activate user subscription
    static activateUserSubscription = async (user_id: number, plan_id: number) => {
        try {
            const plan = await SubscriptionPlan.findByPk(plan_id);
            if (!plan) {
                throw new ApiError(httpStatus.NOT_FOUND, "Subscription plan not found");
            }

            const startDate = new Date();
            const endDate = new Date();
            endDate.setDate(startDate.getDate() + plan.duration_days);

            // Deactivate any existing active subscriptions
            await UserSubscription.update(
                { is_active: false, revoked_at: new Date() },
                { where: { user_id, is_active: true } }
            );

            // Create new subscription
            const subscription = await UserSubscription.create({
                user_id,
                plan_id,
                payment_status: "completed",
                start_date: startDate,
                end_date: endDate,
                auto_renew: false,
                issued_at: startDate,
                expires_at: endDate,
                interest_sent_count: 0,
                contact_viewed_count: 0,
                profile_viewed_count: 0,
                chat_initiated_count: 0,
                is_active: true,
                token: `SUB_${Date.now()}_${user_id}`,
            });

            return subscription;
        } catch (error: any) {
            console.error('Activate user subscription error:', error);
            throw error;
        }
    };
}
