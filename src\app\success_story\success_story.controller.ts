import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";
import ApiError from "../../utils/ApiError";
import httpMessages from "../../config/httpMessages";
import SuccessStoryService from "./success_story.service";
import SuccessStory from "../../database/models/success_story.model";
import User from "../../database/models/user.model";
import SuccessStoryMedia from "../../database/models/success_story_media.model";

export default class SuccessStoryController {
    static successStoryService = SuccessStoryService;
    constructor() { }

    static getAll = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page, limit, search, status } = request.query;
            const option = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
                search: search ? (search as string) : "",
                status: status ? (status as string) : undefined,
            };
            const list = await this.successStoryService.getSuccessStories(option);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Success stories retrieved successfully",
                data: list,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static getApproved = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page, limit } = request.query;
            const option = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
            };
            const list = await this.successStoryService.getApprovedSuccessStories(option);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Approved success stories retrieved successfully",
                data: list,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static getUserStories = catchAsync(async (request: Request, response: Response) => {
        try {
            const userId = request.decoded;
            const { page, limit, search, status } = request.query;
            const option = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
                search: search ? (search as string) : "",
                status: status ? (status as string) : undefined,
            };
            const list = await this.successStoryService.getUserSuccessStories(userId, option);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "User success stories retrieved successfully",
                data: list,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static create = catchAsync(async (request: Request, response: Response) => {
        try {
            const userId = request.decoded;
            const body = { ...request.body, user_id: userId };

            // Handle media files
            const mediaFiles = [];
            if (request.body.media_files) {
                if (Array.isArray(request.body.media_files)) {
                    mediaFiles.push(...request.body.media_files);
                } else {
                    mediaFiles.push(request.body.media_files);
                }
            }

            const createdData = await this.successStoryService.createSuccessStory(body, mediaFiles);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Success story submitted successfully",
                data: createdData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static showById = catchAsync(async (request: Request, response: Response) => {
        try {
            let { id }: any = request.params;
            if (id === "show") {
                const userId = request.decoded;
                const successStory = await SuccessStory.findOne(
                    {
                        where: { user_id: userId },
                        include: [
                            {
                                model: User,
                                attributes: ["id", "first_name", "last_name", "email"],
                            },
                            {
                                model: SuccessStoryMedia,
                                attributes: ["id", "media_type", "media_path"],
                            },
                        ],
                    });

                return sentResponse(response, {
                    statusCode: httpStatus.OK,
                    message: "Success story retrieved successfully",
                    data: successStory,
                });
            }
            const data = await this.successStoryService.getSuccessStoryById(id);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Success story retrieved successfully",
                data: data,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static updateStatus = catchAsync(async (request: Request, response: Response) => {
        try {
            const id: number = parseInt(request.params.id, 10);
            const body = { ...request.body };

            const updatedData = await this.successStoryService.updateSuccessStoryStatus(id, body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: `Success story ${body.status} successfully`,
                data: updatedData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static delete = catchAsync(async (request: Request, response: Response) => {
        try {
            const id: number = parseInt(request.params.id, 10);
            await this.successStoryService.deleteSuccessStory(id);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Success story deleted successfully",
                data: { id },
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });
}
