import express from "express";
import SubscriptionController from "../app/subscription/subscription.controller";
import { auth } from "../middlewares/auth";
import { validate } from "../middlewares/middleware";
import {
    purchaseSubscriptionValidation,
    trackUsageValidation,
    canPerformActionValidation,
    getSubscriptionByIdValidation,
    cancelSubscriptionValidation
} from "../validations/subscription.validation";

const router = express.Router();

// All routes require authentication
router.use(auth);

// Purchase subscription
router.post("/purchase", validate(purchaseSubscriptionValidation), SubscriptionController.purchaseSubscription);

// Track usage
router.post("/track-usage", validate(trackUsageValidation), SubscriptionController.trackUsage);

// Get subscription status
router.get("/status", SubscriptionController.getSubscriptionStatus);

// Check if user can perform action
router.get("/can-perform", validate(canPerformActionValidation), SubscriptionController.canPerformAction);

// Get subscription history
router.get("/history", SubscriptionController.getSubscriptionHistory);

// Get subscription by ID
router.get("/:subscription_id", validate(getSubscriptionByIdValidation), SubscriptionController.getSubscriptionById);

// Cancel subscription
router.post("/cancel", validate(cancelSubscriptionValidation), SubscriptionController.cancelSubscription);

// Admin routes (you may want to add admin role check middleware)
router.post("/admin/check-expired", SubscriptionController.checkExpiredSubscriptions);

export default router;
