import {
    Table,
    Column,
    Model,
    DataType,
    PrimaryKey,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    Index,
    Default,
} from "sequelize-typescript";
import User from "./user.model";

interface UserVerificationI {
    id: number;
    user_id: number;

    is_profile_completed?: boolean;

    phone: string;
    phone_code: string;
    phone_otp: string;
    phone_otp_expiry: Date | null;
    is_phone_verified?: boolean;

    email: string;
    email_otp: string;
    email_otp_expiry: Date | null;
    is_email_verified?: boolean;

    identity_proof_type: string;
    identity_proof_front: string;
    identity_proof_back: string;
    is_identity_verified?: boolean;

    identity_proof_type1: string;
    identity_proof_front1: string;
    identity_proof_back1: string;
    is_identity_verified1?: boolean;

    is_facebook_verified?: boolean;
}

@Table({
    tableName: "user_verifications",
    timestamps: false,
})
class UserVerification extends Model<UserVerificationI> implements UserVerificationI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    @AllowNull(true)
    @Default(false)
    @Column(DataType.BOOLEAN)
    is_profile_completed?: boolean;

    @AllowNull(true)
    @Column(DataType.STRING(15))
    phone: string;

    @AllowNull(true)
    @Column(DataType.STRING(128))
    phone_code: string;

    @AllowNull(true)
    @Column(DataType.STRING(255))
    phone_otp: string;

    @AllowNull(true)
    @Column(DataType.DATE)
    phone_otp_expiry: Date | null;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    email: string;

    @AllowNull(true)
    @Column(DataType.STRING(255))
    email_otp: string;

    @AllowNull(true)
    @Column(DataType.DATE)
    email_otp_expiry: Date | null;

    @AllowNull(true)
    @Default(false)
    @Column(DataType.BOOLEAN)
    is_email_verified?: boolean;

    @AllowNull(true)
    @Default(false)
    @Column(DataType.BOOLEAN)
    is_phone_verified?: boolean;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    identity_proof_type: string;

    @AllowNull(true)
    @Column(DataType.STRING(255))
    identity_proof_front: string;

    @AllowNull(true)
    @Column(DataType.STRING(255))
    identity_proof_back: string;

    @AllowNull(true)
    @Default(false)
    @Column(DataType.BOOLEAN)
    is_identity_verified?: boolean;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    identity_proof_type1: string;

    @AllowNull(true)
    @Column(DataType.STRING(255))
    identity_proof_front1: string;
    
    @AllowNull(true)
    @Column(DataType.STRING(255))
    identity_proof_back1: string;
    
    @AllowNull(true)
    @Default(false)
    @Column(DataType.BOOLEAN)
    is_identity_verified1?: boolean;

    @AllowNull(true)
    @Default(false)
    @Column(DataType.BOOLEAN)
    is_facebook_verified?: boolean;
}

export default UserVerification;
