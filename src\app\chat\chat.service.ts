import httpStatus from "http-status";
import { Op } from "sequelize";
import ApiError from "../../utils/ApiError";
import httpMessages from "../../config/httpMessages";
import Chat from "../../database/models/chat.model";
import Message from "../../database/models/message.model";
import User from "../../database/models/user.model";
import { encryptData } from "../../middlewares/EncryptionResponse";
import sequelize from "sequelize";

class ChatService {
  /**
   * Create a new chat between two users
   * @param {number} user1Id - First user ID
   * @param {number} user2Id - Second user ID
   * @returns {Promise<Chat>}
   */
  static createChat = async (user1Id: number, user2Id: number) => {
    // Check if chat already exists
    try {
      const existingChat = await Chat.findOne({
        where: {
          [Op.or]: [
            { user1_id: user1Id, user2_id: user2Id },
            { user1_id: user2Id, user2_id: user1Id }
          ]
        }
      });

      if (existingChat) {
        return existingChat;
      }

      const chat = await Chat.create({
        user1_id: user1Id,
        user2_id: user2Id,
        is_active: true,
        last_message_at: new Date(),
        auto_delete_days: null
      });
      // Create new chat
      return await this.getChatById(chat.id,user1Id);
    } catch (error: any) {
      console.log('error: ', error);
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }

  };

  /**
   * Get chat by ID
   * @param {number} chatId - Chat ID
   * @returns {Promise<Chat>}
   */
  static getChatById = async (chatId: number,userId: number) => {
    const chat = await Chat.findByPk(chatId, {
      include: [
        {
          model: User,
          as: 'user1',
          attributes: ['id', 'first_name', 'last_name', 'email','profile_image','member_id'],
        },
        {
          model: User,
          as: 'user2',
          attributes: ['id', 'first_name', 'last_name', 'email','profile_image','member_id'],
        },
        {
          model: Message,
          separate: true,
          order: [['createdAt', 'ASC']],
        },
      ],
       attributes: {
          include: [
            [
              sequelize.literal(`CASE 
              WHEN user1_id = ${userId} THEN 'user2'
              ELSE 'user1' 
            END`),
              'other_user'
            ]
          ]
        },
    });

    if (!chat) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Chat not found');
    }

    return chat;
  };

  /**
   * Get all chats for a user
   * @param {number} userId - User ID
   * @returns {Promise<Chat[]>}
   */
  static getUserChats = async (userId: number) => {
    try {
    const chats = await Chat.findAll({
      where: {
        [Op.or]: [
          { user1_id: userId },
          { user2_id: userId }
        ],
        is_active: true
      },
      include: [
        {
          model: User,
          as: 'user1',
          attributes: ['id', 'first_name', 'last_name', 'email','profile_image','member_id'],
         
        },
        {
          model: User,
          as: 'user2',
          attributes: ['id', 'first_name', 'last_name', 'email','profile_image','member_id'],
        },
        {
          model: Message,
          limit: 1,
          order: [['createdAt', 'DESC']],
        }
      ],
      attributes: {
        include: [
          [
            sequelize.literal(`CASE 
              WHEN user1_id = ${userId} THEN 'user2'
              ELSE 'user1' 
            END`),
            'other_user'
          ]
        ]
      },
      order: [['last_message_at', 'DESC']]
    });

    return chats;
    } catch (error: any) {
      console.log('error: ', error);
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Set auto-delete days for a chat
   * @param {number} chatId - Chat ID
   * @param {number} days - Number of days after which messages will be auto-deleted
   * @returns {Promise<Chat>}
   */
  static setAutoDeleteDays = async (chatId: number, days: number) => {
    const chat = await Chat.findByPk(chatId);

    if (!chat) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Chat not found');
    }

    await chat.update({ auto_delete_days: days });
    return chat;
  };

  /**
   * Delete a chat (soft delete by setting is_active to false)
   * @param {number} chatId - Chat ID
   * @param {number} userId - User ID requesting the deletion
   * @returns {Promise<boolean>}
   */
  static deleteChat = async (chatId: number, userId: number) => {
    const chat = await Chat.findByPk(chatId);

    if (!chat) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Chat not found');
    }

    // Check if user is part of the chat
    if (chat.user1_id !== userId && chat.user2_id !== userId) {
      throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to delete this chat');
    }

    await chat.update({ is_active: false });
    return true;
  };
}

export default ChatService;
