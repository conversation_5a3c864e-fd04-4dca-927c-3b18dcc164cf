"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const sequelize_1 = require("sequelize");
const modules_model_1 = __importDefault(require("../../../database/models/modules.model"));
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const role_permissions_model_1 = __importDefault(require("../../../database/models/role_permissions.model"));
const role_model_1 = __importDefault(require("../../../database/models/role.model"));
const httpMessages_1 = __importDefault(require("../../../config/httpMessages"));
class ModuleService {
    constructor() { }
}
_a = ModuleService;
/**
 * Get module by id
 * @param {Number} id
 * @returns {Promise<Module>}
 */
ModuleService.getModuleById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return yield modules_model_1.default.findOne({
            where: { id },
            include: [
                {
                    model: modules_model_1.default,
                    as: "parent",
                    attributes: ["id", "name"],
                },
            ],
        });
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get modules
 * @param {Object} options
 * @param {number} [options.page] - Current page number (optional)
 * @param {number} [options.limit] - Number of items per page (optional)
 * @param {string} [options.search] - Search term for filtering (optional)
 * @returns {Promise<Module[]>}
 */
ModuleService.getModules = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search } = options;
        const whereCondition = search
            ? {
                [sequelize_1.Op.or]: [
                    { name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                    { route: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                ],
            }
            : {};
        whereCondition.parent_id = null;
        whereCondition.is_active = true;
        const queryOption = {
            where: whereCondition,
            attributes: ["id", "name", "route", "icon", "sort_order", "is_active"],
            include: [
                {
                    model: modules_model_1.default,
                    as: "subModules",
                    required: false,
                    where: { is_active: true },
                    attributes: ["id", "name", "route", "icon", "sort_order", "is_active"],
                },
            ],
            order: [["sort_order", "ASC"]],
        };
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const modules = yield modules_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: modules.count,
                totalPages: Math.ceil(modules.count / limit),
                currentPage: page,
                modules: modules.rows,
            };
        }
        else {
            return modules.rows;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Create a Module
 * @param {Object} moduleBody
 * @returns {Promise<Module>}
 */
ModuleService.createModule = (moduleBody) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (yield _a.getModuleByRoute(moduleBody.route)) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.MODULES.ROUTE_ALREADY_TAKEN);
        }
        if (!moduleBody.parent_id) {
            const maxsortorder = yield modules_model_1.default.findOne({
                attributes: [
                    [sequelize_1.Sequelize.fn("MAX", sequelize_1.Sequelize.col("sort_order")), "maxSortOrder"],
                ],
                where: { parent_id: null },
                raw: true,
            });
            moduleBody.sort_order = (maxsortorder === null || maxsortorder === void 0 ? void 0 : maxsortorder.maxSortOrder)
                ? maxsortorder.maxSortOrder + 1
                : 1;
        }
        else {
            const parentModule = yield modules_model_1.default.findByPk(moduleBody.parent_id);
            const maxsortorder = yield modules_model_1.default.findOne({
                attributes: [
                    [sequelize_1.Sequelize.fn("MAX", sequelize_1.Sequelize.col("sort_order")), "maxSortOrder"],
                ],
                where: { parent_id: parentModule.id },
                raw: true,
            });
            moduleBody.sort_order = (maxsortorder === null || maxsortorder === void 0 ? void 0 : maxsortorder.maxSortOrder)
                ? maxsortorder.maxSortOrder + 1
                : 1;
        }
        const module = yield modules_model_1.default.create(moduleBody);
        const rolePermissions = yield role_model_1.default.findAll();
        const rolePermissionsData = rolePermissions.map((role) => {
            return {
                role_id: role.id,
                module_id: module.id,
            };
        });
        yield role_permissions_model_1.default.bulkCreate(rolePermissionsData);
        return module;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get module by route
 * @param {string} route
 * @returns {Promise<Module>}
 */
ModuleService.getModuleByRoute = (route) => __awaiter(void 0, void 0, void 0, function* () {
    return modules_model_1.default.findOne({
        where: { route },
    });
});
/**
 * Update module by id
 * @param {Number} moduleId
 * @param {Object} updateBody
 * @returns {Promise<Module>}
 */
ModuleService.updateModuleById = (moduleId, updateBody) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const module = yield modules_model_1.default.findByPk(moduleId);
        if (!module) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.MODULES.NOT_FOUND);
        }
        Object.assign(module, updateBody);
        yield module.save();
        return module;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Delete module by id
 * @param {Number} moduleId
 * @returns {Promise<Module>}
 */
ModuleService.deleteModuleById = (moduleId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const module = yield modules_model_1.default.findByPk(moduleId);
        if (!module) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.MODULES.NOT_FOUND);
        }
        yield module.destroy();
        return module;
    }
    catch (error) {
        if (error.name === "SequelizeForeignKeyConstraintError") {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Cannot delete this Record as it is referenced in another table.");
        }
        else {
            throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting Module.");
        }
    }
});
exports.default = ModuleService;
