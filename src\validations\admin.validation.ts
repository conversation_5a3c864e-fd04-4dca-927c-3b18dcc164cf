import Joi from "joi";

export const adminloginValidation = {
  body: Joi.object().keys({
    username: Joi.string().trim().required().messages({
      "string.empty": "User Name is required.",
      "string.base": "User Name must be a string.",
    }),
    password: Joi.string().trim().required().messages({
      "string.empty": "Password is required.",
      "string.base": "Password must be a string.",
    }),
  }),
};

export const createAdminValidation = {
  body: Joi.object().keys({
    first_name: Joi.string().trim().required().messages({
      "string.empty": "First name is required.",
      "string.base": "First name must be a string.",
    }),
    last_name: Joi.string().trim().required().messages({
      "string.empty": "Last name is required.",
      "string.base": "Last name must be a string.",
    }),
    email: Joi.string().trim().email().required().messages({
      "string.empty": "Email is required.",
      "string.email": "Email must be a valid email address.",
    }),
    mobile_number: Joi.string()
      .trim()
      .pattern(/^[0-9]{7,20}$/)
      .required()
      .messages({
        "string.empty": "Mobile number is required.",
        "string.pattern.base":
          "Mobile number must be numeric and between 7 to 20 digits.",
      }),
    username: Joi.string().trim().required().messages({
      "string.empty": "Username is required.",
      "string.base": "Username must be a string.",
    }),
    password: Joi.string().trim().min(6).required().messages({
      "string.empty": "Password is required.",
      "string.min": "Password must be at least 6 characters long.",
    }),

    role_id: Joi.number().required().messages({
      "number.base": "ID must be a number.",
      "any.required": "ID is required.",
    }),
    is_super_admin: Joi.boolean().optional().allow(null, ""),
  }),
};

export const updateAdminValidation = {
  body: Joi.object().keys({
    first_name: Joi.string().trim().required().messages({
      "string.empty": "First name is required.",
      "string.base": "First name must be a string.",
    }),
    last_name: Joi.string().trim().required().messages({
      "string.empty": "Last name is required.",
      "string.base": "Last name must be a string.",
    }),
    email: Joi.string().trim().email().required().messages({
      "string.empty": "Email is required.",
      "string.email": "Email must be a valid email address.",
    }),
    mobile_number: Joi.string()
      .trim()
      .pattern(/^[0-9]{7,20}$/)
      .required()
      .messages({
        "string.empty": "Mobile number is required.",
        "string.pattern.base":
          "Mobile number must be numeric and between 7 to 20 digits.",
      }),
    username: Joi.string().trim().required().messages({
      "string.empty": "Username is required.",
      "string.base": "Username must be a string.",
    }),
    password: Joi.string().trim().min(6).optional().messages({
      "string.empty": "Password is required.",
      "string.min": "Password must be at least 6 characters long.",
    }),
    role_id: Joi.number().required().messages({
      "number.base": "ID must be a number.",
      "any.required": "ID is required.",
    }),
    permissions: Joi.object()
      .pattern(
        Joi.string(),
        Joi.object().pattern(Joi.string(), Joi.boolean()).unknown(true)
      )
      .optional()
      .allow(null)
      .messages({
        "object.base": "Permissions must be an object with proper structure.",
      }),
    is_super_admin: Joi.boolean().optional().allow(null, ""),
  }),
};



//  permissions: Joi.object()
//       .pattern(
//         Joi.string(),
//         Joi.object().pattern(Joi.string(), Joi.boolean()).unknown(true)
//       )
//       .optional()
//       .allow(null)
//       .messages({
//         "object.base": "Permissions must be an object with proper structure.",
//       }),