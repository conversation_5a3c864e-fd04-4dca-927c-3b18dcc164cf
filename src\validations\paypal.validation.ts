import Joi from "joi";

export const createOrderValidation = {
  body: Joi.object().keys({
    plan_id: Joi.number().integer().positive().optional().messages({
      "number.base": "Plan ID must be a number.",
      "number.integer": "Plan ID must be an integer.",
      "number.positive": "Plan ID must be a positive number.",
    }),
    amount: Joi.number().positive().precision(2).required().messages({
      "number.base": "Amount must be a number.",
      "number.positive": "Amount must be a positive number.",
      "any.required": "Amount is required.",
    }),
    currency: Joi.string().length(3).uppercase().default("USD").messages({
      "string.base": "Currency must be a string.",
      "string.length": "Currency must be exactly 3 characters.",
    }),
  }),
};

export const capturePaymentValidation = {
  params: Joi.object().keys({
    order_id: Joi.string().required().messages({
      "string.base": "Order ID must be a string.",
      "string.empty": "Order ID is required.",
      "any.required": "Order ID is required.",
    }),
  }),
};

export const getPaymentDetailsValidation = {
  params: Joi.object().keys({
    payment_id: Joi.string().required().messages({
      "string.base": "Payment ID must be a string.",
      "string.empty": "Payment ID is required.",
      "any.required": "Payment ID is required.",
    }),
  }),
};

export const getUserPaymentsValidation = {
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).default(1).messages({
      "number.base": "Page must be a number.",
      "number.integer": "Page must be an integer.",
      "number.min": "Page must be at least 1.",
    }),
    limit: Joi.number().integer().min(1).max(100).default(10).messages({
      "number.base": "Limit must be a number.",
      "number.integer": "Limit must be an integer.",
      "number.min": "Limit must be at least 1.",
      "number.max": "Limit cannot exceed 100.",
    }),
    status: Joi.string().valid("pending", "completed", "failed", "cancelled", "refunded").optional().messages({
      "string.base": "Status must be a string.",
      "any.only": "Status must be one of: pending, completed, failed, cancelled, refunded.",
    }),
    search: Joi.string().optional().messages({
      "string.base": "Search must be a string.",
    }),
  }),
};

export const getPaymentAnalyticsValidation = {
  query: Joi.object().keys({
    start_date: Joi.date().iso().optional().messages({
      "date.base": "Start date must be a valid date.",
      "date.format": "Start date must be in ISO format.",
    }),
    end_date: Joi.date().iso().min(Joi.ref('start_date')).optional().messages({
      "date.base": "End date must be a valid date.",
      "date.format": "End date must be in ISO format.",
      "date.min": "End date must be after start date.",
    }),
    user_id: Joi.number().integer().positive().optional().messages({
      "number.base": "User ID must be a number.",
      "number.integer": "User ID must be an integer.",
      "number.positive": "User ID must be a positive number.",
    }),
  }),
};

export const refundPaymentValidation = {
  params: Joi.object().keys({
    payment_id: Joi.string().required().messages({
      "string.base": "Payment ID must be a string.",
      "string.empty": "Payment ID is required.",
      "any.required": "Payment ID is required.",
    }),
  }),
  body: Joi.object().keys({
    amount: Joi.number().positive().precision(2).optional().messages({
      "number.base": "Amount must be a number.",
      "number.positive": "Amount must be a positive number.",
    }),
    reason: Joi.string().max(255).optional().messages({
      "string.base": "Reason must be a string.",
      "string.max": "Reason cannot exceed 255 characters.",
    }),
  }),
};

export const webhookValidation = {
  body: Joi.object().required().messages({
    "object.base": "Webhook body must be an object.",
    "any.required": "Webhook body is required.",
  }),
};
