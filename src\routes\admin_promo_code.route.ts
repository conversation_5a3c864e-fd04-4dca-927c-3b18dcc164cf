import express from "express";
import AdminPromoCodeController from "../app/admin/admin_promo_code.controller";
import { auth } from "../middlewares/auth";
import { validate } from "../middlewares/middleware";
import {
    createPromoCodeValidation,
    updatePromoCodeValidation,
    deletePromoCodeValidation,
    getPromoCodeValidation
} from "../validations/promo_code.validation";

const router = express.Router();

// All routes require authentication (admin only)
router.use(auth);   

// Create promo code
router.post("/", validate(createPromoCodeValidation), AdminPromoCodeController.createPromoCode);

// Get all promo codes with pagination
router.get("/", AdminPromoCodeController.getPromoCodes);

// Get promo code by ID
router.get("/:id", validate(getPromoCodeValidation), AdminPromoCodeController.getPromoCodeById);

// Update promo code
router.put("/:id",  AdminPromoCodeController.updatePromoCode);

// Delete promo code
router.delete("/:id", validate(deletePromoCodeValidation), AdminPromoCodeController.deletePromoCode);

// Get promo code statistics
router.get("/:id/stats", validate(getPromoCodeValidation), AdminPromoCodeController.getPromoCodeStats);

export default router;
