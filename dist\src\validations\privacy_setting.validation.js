"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.privacySettingValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.privacySettingValidation = {
    body: joi_1.default.object().keys({
        alert_matches_frequency: joi_1.default.string().valid('Daily', 'Weekly', 'Unsubscribe').allow(null).messages({
            "string.base": "Alert matches frequency must be a string.",
            "any.only": "Alert matches frequency must be one of 'Daily', 'Weekly', or 'Unsubscribe'.",
        }),
        alert_membership_frequency: joi_1.default.string().valid('Daily', 'Weekly', 'Unsubscribe').allow(null).messages({
            "string.base": "Alert membership frequency must be a string.",
            "any.only": "Alert membership frequency must be one of 'Daily', 'Weekly', or 'Unsubscribe'.",
        }),
        alert_messages_frequency: joi_1.default.string().valid('Daily', 'Weekly', 'Unsubscribe').allow(null).messages({
            "string.base": "Alert messages frequency must be a string.",
            "any.only": "Alert messages frequency must be one of 'Daily', 'Weekly', or 'Unsubscribe'.",
        }),
        alert_invitations_frequency: joi_1.default.string().valid('Daily', 'Weekly', 'Unsubscribe').allow(null).messages({
            "string.base": "Alert invitations frequency must be a string.",
            "any.only": "Alert invitations frequency must be one of 'Daily', 'Weekly', or 'Unsubscribe'.",
        }),
        alert_updates_frequency: joi_1.default.string().valid('Daily', 'Weekly', 'Unsubscribe').allow(null).messages({
            "string.base": "Alert updates frequency must be a string.",
            "any.only": "Alert updates frequency must be one of 'Daily', 'Weekly', or 'Unsubscribe'.",
        }),
        alert_profile_completion_frequency: joi_1.default.string().valid('Daily', 'Weekly', 'Unsubscribe').allow(null).messages({
            "string.base": "Alert profile completion frequency must be a string.",
            "any.only": "Alert profile completion frequency must be one of 'Daily', 'Weekly', or 'Unsubscribe'.",
        }),
        profile_visibility: joi_1.default.number().integer().optional().allow(null).messages({
            "number.base": "Profile visibility must be a number.",
            "number.integer": "Profile visibility must be an integer.",
        }),
        delete_reason: joi_1.default.string().valid('found_match_barbadhu', 'found_match_elsewhere', 'taking_break', 'unhappy_experience', 'other_reasons').allow(null).messages({
            "string.base": "Delete reason must be a string.",
            "any.only": "Delete reason must be one of 'found_match_barbadhu', 'found_match_elsewhere', 'taking_break', 'unhappy_experience', or 'other_reasons'.",
        }),
        delete_feedback: joi_1.default.string().allow(null).messages({
            "string.base": "Delete feedback must be a string.",
        }),
    }),
};
