import {
    Table,
    Column,
    Model,
    DataType,
    PrimaryKey,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    Index,
} from "sequelize-typescript";
import User from "./user.model";

interface UserHobbiesI {
    id: number;
    user_id: number;
    hobbies: string;
    interests: string;
}

@Table({
    tableName: "user_hobbies",
    timestamps: false,
})
class UserHobbies extends Model<UserHobbiesI> implements UserHobbiesI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    @AllowNull(true)
    @Column(DataType.TEXT)
    hobbies: string;

    @AllowNull(true)
    @Column(DataType.TEXT)
    interests: string;
}

export default UserHobbies