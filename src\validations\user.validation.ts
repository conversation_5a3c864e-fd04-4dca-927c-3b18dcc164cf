import Joi = require("joi");
import httpMessages from "../config/httpMessages";
import { passwordValidation } from "./custom.validation";

export const userIdValidationSchema = {
  params: Joi.object({
    userId: Joi.string().required().not().empty().messages({
      "any.required": 'The "hotelId" parameter is required.',
      "string.empty": 'The "hotelId" parameter cannot be empty.',
    }),
  }),
};

export const userValidation = {
    body: Joi.object().keys({
      firstName: Joi.string().trim().required().messages({
        "string.base": "First name must be a string.",
        "string.empty": "First name is required.",
      }),
      middleName: Joi.string().optional().allow(null, "").messages({
        "string.base": "Middle name must be a string.",
      }),
      lastName: Joi.string().trim().required().messages({
        "string.base": "Last name must be a string.",
        "string.empty": "Last name is required.",
      }),
      email: Joi.string().email().trim().required().messages({
        "string.email": "Please provide a valid email address.",
        "string.empty": "Email is required.",
      }),
      password: Joi.string().required().custom(passwordValidation).messages({
        "string.empty": "Password is required.",
        "any.custom": "Password does not meet the complexity requirements.",
      }),
      confirmPassword: Joi.any().equal(Joi.ref("password")).required().messages({
        "any.only":
          httpMessages.REGISTER?.PASSWORD_MISMATCH ||
          "Passwords do not match.",
        "any.required": "Confirmation password is required.",
      }),
      gender: Joi.string()
        .valid("Male", "Female", "Other")
        .required()
        .messages({
          "any.only": "Gender must be Male, Female or Other.",
          "string.empty": "Gender is required.",
        }),
      dateOfBirth: Joi.date().iso().required().messages({
        "date.base": "Date of birth must be a valid ISO date.",
        "any.required": "Date of birth is required.",
      }),
      phone: Joi.string().required().messages({
        "string.base": "Phone must be a string.",
        "string.empty": "Phone number is required.",
      }),
      profileCreatedFor: Joi.string().optional().allow(null, ""),
      countryOfCitizenship: Joi.string().required(),
      religion: Joi.string().required(),
      caste: Joi.string().required(),
      gotra: Joi.string().optional().allow(null, ""),
      maritalStatus: Joi.string().required(),
      numberOfChildren: Joi.number().integer().optional().allow(null),
      numberOfBoys: Joi.number().integer().optional().allow(null),
      countryLivingIn: Joi.string().required(),
      city: Joi.string().required(),
      residencyStatus: Joi.string().required(),
      education: Joi.string().required(),
      employmentStatus: Joi.string().required(),
      profession: Joi.string().optional().allow(null, ""),
      workingFor: Joi.string().optional().allow(null, ""),
      height: Joi.string().optional().allow(null, ""),
      disability: Joi.string().optional().allow(null, ""),
      countryOfBirth: Joi.string().optional().allow(null, ""),
      birthCity: Joi.string().optional().allow(null, ""),
      birthTime: Joi.string().optional().allow(null, ""),
      horoscopeMatch: Joi.boolean().optional(),
      familyType: Joi.string().optional().allow(null, ""),
      fatherOccupation: Joi.string().optional().allow(null, ""),
      motherOccupation: Joi.string().optional().allow(null, ""),
      numberOfSiblings: Joi.number().integer().optional().allow(null),
      maternalSurname: Joi.string().optional().allow(null, ""),
      hisGotra: Joi.string().optional().allow(null, ""),
      profileBio: Joi.string().optional().allow(null, ""),
      profileImage: Joi.string().uri().optional().allow(null, ""),
      isPhoneVerified: Joi.boolean().optional(),
      isIdentityVerified: Joi.boolean().optional(),
      isFacebookVerified: Joi.boolean().optional(),
    }),
  };

export const updateUserValidation = {
  body: Joi.object().keys({
    first_name: Joi.string().trim().required().messages({
      "string.empty": "First name is required.",
      "string.base": "First name must be a string.",
    }),
    last_name: Joi.string().trim().required().messages({
      "string.empty": "Last name is required.",
      "string.base": "Last name must be a string.",
    }),
    email: Joi.string().trim().email().required().messages({
      "string.empty": "Email is required.",
      "string.email": "Please provide a valid email address.",
    }),
    phone_number: Joi.string().optional().allow(null, "").messages({
      "string.base": "Phone number must be a string.",
      "any.custom": "Phone number is invalid.",
    }),
    status: Joi.string().valid("active", "inactive").required().messages({
      "any.only": "Status must be either 'active' or 'inactive'.",
      "string.empty": "Status is required.",
    }),
    role_id: Joi.number().required().messages({
      "number.base": "Role ID must be a number.",
      "any.required": "Role ID is required.",
    }),
    hotel_id: Joi.number().optional().allow(null, "").messages({
      "number.base": "Hotel ID must be a number.",
    }),
    permissions: Joi.string().optional().allow(null, "").messages({
      "string.base": "Permissions must be a string.",
    }),
    profile_picture: Joi.string().optional().allow(null, "").messages({
      "string.base": "Profile picture must be a valid URL string.",
    }),
    address: Joi.string().optional().allow(null, "").messages({
      "string.base": "Address must be a string.",
    }),
    is_owner: Joi.boolean().required().messages({
      "boolean.base": "Is Admin must be a boolean value.",
      "any.required": "Is Admin field is required.",
    }),
  }),
};

export const AssignHotelValidationSchema = {
  params: Joi.object({
    userId: Joi.string().required().not().empty().messages({
      "any.required": 'The "userId" parameter is required.',
      "string.empty": 'The "userId" parameter cannot be empty.',
    }),
  }),
  body: Joi.object().keys({
    user_id: Joi.number().integer().required().messages({
      "number.base": "User ID must be a number.",
      "number.integer": "User ID must be an integer.",
      "any.required": "User ID is required.",
    }),
    hotel_id: Joi.string().required().not().empty().messages({
      "any.required": 'The "hotelId" parameter is required.',
      "string.empty": 'The "hotelId" parameter cannot be empty.',
    }),
  }),
};
