"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const user_subscriptions_model_1 = __importDefault(require("../../database/models/user_subscriptions.model"));
const subscription_plans_mode_1 = __importDefault(require("../../database/models/subscription_plans.mode"));
const user_action_log_model_1 = __importDefault(require("../../database/models/user_action_log.model"));
const paypal_payment_model_1 = __importDefault(require("../../database/models/paypal_payment.model"));
const khalti_payment_model_1 = __importDefault(require("../../database/models/khalti_payment.model"));
const promo_code_service_1 = __importDefault(require("../promo_code/promo_code.service"));
const sequelize_1 = require("sequelize");
class SubscriptionService {
    constructor() { }
    // Helper methods
    static getCurrentUsageCount(subscription, action_type) {
        switch (action_type) {
            case 'interest_sent':
                return subscription.interest_sent_count;
            case 'contact_viewed':
                return subscription.contact_viewed_count;
            case 'profile_viewed':
                return subscription.profile_viewed_count;
            case 'chat_initiated':
                return subscription.chat_initiated_count;
            default:
                return 0;
        }
    }
    static getUsageLimit(plan, action_type) {
        switch (action_type) {
            case 'interest_sent':
                return plan.interest_limit || 0;
            case 'contact_viewed':
                return plan.contact_limit || 0;
            case 'profile_viewed':
                return plan.view_profiles_limit || 0;
            case 'chat_initiated':
                return plan.chat_limit || 0;
            default:
                return 0;
        }
    }
    static getUsageFieldName(action_type) {
        switch (action_type) {
            case 'interest_sent':
                return 'interest_sent_count';
            case 'contact_viewed':
                return 'contact_viewed_count';
            case 'profile_viewed':
                return 'profile_viewed_count';
            case 'chat_initiated':
                return 'chat_initiated_count';
            default:
                throw new Error(`Unknown action type: ${action_type}`);
        }
    }
}
_a = SubscriptionService;
// Purchase subscription with payment integration
SubscriptionService.purchaseSubscription = (params) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { user_id, plan_id, payment_id, payment_method = "paypal", promo_code } = params;
        // Validate subscription plan
        const plan = yield subscription_plans_mode_1.default.findByPk(plan_id);
        if (!plan || !plan.is_active) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Subscription plan not found or inactive");
        }
        // Initialize pricing variables
        let originalAmount = plan.price;
        let discountAmount = 0;
        let finalAmount = plan.price;
        let promoCodeId;
        // Validate and apply promo code if provided
        if (promo_code) {
            const promoValidation = yield promo_code_service_1.default.validatePromoCode({
                code: promo_code,
                user_id,
                plan_id,
                plan_price: plan.price
            });
            if (!promoValidation.isValid) {
                throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, promoValidation.message || "Invalid promo code");
            }
            if (promoValidation.promoCode && promoValidation.discountAmount !== undefined && promoValidation.finalAmount !== undefined) {
                promoCodeId = promoValidation.promoCode.id;
                discountAmount = promoValidation.discountAmount;
                finalAmount = promoValidation.finalAmount;
            }
        }
        // Validate payment if provided
        let paymentStatus = "pending";
        if (payment_id) {
            let payment = null;
            if (payment_method === "paypal") {
                payment = yield paypal_payment_model_1.default.findOne({
                    where: { id: payment_id, user_id, status: "completed" }
                });
            }
            else if (payment_method === "khalti") {
                payment = yield khalti_payment_model_1.default.findOne({
                    where: { id: payment_id, user_id, status: "completed" }
                });
            }
            if (payment) {
                paymentStatus = "completed";
            }
            else {
                throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Payment not found or not completed");
            }
        }
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(startDate.getDate() + plan.duration_days);
        // Check for existing active subscription
        const existingSubscription = yield user_subscriptions_model_1.default.findOne({
            where: { user_id, is_active: true }
        });
        let subscription;
        if (existingSubscription) {
            // Update existing subscription
            subscription = yield existingSubscription.update({
                plan_id,
                payment_id,
                payment_status: paymentStatus,
                start_date: startDate,
                end_date: endDate,
                expires_at: endDate,
                interest_sent_count: 0,
                contact_viewed_count: 0,
                profile_viewed_count: 0,
                chat_initiated_count: 0,
                promo_code_id: promoCodeId,
                discount_amount: discountAmount,
                original_amount: originalAmount,
                is_active: paymentStatus === "completed",
                token: `sub_${Date.now()}_${user_id}`,
            });
        }
        else {
            // Create new subscription
            subscription = yield user_subscriptions_model_1.default.create({
                user_id,
                plan_id,
                payment_id,
                payment_status: paymentStatus,
                start_date: startDate,
                end_date: endDate,
                auto_renew: false,
                issued_at: startDate,
                expires_at: endDate,
                interest_sent_count: 0,
                contact_viewed_count: 0,
                profile_viewed_count: 0,
                chat_initiated_count: 0,
                promo_code_id: promoCodeId,
                discount_amount: discountAmount,
                original_amount: originalAmount,
                is_active: paymentStatus === "completed",
                token: `sub_${Date.now()}_${user_id}`,
            });
        }
        // Record promo code usage if promo code was applied and payment is completed
        if (promoCodeId && paymentStatus === "completed") {
            yield promo_code_service_1.default.applyPromoCode({
                promo_code_id: promoCodeId,
                user_id,
                subscription_id: subscription.id,
                original_amount: originalAmount,
                discount_amount: discountAmount,
                final_amount: finalAmount
            });
        }
        return {
            subscription,
            plan,
            originalAmount,
            discountAmount,
            finalAmount,
            promoCodeApplied: !!promoCodeId,
            message: paymentStatus === "completed" ?
                "Subscription activated successfully" :
                "Subscription created, pending payment completion"
        };
    }
    catch (error) {
        console.error('Purchase subscription error:', error);
        throw error;
    }
});
// Track usage with duplicate prevention
SubscriptionService.trackUsage = (params) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { user_id, action_type, target_user_id } = params;
        // Get active subscription
        const subscription = yield _a.getActiveSubscription(user_id);
        if (!subscription.isActive || !subscription.subscription) {
            throw new ApiError_1.default(http_status_1.default.FORBIDDEN, "No active subscription found");
        }
        const userSubscription = subscription.subscription;
        const plan = subscription.plan;
        // Check if action already performed for this target
        const existingAction = yield user_action_log_model_1.default.findOne({
            where: {
                user_id,
                action_type,
                target_user_id,
                subscription_id: userSubscription.id
            }
        });
        console.log('existingAction: ', existingAction);
        if (existingAction) {
            return {
                success: false,
                message: "Action already performed for this user in current subscription",
                alreadyPerformed: true
            };
        }
        // Check usage limits
        const currentCount = _a.getCurrentUsageCount(userSubscription, action_type);
        const limit = _a.getUsageLimit(plan, action_type);
        if (limit > 0 && currentCount > limit) {
            console.log('limit: ', limit);
            throw new ApiError_1.default(http_status_1.default.FORBIDDEN, `${action_type} limit exceeded for current subscription`);
        }
        // Log the action
        yield user_action_log_model_1.default.create({
            user_id,
            action_type,
            target_user_id,
            subscription_id: userSubscription.id,
            action_date: new Date()
        });
        // Update usage count
        const updateField = _a.getUsageFieldName(action_type);
        yield userSubscription.update({
            [updateField]: currentCount + 1
        });
        return {
            success: true,
            message: "Usage tracked successfully",
            currentUsage: currentCount + 1,
            remainingUsage: limit > 0 ? limit - (currentCount + 1) : -1
        };
    }
    catch (error) {
        console.error('Track usage error:', error);
        throw error;
    }
});
// Get active subscription status
SubscriptionService.getActiveSubscription = (user_id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const subscription = yield user_subscriptions_model_1.default.findOne({
            where: {
                user_id,
                is_active: true,
                expires_at: { [sequelize_1.Op.gt]: new Date() }
            },
            include: [
                {
                    model: subscription_plans_mode_1.default,
                    attributes: ['id', 'name', 'interest_limit', 'contact_limit', 'view_profiles_limit', 'chat_limit']
                }
            ]
        });
        if (!subscription) {
            return {
                isActive: false,
                message: "No active subscription found"
            };
        }
        const plan = yield subscription_plans_mode_1.default.findByPk(subscription.plan_id);
        if (!plan) {
            return {
                isActive: false,
                message: "Subscription plan not found"
            };
        }
        // Check if subscription is expired
        if (new Date() > subscription.expires_at) {
            yield subscription.update({ is_active: false });
            return {
                isActive: false,
                subscription,
                plan,
                message: "Subscription has expired"
            };
        }
        // Calculate remaining usage
        const remainingUsage = {
            interest_sent: plan.interest_limit > 0 ?
                Math.max(0, plan.interest_limit - subscription.interest_sent_count) : -1,
            contact_viewed: plan.contact_limit > 0 ?
                Math.max(0, plan.contact_limit - subscription.contact_viewed_count) : -1,
            profile_viewed: plan.view_profiles_limit > 0 ?
                Math.max(0, plan.view_profiles_limit - subscription.profile_viewed_count) : -1,
            chat_initiated: plan.chat_limit > 0 ?
                Math.max(0, plan.chat_limit - subscription.chat_initiated_count) : -1,
        };
        return {
            isActive: true,
            subscription,
            plan,
            message: "Active subscription found",
            remainingUsage
        };
    }
    catch (error) {
        console.error('Get active subscription error:', error);
        throw error;
    }
});
// Check if user can perform action
SubscriptionService.canPerformAction = (user_id, action_type, target_user_id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const subscriptionStatus = yield _a.getActiveSubscription(user_id);
        if (!subscriptionStatus.isActive) {
            return {
                canPerform: false,
                message: subscriptionStatus.message,
                requiresSubscription: true
            };
        }
        const { subscription, plan } = subscriptionStatus;
        const currentCount = _a.getCurrentUsageCount(subscription, action_type);
        const limit = _a.getUsageLimit(plan, action_type);
        // Check if unlimited (limit = 0 or -1)
        if (limit <= 0) {
            return {
                canPerform: true,
                message: "Unlimited usage available"
            };
        }
        // Check for duplicate action if target_user_id provided
        if (target_user_id) {
            console.log('target_user_id: ', target_user_id);
            const existingAction = yield user_action_log_model_1.default.findOne({
                where: {
                    user_id,
                    action_type: action_type,
                    target_user_id,
                    subscription_id: subscription.id
                }
            });
            if (existingAction) {
                return {
                    canPerform: true,
                    message: "Action already performed for this user",
                    alreadyPerformed: true
                };
            }
        }
        // Check if limit exceeded
        if (currentCount >= limit) {
            return {
                canPerform: false,
                message: `${action_type === 'interest_sent' ? 'Interest' : action_type === 'contact_viewed' ? 'Contact' : action_type === 'profile_viewed' ? 'Profile' : 'Chat'} limit exceeded`,
                currentUsage: currentCount,
                limit: limit
            };
        }
        return {
            canPerform: true,
            message: "Action allowed",
            currentUsage: currentCount,
            limit: limit,
            remaining: limit - currentCount
        };
    }
    catch (error) {
        console.error('Can perform action error:', error);
        throw error;
    }
});
// Daily expiry check (for cron job)
SubscriptionService.checkAndUpdateExpiredSubscriptions = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const expiredSubscriptions = yield user_subscriptions_model_1.default.findAll({
            where: {
                is_active: true,
                expires_at: { [sequelize_1.Op.lt]: new Date() }
            }
        });
        const updatePromises = expiredSubscriptions.map(subscription => subscription.update({
            is_active: false,
            revoked_at: new Date()
        }));
        yield Promise.all(updatePromises);
        console.log(`Updated ${expiredSubscriptions.length} expired subscriptions`);
        return {
            updatedCount: expiredSubscriptions.length,
            expiredSubscriptions: expiredSubscriptions.map(sub => ({
                id: sub.id,
                user_id: sub.user_id,
                expires_at: sub.expires_at
            }))
        };
    }
    catch (error) {
        console.error('Check expired subscriptions error:', error);
        throw error;
    }
});
// Get user subscription history
SubscriptionService.getUserSubscriptionHistory = (user_id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const subscriptions = yield user_subscriptions_model_1.default.findAll({
            where: { user_id },
            include: [
                {
                    model: subscription_plans_mode_1.default,
                    attributes: ['id', 'name', 'price', 'duration_days']
                }
            ],
            order: [['createdAt', 'DESC']]
        });
        return subscriptions;
    }
    catch (error) {
        console.error('Get subscription history error:', error);
        throw error;
    }
});
exports.default = SubscriptionService;
