import {
    Table,
    Column,
    Model,
    DataType,
    PrimaryKey,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    CreatedAt,
    UpdatedAt,
    Index
} from "sequelize-typescript";
import HelpCategory from "./help_category.model";

export interface HelpQuestionI {
    id: number;
    category_id: number;
    question: string;
    description: string;
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: "help_questions",
    timestamps: true,
})
class HelpQuestion extends Model<HelpQuestionI> implements HelpQuestionI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => HelpCategory)
    @AllowNull(false)
    @Column
    category_id: number;

    @BelongsTo(() => HelpCategory)
    category: HelpCategory;

    @AllowNull(false)
    @Column(DataType.STRING(255))
    question: string;

    @AllowNull(false)
    @Column(DataType.TEXT)
    description: string;

    @CreatedAt
    createdAt?: Date;

    @UpdatedAt
    updatedAt?: Date;
}

export default HelpQuestion;