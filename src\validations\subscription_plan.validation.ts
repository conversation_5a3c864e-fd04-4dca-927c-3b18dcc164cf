import Joi from "joi";

export const subscriptionPlanValidation = {
  body: Joi.object().keys({
    name: Joi.string().trim().required().messages({
      "string.base": "Name must be a string.",
      "string.empty": "Name is required.",
      "any.required": "Name is required.",
    }),

    price: Joi.number().precision(2).required().messages({
      "number.base": "Price must be a number.",
      "number.positive": "Price must be a positive number.",
      "any.required": "Price is required.",
    }),

    duration_days: Joi.number().integer().positive().required().messages({
      "number.base": "Duration days must be a number.",
      "number.integer": "Duration days must be an integer.",
      "number.positive": "Duration days must be a positive number.",
      "any.required": "Duration days is required.",
    }),

    interest: Joi.boolean().required().messages({
      "boolean.base": "Interest must be a boolean.",
      "any.required": "Interest is required.",
    }),

    interest_limit: Joi.number().integer().min(0).allow(null).messages({
      "number.base": "Interest limit must be a number.",
      "number.integer": "Interest limit must be an integer.",
      "number.min": "Interest limit must be a non-negative number.",
    }),

    contact: Joi.boolean().required().messages({
      "boolean.base": "Contact must be a boolean.",
      "any.required": "Contact is required.",
    }),

    contact_limit: Joi.number().integer().min(0).allow(null).messages({
      "number.base": "Contact limit must be a number.",
      "number.integer": "Contact limit must be an integer.",
      "number.min": "Contact limit must be a non-negative number.",
    }),

    view_profiles: Joi.boolean().required().messages({
      "boolean.base": "View profiles must be a boolean.",
      "any.required": "View profiles is required.",
    }),

    view_profiles_limit: Joi.number().integer().min(0).allow(null).messages({
      "number.base": "View profiles limit must be a number.",
      "number.integer": "View profiles limit must be an integer.",
      "number.min": "View profiles limit must be a non-negative number.",
    }),

    chat: Joi.boolean().required().messages({
      "boolean.base": "Chat must be a boolean.",
      "any.required": "Chat is required.",
    }),

    chat_limit: Joi.number().integer().min(0).allow(null).messages({
      "number.base": "Chat limit must be a number.",
      "number.integer": "Chat limit must be an integer.",
      "number.min": "Chat limit must be a non-negative number.",
    }),

    has_verified_badge: Joi.boolean().default(false).messages({
      "boolean.base": "Has verified badge must be a boolean.",
    }),

    is_personalized_matchmaking: Joi.boolean().default(false).messages({
      "boolean.base": "Is personalized matchmaking must be a boolean.",
    }),

    is_active: Joi.boolean().default(true).messages({
      "boolean.base": "Is active must be a boolean.",
    }),
  }),
};