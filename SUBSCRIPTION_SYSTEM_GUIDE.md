# Complete Subscription Management System

## Overview
This document provides a comprehensive guide for the subscription management system implemented in the BarBadhu application. The system includes subscription purchase, usage tracking with duplicate prevention, payment integration, automated expiry checks, and enhanced authentication.

## Features Implemented

### 1. Enhanced Subscription Model
- ✅ Payment status tracking (pending, completed, failed, refunded)
- ✅ Individual usage counters for each action type
- ✅ Payment integration with PayPal
- ✅ Automatic expiry management

### 2. Usage Tracking System
- ✅ Track individual actions: interest_sent, contact_viewed, profile_viewed, chat_initiated
- ✅ Prevent duplicate counting with UserActionLog model
- ✅ Real-time usage validation against subscription limits
- ✅ Unique constraint to prevent double counting

### 3. Subscription Management
- ✅ Purchase subscriptions with payment integration
- ✅ Automatic subscription activation on payment completion
- ✅ Usage tracking and validation
- ✅ Subscription status checks
- ✅ Subscription history

### 4. Automated Expiry Management
- ✅ Daily cron job to check expired subscriptions
- ✅ Automatic status updates for expired subscriptions
- ✅ Manual trigger for testing

### 5. Enhanced Authentication
- ✅ Subscription status validation during authentication
- ✅ Action permission checking middleware
- ✅ Subscription-aware authentication middleware

## Database Schema

### Updated UserSubscription Table
```sql
ALTER TABLE user_subscriptions ADD COLUMN payment_id INT;
ALTER TABLE user_subscriptions ADD COLUMN payment_status ENUM('pending','completed','failed','refunded');
ALTER TABLE user_subscriptions ADD COLUMN interest_sent_count INT DEFAULT 0;
ALTER TABLE user_subscriptions ADD COLUMN contact_viewed_count INT DEFAULT 0;
ALTER TABLE user_subscriptions ADD COLUMN profile_viewed_count INT DEFAULT 0;
ALTER TABLE user_subscriptions ADD COLUMN chat_initiated_count INT DEFAULT 0;
```

### UserActionLog Table
```sql
CREATE TABLE user_action_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  action_type ENUM('interest_sent','contact_viewed','profile_viewed','chat_initiated'),
  target_user_id INT NOT NULL,
  subscription_id INT NOT NULL,
  action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_action_target_subscription (user_id, action_type, target_user_id, subscription_id)
);
```

## API Endpoints

### Subscription Management
```
POST /subscription/purchase           - Purchase subscription
POST /subscription/track-usage        - Track usage action
GET  /subscription/status             - Get subscription status
GET  /subscription/can-perform        - Check if action is allowed
GET  /subscription/history            - Get subscription history
GET  /subscription/:subscription_id   - Get subscription details
POST /subscription/cancel             - Cancel subscription
POST /subscription/admin/check-expired - Admin: Check expired subscriptions
```

## Usage Examples

### 1. Purchase Subscription
```javascript
POST /subscription/purchase
Headers: {
  "Authorization": "Bearer {user_token}",
  "Content-Type": "application/json"
}
Body: {
  "plan_id": 1,
  "payment_id": 123  // Optional, from PayPal payment
}
```

### 2. Track Usage (with duplicate prevention)
```javascript
POST /subscription/track-usage
Headers: {
  "Authorization": "Bearer {user_token}",
  "Content-Type": "application/json"
}
Body: {
  "action_type": "interest_sent",
  "target_user_id": 456
}
```

### 3. Check if Action is Allowed
```javascript
GET /subscription/can-perform?action_type=contact_viewed&target_user_id=456
Headers: {
  "Authorization": "Bearer {user_token}"
}
```

### 4. Get Subscription Status
```javascript
GET /subscription/status
Headers: {
  "Authorization": "Bearer {user_token}"
}

Response: {
  "isActive": true,
  "subscription": {...},
  "plan": {...},
  "remainingUsage": {
    "interest_sent": 45,
    "contact_viewed": 95,
    "profile_viewed": 195,
    "chat_initiated": 25
  }
}
```

## Middleware Usage

### 1. Basic Auth with Subscription Info
```javascript
import { authWithSubscription } from '../middlewares/authWithSubscription';

router.get('/profile', authWithSubscription, (req, res) => {
  // req.subscription contains subscription info
  // req.subscriptionWarning contains warnings if subscription inactive
});
```

### 2. Require Active Subscription
```javascript
import { requireActiveSubscription } from '../middlewares/authWithSubscription';

router.post('/premium-feature', requireActiveSubscription, (req, res) => {
  // Only users with active subscriptions can access
});
```

### 3. Check Specific Action Permission
```javascript
import { checkActionPermission } from '../middlewares/authWithSubscription';

router.post('/send-interest', checkActionPermission('interest_sent'), (req, res) => {
  // Automatically checks if user can send interest
  // Blocks if limit exceeded or already sent to target user
});
```

## Cron Jobs

### Daily Expiry Check
- **Schedule**: Daily at 12:00 AM UTC
- **Function**: Automatically deactivates expired subscriptions
- **Manual Trigger**: Available for testing

```javascript
// Manual trigger for testing
const cronService = CronJobService.getInstance();
const result = await cronService.triggerSubscriptionExpiryCheck();
```

## Integration with PayPal

The subscription system is fully integrated with the PayPal payment system:

1. **Purchase Flow**:
   - User selects subscription plan
   - Creates PayPal order
   - Completes payment
   - Subscription automatically activated

2. **Payment Status Tracking**:
   - Pending: Subscription created but payment not completed
   - Completed: Payment successful, subscription active
   - Failed: Payment failed, subscription inactive
   - Refunded: Payment refunded, subscription deactivated

## Usage Tracking Logic

### Duplicate Prevention
1. When user performs an action, system checks UserActionLog
2. If action already exists for same user + target + subscription, blocks duplicate
3. If new action, logs it and increments usage counter
4. Checks against subscription limits before allowing action

### Action Types
- **interest_sent**: Sending interest to another user
- **contact_viewed**: Viewing contact details of another user
- **profile_viewed**: Viewing full profile of another user
- **chat_initiated**: Starting a chat with another user

## Error Handling

### Common Error Scenarios
1. **No Active Subscription**: Returns 403 with subscription required message
2. **Usage Limit Exceeded**: Returns 403 with limit exceeded message
3. **Duplicate Action**: Returns success but indicates already performed
4. **Expired Subscription**: Automatically deactivated, returns subscription expired message

## Setup Instructions

### 1. Run Migrations
```bash
npx sequelize-cli db:migrate
```

### 2. Install Dependencies
```bash
npm install node-cron
```

### 3. Environment Variables
No additional environment variables required.

### 4. Start Server
The cron jobs will automatically initialize when the server starts.

## Files Created/Modified

### New Files:
- `src/app/subscription/subscription.service.ts`
- `src/app/subscription/subscription.controller.ts`
- `src/database/models/user_action_log.model.ts`
- `src/validations/subscription.validation.ts`
- `src/routes/subscription.route.ts`
- `src/middlewares/authWithSubscription.ts`
- `src/services/cronJobs.ts`
- `src/database/migrations/20241201130000-update-subscription-tables.js`

### Modified Files:
- `src/database/models/user_subscriptions.model.ts` - Enhanced with usage tracking
- `src/database/database.ts` - Added new models
- `src/routes/index.ts` - Added subscription routes
- `src/server/index.ts` - Initialize cron jobs

## Testing

### Test Subscription Purchase
1. Create PayPal payment
2. Purchase subscription with payment_id
3. Verify subscription is activated

### Test Usage Tracking
1. Perform action (e.g., send interest)
2. Verify usage count incremented
3. Try same action again - should be blocked
4. Verify limit enforcement

### Test Expiry Check
1. Create subscription with past expiry date
2. Run manual expiry check
3. Verify subscription deactivated

## Security Features
- ✅ JWT token validation
- ✅ Subscription status verification
- ✅ Usage limit enforcement
- ✅ Duplicate action prevention
- ✅ Payment status validation
- ✅ SQL injection protection via Sequelize ORM

This subscription system provides a complete solution for managing user subscriptions with payment integration, usage tracking, and automated management.
