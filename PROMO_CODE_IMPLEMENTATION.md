# Promo Code Implementation Guide

This document outlines the implementation of promo code functionality within the subscription module.

## Overview

The promo code system allows users to apply discount codes during subscription purchases, providing percentage or fixed amount discounts with various validation rules and usage tracking.

## Features

### Core Features
- **Promo Code Validation**: Validates codes against multiple criteria
- **Discount Calculation**: Supports percentage and fixed amount discounts
- **Usage Tracking**: Tracks individual user usage and overall statistics
- **Admin Management**: Full CRUD operations for promo codes
- **Integration**: Seamlessly integrated with existing subscription flow

### Validation Rules
- Code existence and active status
- Expiry date validation
- Usage limit enforcement
- User-specific usage prevention (one-time use per user)
- Plan applicability restrictions
- Minimum purchase amount requirements
- First-time user restrictions
- Maximum discount limits

## Database Schema

### Promo Codes Table (`promo_codes`)
```sql
- id (Primary Key)
- code (Unique, String, 50 chars)
- description (Text, Optional)
- discount_type (ENUM: 'percentage', 'fixed_amount')
- discount_value (Decimal)
- minimum_purchase_amount (Decimal, Optional)
- maximum_discount_amount (Decimal, Optional)
- usage_limit (Integer, Optional)
- used_count (Integer, Default: 0)
- start_date (Date, Optional)
- expiry_date (Date, Optional)
- applicable_plans (JSON, Optional)
- first_time_users_only (Boolean, Default: false)
- is_active (Boolean, Default: true)
- created_by (Foreign Key to users, Optional)
- createdAt, updatedAt (Timestamps)
```

### Promo Code Usage Table (`promo_code_usage`)
```sql
- id (Primary Key)
- promo_code_id (Foreign Key to promo_codes)
- user_id (Foreign Key to users)
- subscription_id (Foreign Key to user_subscriptions, Optional)
- discount_amount (Decimal)
- original_amount (Decimal)
- final_amount (Decimal)
- used_at (Date)
- createdAt, updatedAt (Timestamps)
```

### Updated User Subscriptions Table
Added fields:
- `promo_code_id` (Foreign Key, Optional)
- `discount_amount` (Decimal, Optional)
- `original_amount` (Decimal, Optional)

## API Endpoints

### User Endpoints

#### Validate Promo Code
```
POST /api/promo_code/validate
Body: { code: string, plan_id: number }
Response: { isValid: boolean, discountAmount?: number, finalAmount?: number, ... }
```

#### Get Usage History
```
GET /api/promo_code/usage-history
Response: Array of user's promo code usage records
```

#### Get Promo Code Info
```
GET /api/promo_code/:code
Response: Public promo code information
```

### Subscription Purchase (Updated)
```
POST /api/subscription/purchase
Body: { plan_id: number, payment_id?: number, promo_code?: string }
Response: Enhanced with promo code application details
```

### Admin Endpoints

#### Create Promo Code
```
POST /api/admin/promo_codes
Body: { code, description, discount_type, discount_value, ... }
```

#### Get All Promo Codes
```
GET /api/admin/promo_codes?page=1&limit=10&search=&is_active=true
```

#### Get Promo Code by ID
```
GET /api/admin/promo_codes/:id
```

#### Update Promo Code
```
PUT /api/admin/promo_codes/:id
Body: { description?, discount_value?, ... }
```

#### Delete Promo Code
```
DELETE /api/admin/promo_codes/:id
```

#### Get Promo Code Statistics
```
GET /api/admin/promo_codes/:id/stats
Response: Usage statistics and analytics
```

## Usage Examples

### Creating a Promo Code (Admin)
```javascript
POST /api/admin/promo_codes
{
  "code": "WELCOME20",
  "description": "20% off for new users",
  "discount_type": "percentage",
  "discount_value": 20,
  "maximum_discount_amount": 50,
  "first_time_users_only": true,
  "expiry_date": "2024-12-31T23:59:59Z",
  "is_active": true
}
```

### Validating a Promo Code (User)
```javascript
POST /api/promo_code/validate
{
  "code": "WELCOME20",
  "plan_id": 1
}

Response:
{
  "isValid": true,
  "originalAmount": 100,
  "discountAmount": 20,
  "finalAmount": 80,
  "promoCode": {
    "code": "WELCOME20",
    "description": "20% off for new users",
    "discount_type": "percentage",
    "discount_value": 20
  }
}
```

### Purchasing with Promo Code
```javascript
POST /api/subscription/purchase
{
  "plan_id": 1,
  "payment_id": 123,
  "promo_code": "WELCOME20"
}

Response:
{
  "subscription": { ... },
  "plan": { ... },
  "originalAmount": 100,
  "discountAmount": 20,
  "finalAmount": 80,
  "promoCodeApplied": true,
  "message": "Subscription activated successfully"
}
```

## Implementation Details

### Service Layer
- `PromoCodeService`: Core validation and application logic
- `AdminPromoCodeService`: Admin management operations
- Updated `SubscriptionService`: Integrated promo code handling

### Validation Logic
1. Code existence and status check
2. Date range validation (start/expiry)
3. Usage limit verification
4. User-specific usage check
5. Plan applicability validation
6. Minimum purchase validation
7. First-time user restriction check
8. Discount calculation with limits

### Error Handling
- Comprehensive error messages for validation failures
- Proper HTTP status codes
- Transaction rollback on failures
- Logging for debugging and monitoring

## Testing

### Test Coverage
- Unit tests for validation logic
- Integration tests for API endpoints
- Edge case testing for discount calculations
- Error scenario validation

### Running Tests
```bash
npm test src/tests/promo_code.test.ts
```

## Migration Instructions

1. Run database migrations:
   ```bash
   npx sequelize-cli db:migrate
   ```

2. Update environment variables if needed

3. Restart the application

4. Test the implementation with sample promo codes

## Security Considerations

- Promo codes are case-insensitive and stored in uppercase
- Usage tracking prevents duplicate applications
- Admin-only access for promo code management
- Input validation and sanitization
- Rate limiting on validation endpoints (recommended)

## Future Enhancements

- Bulk promo code generation
- Advanced analytics and reporting
- Integration with marketing campaigns
- Automatic expiry notifications
- Referral-based promo codes
- Category-specific discounts
