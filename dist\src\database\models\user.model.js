"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var User_1;
Object.defineProperty(exports, "__esModule", { value: true });
const sequelize_typescript_1 = require("sequelize-typescript");
const bcrypt = __importStar(require("bcrypt"));
const user_astro_details_model_1 = __importDefault(require("./user_astro_details.model"));
const user_basic_details_model_1 = __importDefault(require("./user_basic_details.model"));
const user_education_career_model_1 = __importDefault(require("./user_education_career.model"));
const user_family_details_model_1 = __importDefault(require("./user_family_details.model"));
const user_lifestyle_model_1 = __importDefault(require("./user_lifestyle.model"));
const user_location_details_model_1 = __importDefault(require("./user_location_details.model"));
const user_verifications_model_1 = __importDefault(require("./user_verifications.model"));
const user_hobbies_model_1 = __importDefault(require("./user_hobbies.model"));
const user_shortlist_model_1 = __importDefault(require("./user_shortlist.model"));
const privacy_settings_model_1 = __importDefault(require("./privacy_settings.model"));
const token_model_1 = __importDefault(require("./token.model"));
const user_subscriptions_model_1 = __importDefault(require("./user_subscriptions.model"));
const user_preferences_model_1 = __importDefault(require("./user_preferences.model"));
const user_gallery_model_1 = __importDefault(require("./user_gallery.model"));
const sequelize_1 = require("sequelize");
let User = User_1 = class User extends sequelize_typescript_1.Model {
    static encryptPassword(instance) {
        return __awaiter(this, void 0, void 0, function* () {
            if (instance.changed('password')) {
                instance.password = yield bcrypt.hash(instance.password, 10);
            }
        });
    }
    static generateMemberId(instance) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield User_1.findOne({
                attributes: [
                    [sequelize_1.Sequelize.fn('MAX', sequelize_1.Sequelize.col('member_id')), 'maxMemberId']
                ],
                raw: true,
            });
            let nextId = 1111;
            if (result === null || result === void 0 ? void 0 : result.maxMemberId) {
                nextId = parseInt(result.maxMemberId, 10) + 1;
            }
            instance.member_id = nextId.toString().padStart(6, '0');
        });
    }
};
__decorate([
    sequelize_typescript_1.PrimaryKey,
    sequelize_typescript_1.AutoIncrement,
    sequelize_typescript_1.Column,
    __metadata("design:type", Number)
], User.prototype, "id", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(true),
    sequelize_typescript_1.Unique,
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING(266)),
    __metadata("design:type", String)
], User.prototype, "member_id", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING(100)),
    __metadata("design:type", String)
], User.prototype, "first_name", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(true),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING(100)),
    __metadata("design:type", String)
], User.prototype, "middle_name", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING(100)),
    __metadata("design:type", String)
], User.prototype, "last_name", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Default)("male"),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.ENUM("male", "female", "other")),
    __metadata("design:type", String)
], User.prototype, "gender", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(true),
    (0, sequelize_typescript_1.Default)("Myself"),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING(255)),
    __metadata("design:type", String)
], User.prototype, "profile_created_for", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.DATE),
    __metadata("design:type", Date)
], User.prototype, "date_of_birth", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING(100)),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING(15)),
    __metadata("design:type", String)
], User.prototype, "phone", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING()),
    __metadata("design:type", String)
], User.prototype, "phone_code", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING(255)),
    __metadata("design:type", String)
], User.prototype, "password", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Default)("new"),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.ENUM("new", "pending", "approved", "rejected", "blocked", "deactivated")),
    __metadata("design:type", String)
], User.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Default)(false),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.BOOLEAN),
    __metadata("design:type", Boolean)
], User.prototype, "is_hide_profile", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Default)(false),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.BOOLEAN),
    __metadata("design:type", Boolean)
], User.prototype, "terms_condition", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(true),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.TEXT),
    __metadata("design:type", String)
], User.prototype, "profile_bio", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(true),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING(255)),
    __metadata("design:type", String)
], User.prototype, "profile_image", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Default)(0),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.INTEGER),
    __metadata("design:type", Number)
], User.prototype, "failedLoginAttempts", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(false),
    (0, sequelize_typescript_1.Default)(false),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.BOOLEAN),
    __metadata("design:type", Boolean)
], User.prototype, "isSuspended", void 0);
__decorate([
    (0, sequelize_typescript_1.AllowNull)(true),
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.DATE),
    __metadata("design:type", Object)
], User.prototype, "suspendedAt", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => user_gallery_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_gallery_model_1.default)
], User.prototype, "userGallery", void 0);
__decorate([
    (0, sequelize_typescript_1.HasOne)(() => privacy_settings_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", privacy_settings_model_1.default)
], User.prototype, "privacySetting", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => user_subscriptions_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_subscriptions_model_1.default)
], User.prototype, "userSubscription", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => token_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", token_model_1.default)
], User.prototype, "token", void 0);
__decorate([
    (0, sequelize_typescript_1.HasOne)(() => user_basic_details_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_basic_details_model_1.default)
], User.prototype, "basicDetails", void 0);
__decorate([
    (0, sequelize_typescript_1.HasOne)(() => user_location_details_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_location_details_model_1.default)
], User.prototype, "locationDetails", void 0);
__decorate([
    (0, sequelize_typescript_1.HasOne)(() => user_education_career_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_education_career_model_1.default)
], User.prototype, "educationCareer", void 0);
__decorate([
    (0, sequelize_typescript_1.HasOne)(() => user_lifestyle_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_lifestyle_model_1.default)
], User.prototype, "lifestyle", void 0);
__decorate([
    (0, sequelize_typescript_1.HasOne)(() => user_astro_details_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_astro_details_model_1.default)
], User.prototype, "astroDetails", void 0);
__decorate([
    (0, sequelize_typescript_1.HasOne)(() => user_family_details_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_family_details_model_1.default)
], User.prototype, "familyDetails", void 0);
__decorate([
    (0, sequelize_typescript_1.HasOne)(() => user_hobbies_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_hobbies_model_1.default)
], User.prototype, "hobbies", void 0);
__decorate([
    (0, sequelize_typescript_1.HasOne)(() => user_verifications_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_verifications_model_1.default)
], User.prototype, "verification", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => user_shortlist_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_shortlist_model_1.default)
], User.prototype, "userShortlistDetails", void 0);
__decorate([
    (0, sequelize_typescript_1.HasOne)(() => user_shortlist_model_1.default, { foreignKey: "shortlisted_user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_shortlist_model_1.default)
], User.prototype, "shortlisted_user", void 0);
__decorate([
    (0, sequelize_typescript_1.HasOne)(() => user_preferences_model_1.default, { foreignKey: "user_id", onDelete: "CASCADE", }),
    __metadata("design:type", user_preferences_model_1.default)
], User.prototype, "userPreference", void 0);
__decorate([
    sequelize_typescript_1.BeforeCreate,
    sequelize_typescript_1.BeforeUpdate,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [User]),
    __metadata("design:returntype", Promise)
], User, "encryptPassword", null);
__decorate([
    sequelize_typescript_1.BeforeCreate,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [User]),
    __metadata("design:returntype", Promise)
], User, "generateMemberId", null);
User = User_1 = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: "users",
        timestamps: true,
    })
], User);
exports.default = User;
// import {
//   AllowNull,
//   AutoIncrement,
//   Column,
//   DataType,
//   Default,
//   Model,
//   PrimaryKey,
//   Table,
//   ForeignKey,
//   BelongsTo
// } from "sequelize-typescript";
// import User from "./user.model";
// export interface UserReportI {
//   id: number;
//   reporter_id: number;
//   reported_user_id: number;
//   reason: "Harassment" | "Fake Profile" | "Inappropriate Content" | "Other";
//   details: string;
//   status: "Pending" | "Reviewed" | "Resolved" | "Dismissed";
//   admin_notes: string;
//   admin_action: "None" | "Warning" | "Suspension" | "Account Deletion";
//   createdAt?: Date;
//   updatedAt?: Date;
// }
// @Table({
//   tableName: "user_reports",
//   timestamps: true,
// })
// class UserReport extends Model<UserReportI> implements UserReportI {
//   @PrimaryKey
//   @AutoIncrement
//   @Column
//   id: number;
//   @AllowNull(false)
//   @ForeignKey(() => User)
//   @Column(DataType.INTEGER)
//   reporter_id: number;
//   @AllowNull(false)
//   @ForeignKey(() => User)
//   @Column(DataType.INTEGER)
//   reported_user_id: number;
//   @AllowNull(false)
//   @Column(DataType.ENUM("Harassment", "Fake Profile", "Inappropriate Content", "Other"))
//   reason: "Harassment" | "Fake Profile" | "Inappropriate Content" | "Other";
//   @AllowNull(true)
//   @Column(DataType.TEXT)
//   details: string;
//   @AllowNull(false)
//   @Default("Pending")
//   @Column(DataType.ENUM("Pending", "Reviewed", "Resolved", "Dismissed"))
//   status: "Pending" | "Reviewed" | "Resolved" | "Dismissed";
//   @AllowNull(true)
//   @Column(DataType.TEXT)
//   admin_notes: string;
//   @AllowNull(false)
//   @Default("None")
//   @Column(DataType.ENUM("None", "Warning", "Suspension", "Account Deletion"))
//   admin_action: "None" | "Warning" | "Suspension" | "Account Deletion";
//   @BelongsTo(() => User, { foreignKey: 'reporter_id', as: 'reporter' })
//   reporter: User;
//   @BelongsTo(() => User, { foreignKey: 'reported_user_id', as: 'reportedUser' })
//   reportedUser: User;
// }
// export default UserReport;
