import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON>Key,
  AutoIncrement,
  AllowNull,
  Default,
  Unique,
  BeforeCreate,
  BeforeUpdate,
  ForeignKey,
  BelongsTo,
} from "sequelize-typescript";
import * as bcrypt from "bcrypt";
import Role from "./role.model";

export interface IAdmin {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  mobile_number: string;
  username: string;
  password: string;
  role_id?: number;
  is_super_admin: boolean;
}

@Table({
  tableName: "admins",
  timestamps: true,
})
class Admin extends Model<IAdmin> implements IAdmin {
  @AutoIncrement
  @PrimaryKey
  @Column(DataType.INTEGER)
  id: number;

  @AllowNull(false)
  @Column(DataType.STRING(255))
  first_name: string;

  @AllowNull(false)
  @Column(DataType.STRING(255))
  last_name: string;

  @AllowNull(false)
  @Unique
  @Column(DataType.STRING(255))
  email: string;

  @AllowNull(false)
  @Column(DataType.STRING(20))
  mobile_number: string;

  @AllowNull(false)
  @Unique
  @Column(DataType.STRING(255))
  username: string;

  @AllowNull(false)
  @Column(DataType.STRING)
  password: string;

  @ForeignKey(() => Role)
  @AllowNull(false)
  @Column(DataType.INTEGER)
  role_id?: number;

  @BelongsTo(() => Role, { foreignKey: "role_id", onDelete: "CASCADE" })
  role: Role;

  @Default(false)
  @Column(DataType.BOOLEAN)
  is_super_admin: boolean;

  // Hash password before creating or updating the user
  @BeforeCreate
  @BeforeUpdate
  static async hashPassword(instance: Admin) {
    if (instance.changed("password")) {
      instance.password = await bcrypt.hash(instance.password, 10);
    }
  }
}

export default Admin;
