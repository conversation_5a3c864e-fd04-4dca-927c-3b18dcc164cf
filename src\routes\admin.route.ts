import express from "express";
import AdminController from "../app/admin/admin.controller";
import { auth } from "../middlewares/auth";
import { validate } from "../middlewares/middleware";
import { adminloginValidation, createAdminValidation, updateAdminValidation } from "../validations/admin.validation";
const router = express.Router();

router.post("/login",validate(adminloginValidation), AdminController.login);
router.get("/", auth, AdminController.getAll);
router.post("/", auth,validate(createAdminValidation), AdminController.create);
router.get("/:adminId", auth, AdminController.showById);
router.put("/:adminId", auth,validate(updateAdminValidation), AdminController.update);
router.delete("/:adminId", auth, AdminController.delete);

export default router;
