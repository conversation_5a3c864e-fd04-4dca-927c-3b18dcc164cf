import { Request, Response } from "express";
import httpStatus from "http-status";

import errorResponse, { sentResponse } from "../../utils/response";
import catchAsync from "../../utils/catchAsync";
import httpMessages from "../../config/httpMessages";

import AuthService from "./auth.service";
import UserService from "../user/user.service";
import TokenService from "../../common/services/token.service";
import EmailService from "../../common/services/email.service";
import { UserI } from "../../database/models/user.model";
import ApiError from "../../utils/ApiError";

export default class AuthController {
  static authService = AuthService;
  static userService = UserService;
  static tokenService = TokenService;
  static emailService = EmailService;

  constructor() { }

  static register = catchAsync(async (request: Request, response: Response) => {
    try {
      const userData = { ...request.body };
      const user = await this.userService.createUser(userData);

      // const email_tokens = await this.tokenService.generateVerifyEmailToken(user);
      // await this.emailService.sendVerificationEmail(user.email, email_tokens); //TODO email verfication apply
      try {
        await this.emailService.sendOtpForVerificationEmail(user.email, user.id);
      } catch (error) {
        console.log('error: ', error);
      }
      return sentResponse(response, {
        statusCode: httpStatus.CREATED,
        message: httpMessages.REGISTER.SUCCESS,
        data: null,
      });
    } catch (error) {
      console.log('error: ', error);
      return errorResponse(response, error);
    }
  });

  static login = catchAsync(async (request: Request, response: Response) => {
    try {
      const { email, password } = request.body;
      const user = await this.authService.loginUserWithEmailAndPassword(
        email,
        password
      );
      if (!user.verification.is_email_verified) {
       await this.emailService.sendOtpForVerificationEmail(user.email, user.id);
      }
      const tokens = await this.tokenService.generateAuthTokens(user);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.LOGIN.SUCCESS,
        data: { ...user, token: tokens },
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static logout = catchAsync(async (request: Request, response: Response) => {
    try {
      const { refreshToken }: any = request.body;
      await this.authService.logout(refreshToken);
      return response.status(httpStatus.NO_CONTENT).send();
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static refreshTokens = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const tokens = await this.authService.refreshAuth(
          request.body.refreshToken
        );
        const data = { ...tokens };
        return response.status(httpStatus.CREATED).send({
          success: true,
          message: httpMessages.REFRESH_TOKEN_SUCCESS,
          data
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  static forgotPassword = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const resetPasswordToken =
          await this.tokenService.generateResetPasswordToken(
            request.body.email
          );
        console.log(">>>>>> forgotPassword: ", {
          email: request.body.email,
          resetPasswordToken
        });
        await this.emailService.sendResetPasswordEmail(
          request.body.email,
          resetPasswordToken
        );
        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: httpMessages.USER.PROFILE.RESET_PASSWORD_EMAIL_SENT,
          data: null,
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  static resetPassword = catchAsync(
    async (request: Request, response: Response) => {
      try {
        const { body, query } = request;
        const { token } = query;
        await this.authService.resetPassword(token, body.password || "");
        return response.status(httpStatus.OK).send({
          success: true,
          message: httpMessages.USER.PROFILE.RESET_PASSWORD_SUCCESS
        });
      } catch (error) {
        return errorResponse(response, error);
      }
    }
  );

  static verifyEmailOtp = catchAsync(async (request: Request, response: Response) => {
    try {
      const { email, otp } = request.body;
      let user = await this.emailService.verifyEmailOtp(email, otp);

      const tokens = await this.tokenService.generateAuthTokens(user);

      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.OTP.EMAIL_OTP_VERIFIED,
        data: { ...user, token: tokens },
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static resendEmailOtp = catchAsync(async (request: Request, response: Response) => {
    try {
      const { email } = request.body;
      let user = await this.userService.getUserByEmail(email);
      if (!user) {
        return errorResponse(
          response,
          new ApiError(
            httpStatus.BAD_REQUEST,
            httpMessages.USER.EMAIL_NOT_FOUND
          )
        );
      }
      await this.emailService.sendOtpForVerificationEmail(email, user.id);

      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.OTP.RESENT_OTP,
        data: null,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  // static sendVerificationEmail = catchAsync(async (request: Request, response: Response) => {
  //   const verifyEmailToken = await this.tokenService.generateVerifyEmailToken(
  //     request.user
  //   );
  //   await emailService.sendVerificationEmail(request.user.email, verifyEmailToken);
  //   return response.status(httpStatus.NO_CONTENT).send();
  // });

}
