import express from "express";
const router = express.Router();

import userRoute from "./user.route";
import authRoute from "./auth.route";
import adminRoute from "./admin.route";
import userProfileRoute from "./user_profile.route";
import userShortlistRoute from "./user_shortlist.route";
import geolocationRoute from "./geolocation.route";
import userPreferenceRoute from "./user_preferences.route";
import userGalleryRoute from "./user_gallery.route";
import subscriptionPlanRoute from "./subscription_plans.route";
import userSubscriptionRoute from "./user_subscription.route";
import privacySettingRoute from "./privacy_setting.route";
import userInvitationRoute from "./user_invitation.route";
import chatRoute from "./chat.route";
import messageRoute from "./message.route";
import userVerificationsRoute from "./user_verifications.route";
import successStoryRoute from "./success_story.route";
import roleRoute from "./role.route";
import paypalRoute from "./paypal.route";
import subscriptionRoute from "./subscription.route";
import khaltiRoute from "./khalti.route";
import helpRoute from "./help.route";
import inquiryRoute from "./inquiries.route";
import dashboardRoute from "./dashboard.route";
import userNearbyRoute from "./user_nearby.route";
import moduleRoute from "./module.route";
import promoCodeRoute from "./promo_code.route";
import adminPromoCodeRoute from "./admin_promo_code.route";

const defaultRoutes = [
  {
    path: "/roles",
    route: roleRoute,
  },
  {
    path: "/admin",
    route: adminRoute,
  },
  {
    path: "/modules",
    route: moduleRoute,
  },
  {
    path: "/auth",
    route: authRoute,
  },
  {
    path: "/user_verifications",
    route: userVerificationsRoute,
  },
  {
    path: "/user",
    route: userRoute,
  },
  {
    path: "/geolocation",
    route: geolocationRoute,
  },
  {
    path: "/privacy_setting",
    route: privacySettingRoute,
  },
  {
    path: "/user_profile",
    route: userProfileRoute,
  },
  {
    path: "/user_shortlist",
    route: userShortlistRoute,
  },
  {
    path: "/user_preference",
    route: userPreferenceRoute,
  },
  {
    path: "/user_gallery",
    route: userGalleryRoute,
  },
  {
    path: "/subscription_plan",
    route: subscriptionPlanRoute,
  },
  {
    path: "/user_subscription",
    route: userSubscriptionRoute,
  },
  {
    path: "/paypal",
    route: paypalRoute,
  },
  {
    path: "/user_invitation",
    route: userInvitationRoute,
  },
  {
    path: "/chats",
    route: chatRoute,
  },
  {
    path: "/messages",
    route: messageRoute,
  },
  {
    path: "/success_story",
    route: successStoryRoute,
  },
  {
    path: "/help",
    route: helpRoute,
  },
  {
    path: "/inquiries",
    route: inquiryRoute,
  },
  {
    path: "/dashboard",
    route: dashboardRoute,
  },
  {
    path: "/user_nearby",
    route: userNearbyRoute,
  },
  {
    path: "/subscription",
    route: subscriptionRoute,
  },
  {
    path: "/khalti",
    route: khaltiRoute,
  },
  {
    path: "/promo_code",
    route: promoCodeRoute,
  },
  {
    path: "/admin_promo_codes",
    route: adminPromoCodeRoute,
  },
];

defaultRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

export default router;
