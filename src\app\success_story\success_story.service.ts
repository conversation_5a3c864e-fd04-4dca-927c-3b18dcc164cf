import httpStatus from "http-status";
import ApiError from "../../utils/ApiError";
import SuccessStory from "../../database/models/success_story.model";
import SuccessStoryMedia from "../../database/models/success_story_media.model";
import User from "../../database/models/user.model";
import { Op } from "sequelize";
import EmailService from "../../common/services/email.service";

export default class SuccessStoryService {
    /**
     * Get all success stories with pagination and search
     * @param {Object} options - Query options
     * @returns {Promise<Object>}
     */
    static getSuccessStories =  async (options: any) => {
        try {
            const { page = 1, limit = 10, search = "", status } = options;
            const offset = (page - 1) * limit;

            const whereClause: any = {};

            if (search) {
                whereClause[Op.or] = [
                    { bride_name: { [Op.like]: `%${search}%` } },
                    { groom_name: { [Op.like]: `%${search}%` } },
                    { bride_email: { [Op.like]: `%${search}%` } },
                    { groom_email: { [Op.like]: `%${search}%` } },
                ];
            }

            if (status) {
                whereClause.status = status;
            }

            const { count, rows } = await SuccessStory.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: User,
                        attributes: ["id", "first_name", "last_name", "email"],
                    },
                    {
                        model: SuccessStoryMedia,
                        attributes: ["id", "media_type", "media_path"],
                    },
                ],
                limit,
                offset,
                order: [["createdAt", "DESC"]],
            });

            if (page && limit) {
                return {
                    totalItems: count,
                    totalPages: Math.ceil(count / limit),
                    currentPage: page,
                    success_stories: rows,
                };
            } else {
                return rows;
            }
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Get approved success stories for public display
     * @param {Object} options - Query options
     * @returns {Promise<Object>}
     */
    static getApprovedSuccessStories = async (options: any) => {
        try {
            const { page = 1, limit = 10 } = options;
            const offset = (page - 1) * limit;

            const { count, rows } = await SuccessStory.findAndCountAll({
                where: { status: "approved" },
                include: [
                    {
                        model: SuccessStoryMedia,
                        attributes: ["id", "media_type", "media_path"],
                    },
                ],
                limit,
                offset,
                order: [["createdAt", "DESC"]],
            });

            return {
                total: count,
                totalPages: Math.ceil(count / limit),
                currentPage: page,
                data: rows,
            };
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Get success stories by user ID
     * @param {number} userId - User ID
     * @param {Object} options - Query options
     * @returns {Promise<Object>}
     */
    static getUserSuccessStories = async (userId: number, options: any) => {
        try {
            const { page, limit, search, status } = options;
            const whereCondition: any = search
                ? {
                    [Op.or]: [
                        { bride_name: { [Op.like]: `%${search}%` } },
                        { groom_name: { [Op.like]: `%${search}%` } },
                        { bride_email: { [Op.like]: `%${search}%` } },
                        { groom_email: { [Op.like]: `%${search}%` } },
                    ],
                }
                : {};

            if (status) {
                whereCondition.status = status;
            }

            const queryOption: any = {
                where: whereCondition,
                include: [
                    {
                        model: SuccessStoryMedia,
                        attributes: ["id", "media_type", "media_path"],
                    },
                ],
                order: [["createdAt", "DESC"]],
            };
            // If pagination is provided, apply pagination
            if (page && limit) {
                const offset = (page - 1) * limit;
                queryOption.limit = limit;
                queryOption.offset = offset;
            }
            const data = await SuccessStory.findAndCountAll(queryOption);
            if (page && limit) {
                return {
                    totalItems: data.count,
                    totalPages: Math.ceil(data.count / limit),
                    currentPage: page,
                    success_stories: data.rows,
                };
            } else {
                return data.rows[0];
            }
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Create a new success story
     * @param {Object} data - Success story data
     * @param {Array} mediaFiles - Media files
     * @returns {Promise<SuccessStory>}
     */
    static createSuccessStory = async (data: any, mediaFiles: any) => {
        try {
            const successStory = await SuccessStory.create(data);

            if (mediaFiles && mediaFiles.length > 0) {
                const mediaPromises = mediaFiles.map((file: string) => {
                    return SuccessStoryMedia.create({
                        story_id: successStory.id,
                        media_type: "image", // Assuming all are images for now
                        media_path: file,
                    });
                });

                await Promise.all(mediaPromises);
            }

            // Send confirmation email
            const user = await User.findByPk(data.user_id);
            if (user) {
                await EmailService.sendSuccessStorySubmissionEmail(user.email, {
                    userName: `${user.first_name} ${user.last_name}`,
                    storyId: successStory.id,
                });
            }

            return successStory;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Get success story by ID
     * @param {number} id - Success story ID
     * @returns {Promise<SuccessStory>}
     */
    static getSuccessStoryById = async (id: number) => {
        try {
            const successStory = await SuccessStory.findByPk(id, {
                include: [
                    {
                        model: User,
                        attributes: ["id", "first_name", "last_name", "email"],
                    },
                    {
                        model: SuccessStoryMedia,
                        attributes: ["id", "media_type", "media_path"],
                    },
                ],
            });

            if (!successStory) {
                throw new ApiError(httpStatus.NOT_FOUND, "Success story not found");
            }

            return successStory;
        } catch (error: any) {
            throw new ApiError(error.statusCode || httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Update success story status
     * @param {number} id - Success story ID
     * @param {Object} data - Status data
     * @returns {Promise<SuccessStory>}
     */
    static updateSuccessStoryStatus = async (id: number, data: any) => {
        try {
            const successStory = await SuccessStory.findByPk(id, {
                include: [
                    {
                        model: User,
                        attributes: ["id", "first_name", "last_name", "email"],
                    },
                ],
            });

            if (!successStory) {
                throw new ApiError(httpStatus.NOT_FOUND, "Success story not found");
            }

            await successStory.update(data);

            // Send status update email
            if (successStory.user && successStory.user.email) {
                await EmailService.sendSuccessStoryStatusUpdateEmail(successStory.user.email, {
                    userName: `${successStory.user.first_name} ${successStory.user.last_name}`,
                    storyId: successStory.id,
                    status: data.status,
                    adminNotes: data.admin_notes || "",
                });
            }

            return successStory;
        } catch (error: any) {
            throw new ApiError(error.statusCode || httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Delete success story
     * @param {number} id - Success story ID
     * @returns {Promise<boolean>}
     */
    static deleteSuccessStory = async (id: number) => {
        try {
            const successStory = await SuccessStory.findByPk(id);

            if (!successStory) {
                throw new ApiError(httpStatus.NOT_FOUND, "Success story not found");
            }

            await SuccessStoryMedia.destroy({ where: { story_id: id } });
            await successStory.destroy();

            return true;
        } catch (error: any) {
            throw new ApiError(error.statusCode || httpStatus.BAD_REQUEST, error.message);
        }
    };
}
