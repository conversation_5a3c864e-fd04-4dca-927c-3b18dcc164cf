"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const response_1 = __importDefault(require("../../utils/response"));
const response_2 = __importDefault(require("../../utils/response"));
const message_service_1 = __importDefault(require("./message.service"));
const chat_service_1 = __importDefault(require("../chat/chat.service"));
const socket_1 = require("../../socket");
class MessageController {
}
_a = MessageController;
MessageController.messageService = message_service_1.default;
MessageController.chatService = chat_service_1.default;
/**
 * Send a new message
 * @route POST /api/messages
 */
MessageController.sendMessage = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { chatId, receiverId, content } = request.body;
        const senderId = request.decoded;
        // Verify chat exists and user has access
        const chat = yield _a.chatService.getChatById(chatId, senderId);
        if (chat.user1_id !== senderId && chat.user2_id !== senderId) {
            return (0, response_2.default)(response, {
                statusCode: http_status_1.default.FORBIDDEN,
                message: "You do not have permission to send messages in this chat",
            });
        }
        const message = yield _a.messageService.sendMessage(chatId, senderId, receiverId, content);
        return (0, response_1.default)(response, {
            statusCode: http_status_1.default.CREATED,
            message: "Message sent successfully",
            data: message,
        });
    }
    catch (error) {
        return (0, response_2.default)(response, error);
    }
}));
/**
 * Mark message as delivered
 * @route PUT /api/messages/:id/delivered
 */
MessageController.markAsDelivered = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const messageId = parseInt(request.params.id);
        const message = yield _a.messageService.markAsDelivered(messageId);
        return (0, response_1.default)(response, {
            statusCode: http_status_1.default.OK,
            message: "Message marked as delivered",
            data: message,
        });
    }
    catch (error) {
        return (0, response_2.default)(response, error);
    }
}));
/**
 * Mark message as read
 * @route PUT /api/messages/:id/read
 */
MessageController.markAsRead = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const messageId = parseInt(request.params.id);
        const message = yield _a.messageService.markAsRead(messageId);
        // Notify sender through socket if they're online
        const activeUsers = (0, socket_1.getActiveUsers)();
        const senderSocketId = activeUsers.get(message.sender_id);
        if (senderSocketId) {
            // Socket notification is handled in socket/index.ts
        }
        return (0, response_1.default)(response, {
            statusCode: http_status_1.default.OK,
            message: "Message marked as read",
            data: message,
        });
    }
    catch (error) {
        return (0, response_2.default)(response, error);
    }
}));
/**
 * Mark all messages in a chat as read
 * @route PUT /api/chats/:id/read-all
 */
MessageController.markAllAsRead = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const chatId = parseInt(request.params.id);
        const userId = request.decoded;
        // Verify chat exists and user has access
        const chat = yield _a.chatService.getChatById(chatId, userId);
        if (chat.user1_id !== userId && chat.user2_id !== userId) {
            return (0, response_2.default)(response, {
                statusCode: http_status_1.default.FORBIDDEN,
                message: "You do not have permission to access this chat",
            });
        }
        const count = yield _a.messageService.markAllAsRead(chatId, userId);
        return (0, response_1.default)(response, {
            statusCode: http_status_1.default.OK,
            message: `${count} messages marked as read`,
            data: { count },
        });
    }
    catch (error) {
        return (0, response_2.default)(response, error);
    }
}));
exports.default = MessageController;
