"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const bcrypt_1 = __importDefault(require("bcrypt"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
const sequelize_1 = require("sequelize");
const user_model_1 = __importDefault(require("../../database/models/user.model"));
const user_verifications_model_1 = __importDefault(require("../../database/models/user_verifications.model"));
const user_gallery_model_1 = __importDefault(require("../../database/models/user_gallery.model"));
const user_subscriptions_model_1 = __importDefault(require("../../database/models/user_subscriptions.model"));
const subscription_plans_mode_1 = __importDefault(require("../../database/models/subscription_plans.mode"));
class UserService {
    constructor() { }
}
_a = UserService;
UserService.isPasswordMatch = (user, password) => __awaiter(void 0, void 0, void 0, function* () { return yield bcrypt_1.default.compare(password, user.password); });
/**
 * Login with username and password
 * @param {string} email
 * @param {string} password
 * @returns {Promise<User>}
 */
UserService.loginUserWithEmailAndPassword = (email, password) => __awaiter(void 0, void 0, void 0, function* () {
    const user = yield _a.getUserByEmail(email, {
        shouldReturnPassword: true,
    }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    if (!user) {
        throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.LOGIN.INCORRECT_EMAIL);
    }
    if (!user || !(yield _a.isPasswordMatch(user, password))) {
        throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.LOGIN.INCORRECT_PASS);
    }
    user.password = undefined;
    return user;
});
/**
 * Get user by email
 * @param {string} email
 * @param {any} options
 * @returns {Promise<User>}
 */
UserService.getUserByEmail = (email_1, ...args_1) => __awaiter(void 0, [email_1, ...args_1], void 0, function* (email, options = {}) {
    const { shouldReturnPassword = false } = options;
    let excludeAttr = [];
    if (!shouldReturnPassword) {
        excludeAttr = ["password"];
    }
    return user_model_1.default.findOne({
        where: { email },
        attributes: {
            exclude: excludeAttr,
        },
    });
});
UserService.getUserByPhone = (phone) => __awaiter(void 0, void 0, void 0, function* () {
    return user_model_1.default.findOne({
        where: { phone },
        attributes: {
            exclude: ["password"],
        },
    });
});
/**
 * Get user
 * @param {any} whereOptions
 * @returns {Promise<User>}
 */
UserService.getUser = (...args_1) => __awaiter(void 0, [...args_1], void 0, function* (whereOptions = {}) {
    return user_model_1.default.findOne({
        where: whereOptions,
        attributes: {
            exclude: ["password"],
        },
    });
});
/**
 * Create a user
 * @param {Object} userBody
 * @returns {Promise<User>}
 */
UserService.createUser = (userBody) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        if (userBody.password !== userBody.confirmPassword) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.REGISTER.PASSWORD_MISMATCH);
        }
        else if (yield _a.getUserByEmail(userBody.email)) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.REGISTER.EMAIL_ALREADY_TAKEN);
        }
        else if (userBody.phone &&
            (yield _a.getUserByPhone(userBody.phone))) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.REGISTER.PHONE_ALREADY_TAKEN);
        }
        const user = yield user_model_1.default.create(userBody);
        let verificationBody = {
            user_id: user.id,
            email: user.email,
            is_email_verified: false,
            phone: user.phone,
            phone_code: user.phone_code,
        };
        yield user_verifications_model_1.default.create(verificationBody);
        return _a.getUserByEmail(user.email).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        const errorMessage = ((_b = error.original) === null || _b === void 0 ? void 0 : _b.message) || error.message || "Unknown error occurred";
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage);
    }
});
// static getRole = async (roleName: string) => {
//   return await Role.findOne({
//     where: { roleName },
//   });
// };
/**
 * Return users
 * @param {Object} options
 * @param {number} [options.page] - Current page number (optional)
 * @param {number} [options.limit] - Number of items per page (optional)
 * @param {string} [options.search] - Search term for filtering (optional)
 * @returns {Promise<User[]>}
 */
UserService.getUsers = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, status } = options;
        const whereCondition = search
            ? {
                [sequelize_1.Op.or]: [
                    { email: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                    { first_name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                    { last_name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                ],
            }
            : {};
        let verificationWhereCondition = {};
        if (status) {
            whereCondition.status = status;
        }
        const queryOption = {
            where: whereCondition,
            include: [
                {
                    model: user_verifications_model_1.default,
                    as: "verification",
                    where: verificationWhereCondition,
                    attributes: [],
                },
            ],
            attributes: { exclude: ["password"] },
            order: [["createdAt", "DESC"]],
        };
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const totalItems = yield user_model_1.default.count(queryOption);
        const users = yield user_model_1.default.findAll(queryOption);
        if (page && limit) {
            return {
                totalItems: totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                users: users,
            };
        }
        else {
            return users;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get user by id
 * @param {Number} id
 * @returns {Promise<User>}
 */
UserService.getUserById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return user_model_1.default.findOne({
            where: { id },
            attributes: { exclude: ["password"] },
            include: [
                {
                    model: user_subscriptions_model_1.default,
                    as: "userSubscription",
                    where: {
                        is_active: true,
                    },
                    limit: 1,
                    include: [
                        {
                            model: subscription_plans_mode_1.default,
                            as: "plan",
                            attributes: ["name"],
                        },
                    ],
                },
            ],
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get user by id
 * @param {number} id
 * @returns {Promise<User>}
 */
UserService.getUserByIdWithRoles = (id) => __awaiter(void 0, void 0, void 0, function* () {
    return user_model_1.default.findOne({
        where: { id },
    }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
});
/**
 * Update user by id
 * @param {Number} userId
 * @param {Object} updateBody
 * @returns {Promise<User>}
 */
UserService.updateUserById = (userId, updateBody) => __awaiter(void 0, void 0, void 0, function* () {
    const user = yield user_model_1.default.findByPk(userId);
    if (!user) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER.NOT_FOUND);
    }
    // if (user.role_id == 1) {
    //   throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER.NOT_UPDATE);
    // }
    // if (updateBody.email) {
    //   const userByEmail: any = await this.getUserByEmail(updateBody.email);
    //   if (userId === userByEmail.id) {
    //     throw new ApiError(httpStatus.BAD_REQUEST, "Email already taken");
    //   }
    // }
    Object.assign(user, updateBody);
    yield user.save();
    return user;
});
/**
 * Update user by id
 * @param {Number} userId
 * @param {Object} userProfile
 * @returns {Promise<User>}
 */
UserService.updateUserByProfile = (userId, userProfile) => __awaiter(void 0, void 0, void 0, function* () {
    if (userProfile.email) {
        const fetchedUser = yield _a.getUserByEmail(userProfile.email);
        if (fetchedUser.id != userId) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.REGISTER.EMAIL_ALREADY_TAKEN);
        }
    }
    const user = yield _a.getUserById(userId);
    if (!user) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "User not found");
    }
    Object.assign(user, userProfile);
    const updatedUser = Object.assign(yield user.save());
    updatedUser.password = undefined;
    return updatedUser;
});
/**
 * Delete user by id
 * @param {Number} userId
 * @returns {Promise<User>}
 */
UserService.deleteUserById = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_model_1.default.findByPk(userId);
        if (!user) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "User not found");
        }
        yield user.destroy();
        return user;
    }
    catch (error) {
        throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting User.");
    }
});
/**
 * Update password
 * @param {string} userId
 * @param {string} currentPassword
 * @param {string} newPassword
 * @returns {Promise<User>}
 */
UserService.updatePassword = (userId, currentPassword, newPassword) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_model_1.default.findByPk(userId);
        if (!user) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "User not found");
        }
        if (!(yield _a.isPasswordMatch(user, currentPassword))) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.USER.PROFILE.PASSWORD_DOES_NOT_MATCH);
        }
        const bcryptedPassword = yield bcrypt_1.default.hash(newPassword, 10);
        Object.assign(user, { password: bcryptedPassword });
        return yield user.save();
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Change email
 * @param {string} userId
 * @param {string} newEmail
 * @returns {Promise<User>}
 */
UserService.changeEmail = (userId, newEmail) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        let whereCondition = { email: newEmail };
        if (userId) {
            whereCondition.id = { [sequelize_1.Op.ne]: userId };
        }
        const userByEmail = yield user_model_1.default.findOne({
            where: whereCondition,
        });
        if (userByEmail) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.REGISTER.EMAIL_ALREADY_TAKEN);
        }
        const user = yield user_model_1.default.findByPk(userId);
        if (!user) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "User not found");
        }
        Object.assign(user, { email: newEmail });
        return yield user.save();
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
UserService.completeProfile = (userProfileBody) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const updatedUser = yield _a.updateUserById(userProfileBody.user_id, {
            profile_image: userProfileBody.profile_image,
            profile_bio: userProfileBody.profile_bio,
        });
        const gallery_images = Object.keys(userProfileBody)
            .filter((key) => key.startsWith("gallery_image["))
            .map((key) => userProfileBody[key]);
        if (gallery_images === null || gallery_images === void 0 ? void 0 : gallery_images.length) {
            let payload = [];
            for (let index = 0; index < gallery_images.length; index++) {
                const element = gallery_images[index];
                payload.push({
                    user_id: userProfileBody.user_id,
                    gallery_image: element,
                });
            }
            yield user_gallery_model_1.default.bulkCreate(payload);
        }
        yield user_verifications_model_1.default.update({ is_profile_completed: true }, { where: { user_id: userProfileBody.user_id } });
        return updatedUser;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = UserService;
