"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const sequelize_1 = require("sequelize");
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../../config/httpMessages"));
const user_shortlist_model_1 = __importDefault(require("../../../database/models/user_shortlist.model"));
const user_basic_details_model_1 = __importDefault(require("../../../database/models/user_basic_details.model"));
const user_location_details_model_1 = __importDefault(require("../../../database/models/user_location_details.model"));
const user_education_career_model_1 = __importDefault(require("../../../database/models/user_education_career.model"));
const user_lifestyle_model_1 = __importDefault(require("../../../database/models/user_lifestyle.model"));
const user_family_details_model_1 = __importDefault(require("../../../database/models/user_family_details.model"));
const user_hobbies_model_1 = __importDefault(require("../../../database/models/user_hobbies.model"));
const user_gallery_model_1 = __importDefault(require("../../../database/models/user_gallery.model"));
const user_model_1 = __importDefault(require("../../../database/models/user.model"));
const country_model_1 = __importDefault(require("../../../database/models/country.model"));
const city_model_1 = __importDefault(require("../../../database/models/city.model"));
class UserNearbyService {
    constructor() { }
}
_a = UserNearbyService;
/**
* Return Users
* @param {Object} options
* @param {number} [options.page] - Current page number (optional)
* @param {number} [options.limit] - Number of items per page (optional)
* @param {string} [options.search] - Search term for filtering (optional)
* @returns {Promise<Role[]>}
*/
UserNearbyService.getUsersByLocation = (options, userId) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c;
    try {
        const { page, limit, search } = options;
        const whereCondition = {};
        const currentUser = yield user_model_1.default.findOne({
            where: { id: userId },
            attributes: ["gender"],
            include: [
                {
                    model: user_location_details_model_1.default,
                    as: "locationDetails",
                    attributes: ["city", "country_living_in"]
                },
            ],
        });
        if (!currentUser) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.USER.NOT_FOUND);
        }
        whereCondition.id = { [sequelize_1.Op.ne]: userId };
        if ((currentUser === null || currentUser === void 0 ? void 0 : currentUser.gender) === 'male') {
            whereCondition.gender = 'female';
        }
        else {
            whereCondition.gender = 'male';
        }
        whereCondition.status = 'active';
        whereCondition.is_hide_profile = false;
        const locationWhere = {};
        if ((_b = currentUser === null || currentUser === void 0 ? void 0 : currentUser.locationDetails) === null || _b === void 0 ? void 0 : _b.city) {
            locationWhere.city = (_c = currentUser === null || currentUser === void 0 ? void 0 : currentUser.locationDetails) === null || _c === void 0 ? void 0 : _c.city;
        }
        const queryOption = {
            where: whereCondition,
            distinct: true,
            include: [
                {
                    model: user_basic_details_model_1.default,
                    as: "basicDetails",
                    attributes: ["religion", "caste", "gotra", 'marital_status']
                },
                {
                    model: user_location_details_model_1.default,
                    as: "locationDetails",
                    where: Object.keys(locationWhere).length ? locationWhere : undefined,
                    attributes: ["city", "country_living_in"],
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ['name'],
                        },
                        {
                            model: city_model_1.default,
                            as: "cities",
                            attributes: ['name'],
                        },
                    ],
                },
                {
                    model: user_education_career_model_1.default,
                    as: "educationCareer",
                    attributes: ["education", "profession"]
                },
                {
                    model: user_lifestyle_model_1.default,
                    as: "lifestyle",
                    attributes: ["age", "height_cm"]
                },
                {
                    model: user_family_details_model_1.default,
                    as: "familyDetails",
                    attributes: ["family_type", "father_occupation", "mother_occupation"]
                },
                {
                    model: user_hobbies_model_1.default,
                    as: "hobbies",
                    attributes: ["hobbies", "interests"]
                },
                {
                    model: user_gallery_model_1.default,
                    as: "userGallery",
                },
                {
                    model: user_shortlist_model_1.default,
                    as: "shortlisted_user"
                }
            ],
            order: [["createdAt", "DESC"]],
        };
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const shortlists = yield user_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: shortlists.count,
                totalPages: Math.ceil(shortlists.count / limit),
                currentPage: page,
                shortlists: shortlists.rows,
            };
        }
        else {
            return shortlists.rows;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = UserNearbyService;
