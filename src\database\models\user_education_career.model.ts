import {
    Table,
    Column,
    Model,
    DataType,
    PrimaryKey,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    Index,
} from "sequelize-typescript";
import User from "./user.model"; 

interface UserEducationCareerI {
    id: number;
    user_id: number;
    education: string;
    employment_status: string;
    profession?: string;
    other_profession?: string;
    working_for?: string;
    annual_salary: string;
}

@Table({
    tableName: "user_education_career",
    timestamps: false, 
})
class UserEducationCareer extends Model<UserEducationCareerI> implements UserEducationCareerI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index 
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    education: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    employment_status: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    profession?: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    other_profession?: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    working_for?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    annual_salary: string;
}

export default UserEducationCareer;