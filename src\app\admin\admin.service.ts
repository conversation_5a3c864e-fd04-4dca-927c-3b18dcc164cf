import httpStatus from "http-status";
import bcrypt from "bcrypt";

import ApiError from "../../utils/ApiError";
import httpMessages from "../../config/httpMessages";
import { Op } from "sequelize";
import Admin from "../../database/models/admin.model";
import Role from "../../database/models/role.model";

export default class AdminService {
  constructor() { }

  static isPasswordMatch = async (user: any, password: string) =>
    await bcrypt.compare(password, user.password);

  /**
   * Login with username and password
   * @param {string} username
   * @param {string} password
   * @returns {Promise<Admin>}
   */
  static loginUserWithUsernameAndPassword = async (
    username: string,
    password: string
  ) => {
    const admin = await this.getAdminByusername(username, {
      shouldReturnPassword: true,
    }).then((data: any) => data?.toJSON());

    if (!admin) {
      throw new ApiError(
        httpStatus.UNAUTHORIZED,
        httpMessages.LOGIN.INCORRECT_EMAIL
      );
    }

    if (!admin || !(await this.isPasswordMatch(admin, password))) {
      throw new ApiError(
        httpStatus.UNAUTHORIZED,
        httpMessages.LOGIN.INCORRECT_PASS
      );
    }
    admin.password = undefined;
    return admin;
  };

  /**
   * Get Admin by username
   * @param {string} username
   * @param {any} options
   * @returns {Promise<Admin>}
   */
  static getAdminByusername = async (username: string, options: any = {}) => {
    const { shouldReturnPassword = false } = options;

    let excludeAttr: string[] = [];
    if (!shouldReturnPassword) {
      excludeAttr = ["password"];
    }

    return Admin.findOne({
      where: { username },
      attributes: {
        exclude: excludeAttr,
      },
    });
  };

  /**
   * Create a Admin
   * @param {Object} adminBody
   * @returns {Promise<Admin>}
   */
  static createAdmin = async (adminBody: any) => {
    try {
      if (!adminBody.password) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.REGISTER.PASSWORD_MISMATCH
        );
      } else if (await this.getAdminByusername(adminBody.username)) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.REGISTER.EMAIL_ALREADY_TAKEN
        );
      }

      const admin: Admin = await Admin.create(adminBody);

      return this.getAdminByusername(admin.username).then((data: any) =>
        data?.toJSON()
      );
    } catch (error: any) {
      const errorMessage = error.original?.message || error.message || 'Unknown error occurred';
      throw new ApiError(httpStatus.BAD_REQUEST, errorMessage);
    }
  };

  /**
   * Return admins
   * @param {Object} options
   * @param {number} [options.page] - Current page number (optional)
   * @param {number} [options.limit] - Number of items per page (optional)
   * @param {string} [options.search] - Search term for filtering (optional)
   * @returns {Promise<Admin[]>}
   */
  static getAdmins = async (options: {
    page?: number;
    limit?: number;
    search?: string;
  }) => {
    try {
      const { page, limit, search } = options;
      const whereCondition = search
        ? {
          [Op.or]: [
            { first_name: { [Op.like]: `%${search.toLowerCase()}%` } },
            { last_name: { [Op.like]: `%${search.toLowerCase()}%` } },
            { email: { [Op.like]: `%${search.toLowerCase()}%` } },
            { username: { [Op.like]: `%${search.toLowerCase()}%` } },
          ],
        }
        : {};

      const queryOption: any = {
        where: whereCondition,
        include: [
          {
            model: Role,
            as: "role",
            attributes: ["role_name"],
          },
        ],
        attributes: { exclude: ["password"] },
        order: [["createdAt", "DESC"]],
      };
      // If pagination is provided, apply pagination
      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }
      const admins = await Admin.findAndCountAll(queryOption);
      if (page && limit) {
        return {
          totalItems: admins.count,
          totalPages: Math.ceil(admins.count / limit),
          currentPage: page,
          admins: admins.rows,
        };
      } else {
        return admins.rows;
      }
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get admin by id
   * @param {Number} id
   * @returns {Promise<Admin>}
   */
  static getAdminById = async (id: number) => {
    try {
      return Admin.findOne({
        where: { id },
        attributes: { exclude: ["password"] },
      }).then((data: any) => {
        if (!data) return null;

        const jsonData = data.toJSON();
        jsonData.permissions =
          typeof jsonData.permissions === "string"
            ? JSON.parse(jsonData.permissions)
            : jsonData.permissions;

        return jsonData;
      });
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Update admin by id
   * @param {Number} adminId
   * @param {Object} updateBody
   * @returns {Promise<Admin>}
   */
  static updateAdminById = async (adminId: number, updateBody: any) => {
    try {
      const admin = await Admin.findByPk(adminId);
      if (!admin) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER.NOT_FOUND);
      }
      if (updateBody.password) {
        updateBody.password = await bcrypt.hash(updateBody.password, 10);
      }
      Object.assign(admin, updateBody);
      await admin.save();
      return admin;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }

  };

  /**
   * Delete admin by id
   * @param {Number} adminId
   * @returns {Promise<Admin>}
   */
  static deleteAdminById = async (adminId: number) => {
    try {
      const admin: any = await Admin.findByPk(adminId);
      if (!admin) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER.NOT_FOUND);
      }
      await admin.destroy();
      return admin;
    } catch (error: any) {
      throw new ApiError(
        error.status || httpStatus.BAD_REQUEST,
        error.message || "Error deleting admin."
      );
    }
  };
}
