'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create khalti_payments table
    await queryInterface.createTable('khalti_payments', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      plan_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'subscription_plans',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      khalti_payment_id: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      khalti_transaction_id: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      khalti_order_id: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      pidx: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      currency: {
        type: Sequelize.STRING(3),
        allowNull: false,
        defaultValue: 'NPR'
      },
      status: {
        type: Sequelize.ENUM('pending', 'completed', 'failed', 'cancelled', 'refunded', 'expired'),
        allowNull: false,
        defaultValue: 'pending'
      },
      payment_method: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'khalti'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      khalti_response: {
        type: Sequelize.JSON,
        allowNull: true
      },
      refund_id: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      refund_amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      refund_reason: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      webhook_event_id: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      mobile: {
        type: Sequelize.STRING(20),
        allowNull: true
      },
      product_identity: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      product_name: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      product_url: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      return_url: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      website_url: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Create khalti_webhook_events table
    await queryInterface.createTable('khalti_webhook_events', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      event_id: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      event_type: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      resource_type: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      resource_id: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      summary: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      event_data: {
        type: Sequelize.JSON,
        allowNull: false
      },
      processed: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      processed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      error_message: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('khalti_payments', ['user_id']);
    await queryInterface.addIndex('khalti_payments', ['plan_id']);
    await queryInterface.addIndex('khalti_payments', ['pidx']);
    await queryInterface.addIndex('khalti_payments', ['status']);
    await queryInterface.addIndex('khalti_payments', ['khalti_transaction_id']);
    await queryInterface.addIndex('khalti_payments', ['created_at']);

    await queryInterface.addIndex('khalti_webhook_events', ['event_id']);
    await queryInterface.addIndex('khalti_webhook_events', ['event_type']);
    await queryInterface.addIndex('khalti_webhook_events', ['resource_id']);
    await queryInterface.addIndex('khalti_webhook_events', ['processed']);
    await queryInterface.addIndex('khalti_webhook_events', ['created_at']);
  },

  down: async (queryInterface, Sequelize) => {
    // Drop tables
    await queryInterface.dropTable('khalti_webhook_events');
    await queryInterface.dropTable('khalti_payments');
  }
};
