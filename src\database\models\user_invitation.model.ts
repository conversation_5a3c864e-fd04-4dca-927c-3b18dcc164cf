import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  AllowNull,
  ForeignKey,
  BelongsTo,
  Default
} from "sequelize-typescript";
import User from "./user.model";

export interface UserInvitationI {
  id: number;
  sender_id: number;
  receiver_id: number;
  message: string;
  status: "pending" | "accepted" | "declined";
  is_read: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

@Table({
  tableName: "user_invitations",
  timestamps: true,
})
class UserInvitation extends Model<UserInvitationI> implements UserInvitationI {
  @PrimaryKey
  @AutoIncrement
  @Column
  id: number;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column
  sender_id: number;

  @BelongsTo(() => User, { foreignKey: 'sender_id',  onDelete: "CASCADE" })
  sender: User;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column
  receiver_id: number;

  @BelongsTo(() => User, { foreignKey: 'receiver_id', onDelete: "CASCADE" })
  receiver: User;

  @Column(DataType.TEXT)
  message: string;

  @Default("pending")
  @Column(DataType.ENUM("pending", "accepted", "declined"))
  status: "pending" | "accepted" | "declined";

  @Default(false)
  @Column(DataType.BOOLEAN)
  is_read: boolean;
}

export default UserInvitation;