"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middlewares/auth");
const dashboard_controller_1 = __importDefault(require("../app/Dashboard/dashboard.controller"));
const router = express_1.default.Router();
router.get("", auth_1.auth, dashboard_controller_1.default.getDashboardData);
router.get("/admin", auth_1.auth, dashboard_controller_1.default.getAdminDashboardData);
exports.default = router;
