"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middlewares/auth");
const message_controller_1 = __importDefault(require("../app/message/message.controller"));
const router = express_1.default.Router();
// Apply auth middleware to all message routes
router.use(auth_1.auth);
// Message routes
router.post("/", message_controller_1.default.sendMessage);
router.put("/:id/delivered", message_controller_1.default.markAsDelivered);
router.put("/:id/read", message_controller_1.default.markAsRead);
exports.default = router;
