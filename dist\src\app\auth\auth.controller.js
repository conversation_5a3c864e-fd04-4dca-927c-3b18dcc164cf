"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const response_1 = __importStar(require("../../utils/response"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
const auth_service_1 = __importDefault(require("./auth.service"));
const user_service_1 = __importDefault(require("../user/user.service"));
const token_service_1 = __importDefault(require("../../common/services/token.service"));
const email_service_1 = __importDefault(require("../../common/services/email.service"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
class AuthController {
    constructor() { }
}
_a = AuthController;
AuthController.authService = auth_service_1.default;
AuthController.userService = user_service_1.default;
AuthController.tokenService = token_service_1.default;
AuthController.emailService = email_service_1.default;
AuthController.register = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userData = Object.assign({}, request.body);
        const user = yield _a.userService.createUser(userData);
        // const email_tokens = await this.tokenService.generateVerifyEmailToken(user);
        // await this.emailService.sendVerificationEmail(user.email, email_tokens); //TODO email verfication apply
        try {
            yield _a.emailService.sendOtpForVerificationEmail(user.email, user.id);
        }
        catch (error) {
            console.log('error: ', error);
        }
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.CREATED,
            message: httpMessages_1.default.REGISTER.SUCCESS,
            data: null,
        });
    }
    catch (error) {
        console.log('error: ', error);
        return (0, response_1.default)(response, error);
    }
}));
AuthController.login = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, password } = request.body;
        const user = yield _a.authService.loginUserWithEmailAndPassword(email, password);
        if (!user.verification.is_email_verified) {
            yield _a.emailService.sendOtpForVerificationEmail(user.email, user.id);
        }
        const tokens = yield _a.tokenService.generateAuthTokens(user);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.LOGIN.SUCCESS,
            data: Object.assign(Object.assign({}, user), { token: tokens }),
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
AuthController.logout = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { refreshToken } = request.body;
        yield _a.authService.logout(refreshToken);
        return response.status(http_status_1.default.NO_CONTENT).send();
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
AuthController.refreshTokens = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const tokens = yield _a.authService.refreshAuth(request.body.refreshToken);
        const data = Object.assign({}, tokens);
        return response.status(http_status_1.default.CREATED).send({
            success: true,
            message: httpMessages_1.default.REFRESH_TOKEN_SUCCESS,
            data
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
AuthController.forgotPassword = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const resetPasswordToken = yield _a.tokenService.generateResetPasswordToken(request.body.email);
        console.log(">>>>>> forgotPassword: ", {
            email: request.body.email,
            resetPasswordToken
        });
        yield _a.emailService.sendResetPasswordEmail(request.body.email, resetPasswordToken);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.PROFILE.RESET_PASSWORD_EMAIL_SENT,
            data: null,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
AuthController.resetPassword = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { body, query } = request;
        const { token } = query;
        yield _a.authService.resetPassword(token, body.password || "");
        return response.status(http_status_1.default.OK).send({
            success: true,
            message: httpMessages_1.default.USER.PROFILE.RESET_PASSWORD_SUCCESS
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
AuthController.verifyEmailOtp = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, otp } = request.body;
        let user = yield _a.emailService.verifyEmailOtp(email, otp);
        const tokens = yield _a.tokenService.generateAuthTokens(user);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.OTP.EMAIL_OTP_VERIFIED,
            data: Object.assign(Object.assign({}, user), { token: tokens }),
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
AuthController.resendEmailOtp = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = request.body;
        let user = yield _a.userService.getUserByEmail(email);
        if (!user) {
            return (0, response_1.default)(response, new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.USER.EMAIL_NOT_FOUND));
        }
        yield _a.emailService.sendOtpForVerificationEmail(email, user.id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.OTP.RESENT_OTP,
            data: null,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = AuthController;
