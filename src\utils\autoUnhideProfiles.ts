import moment from "moment";
import PrivacySetting from "../database/models/privacy_settings.model";
import UserService from "../app/user/user.service";
import { Op, Sequelize } from "sequelize";

/**
 * Function to automatically unhide profiles whose visibility period has expired
 * This should be called by a cron job or scheduled task at midnight
 */
export const runAutoUnhideProfiles = async () => {
  try {
    // Get current date at midnight
    const today = moment().startOf('day').toDate();
    
    // Find all privacy settings where profile_end_date has passed
    const expiredSettings = await PrivacySetting.findAll({
      where: {
        profile_end_date: {
          [Op.lt]: today
        },
        // Only consider profiles that are currently hidden
        user_id: {
          [Op.in]: Sequelize.literal(
            `(SELECT id FROM users WHERE is_hide_profile = true)`
          )
        }
      }
    });

    let unhiddenCount = 0;

    // Update each user to unhide their profile
    for (const setting of expiredSettings) {
      await UserService.updateUserById(setting.user_id, { is_hide_profile: false });
      await setting.update({ profile_end_date: null, profile_start_date: null, profile_visibility: 0 });
      unhiddenCount++;
    }

    console.log(`Auto-unhidden ${unhiddenCount} expired profiles`);
    return unhiddenCount;
  } catch (error) {
    console.error('Error auto-unhiding profiles:', error);
    throw error;
  }
};