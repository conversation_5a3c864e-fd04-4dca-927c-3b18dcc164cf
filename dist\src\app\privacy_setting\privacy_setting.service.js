"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const sequelize_1 = require("sequelize");
const privacy_settings_model_1 = __importDefault(require("../../database/models/privacy_settings.model"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
const user_service_1 = __importDefault(require("../user/user.service"));
const user_verifications_model_1 = __importDefault(require("../../database/models/user_verifications.model"));
const axios_1 = __importDefault(require("axios"));
class PrivacySettingService {
    constructor() { }
}
_a = PrivacySettingService;
PrivacySettingService.userService = user_service_1.default;
/**
 * Create a PrivacySetting
 * @param {Object} body
 * @returns {Promise<PrivacySetting>}
 */
PrivacySettingService.createPrivacySetting = (body) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const existingSetting = yield privacy_settings_model_1.default.findOne({
            where: { user_id: body.user_id },
        });
        if (existingSetting) {
            yield existingSetting.update(body);
            return existingSetting;
        }
        const details = yield privacy_settings_model_1.default.create(body);
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Return PrivacySettings
 * @param {Object} options
 * @param {number} [options.page] - Current page number (optional)
 * @param {number} [options.limit] - Number of items per page (optional)
 * @param {string} [options.search] - Search term for filtering (optional)
 * @returns {Promise<PrivacySetting[]>}
 */
PrivacySettingService.getPrivacySettings = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search } = options;
        const whereCondition = search
            ? {
                [sequelize_1.Op.or]: [
                    { role_name: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                ],
            }
            : {};
        if (options.userId) {
            whereCondition.user_id = options.userId;
        }
        const queryOption = {
            where: whereCondition,
            order: [["createdAt", "DESC"]],
        };
        // If pagination is provided, apply pagination
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const data = yield privacy_settings_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: data.count,
                totalPages: Math.ceil(data.count / limit),
                currentPage: page,
                subscription_plans: data.rows,
            };
        }
        else {
            return data.rows[0];
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get PrivacySetting by id
 * @param {Number} id
 * @returns {Promise<PrivacySetting>}
 */
PrivacySettingService.getPrivacySettingById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return privacy_settings_model_1.default.findOne({
            where: { id },
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update role by id
 * @param {Number} Id
 * @param {Object} updateBody
 * @returns {Promise<Role>}
 */
PrivacySettingService.updatePrivacySettingById = (Id, updateBody) => __awaiter(void 0, void 0, void 0, function* () {
    const details = yield privacy_settings_model_1.default.findByPk(Id);
    if (!details) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER_SUBSCRIPTION.NOT_FOUND);
    }
    Object.assign(details, updateBody);
    yield details.save();
    return details;
});
/**
 * Delete role by id
 * @param {Number} Id
 * @returns {Promise<Role>}
 */
PrivacySettingService.deletePrivacySettingById = (Id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const details = yield privacy_settings_model_1.default.findByPk(Id);
        if (!details) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.ROLES.NOT_FOUND);
        }
        yield details.destroy();
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting Role.");
    }
});
PrivacySettingService.hideProfile = (body) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const existingSetting = yield privacy_settings_model_1.default.findOne({
            where: { user_id: body.user_id },
        });
        if (body.profile_visibility) {
            body['profile_start_date'] = new Date();
            let endDate = new Date();
            endDate.setDate(endDate.getDate() + parseInt(body.profile_visibility));
            console.log('endDate: ', endDate);
            body['profile_end_date'] = endDate;
        }
        if (existingSetting) {
            yield existingSetting.update(body);
            _a.userService.updateUserById(body.user_id, { is_hide_profile: true });
            return existingSetting;
        }
        const details = yield privacy_settings_model_1.default.create(body);
        _a.userService.updateUserById(body.user_id, { is_hide_profile: true });
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
PrivacySettingService.deleteProfile = (body) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const existingSetting = yield privacy_settings_model_1.default.findOne({
            where: { user_id: body.user_id },
        });
        if (existingSetting) {
            yield existingSetting.update(body);
            _a.userService.deleteUserById(body.user_id);
            return existingSetting;
        }
        const details = yield privacy_settings_model_1.default.create(body);
        _a.userService.deleteUserById(body.user_id);
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
* Send phone otp
* @param {Number} userId
* @param {String} phone
* @returns {Promise<UserVerification>}
*/
PrivacySettingService.sendPhoneOtp = (userId, phone, phone_code) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const checkDublicate = yield _a.userService.getUserByPhone(phone);
        if (checkDublicate) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.REGISTER.PHONE_ALREADY_TAKEN);
        }
        const verification = yield user_verifications_model_1.default.findOne({
            where: { user_id: userId },
        });
        if (!verification) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "User not found");
        }
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        const otpExpiry = new Date(Date.now() + 5 * 60 * 1000);
        if (phone_code === "+977") {
            const url = 'https://api.sparrowsms.com/v2/sms/';
            const fullNumber = phone;
            const localNumber = fullNumber.replace("+977", "");
            const response = yield axios_1.default.post(url, {
                token: process.env.SPARROW_TOKEN,
                from: process.env.SPARROW_FROM,
                to: localNumber,
                text: `Your verification code is ${otp}. Please enter the code to verify your mobile. Thank you Barbadhu`,
            });
            console.log('response: ', response.status);
        }
        else {
            const client = require('twilio')(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
            // Send OTP via Twilio
            const message = yield client.messages.create({
                body: `Your verification code is ${otp}. Please enter the code to verify your mobile. Thank you Barbadhu`,
                to: phone_code + phone,
                from: process.env.TWILIO_PHONE_NUMBER,
            });
            console.log('message: ', message);
        }
        yield verification.update({ phone_otp: otp, phone_otp_expiry: otpExpiry });
        return verification;
    }
    catch (error) {
        console.log('error: ', error);
        if (error.status === 400) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Invalid phone number");
        }
        else if (error.status === 403) {
            throw new ApiError_1.default(http_status_1.default.FORBIDDEN, "Internal server error");
        }
        else {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
        }
    }
});
PrivacySettingService.verifyPhoneOtp = (userId, phone, otp, phone_code) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const verification = yield user_verifications_model_1.default.findOne({
            where: { user_id: userId },
        });
        if (!verification) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Invalid OTP");
        }
        if (verification.phone_otp !== otp) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.OTP.INVALID_OTP);
        }
        ;
        if (new Date() > new Date(verification.phone_otp_expiry)) {
            throw new ApiError_1.default(http_status_1.default.GONE, httpMessages_1.default.OTP.INVALID_OTP_EXPIRED);
        }
        ;
        yield verification.update({ phone: phone, phone_code: phone_code, phone_otp: '', phone_otp_expiry: null, is_phone_verified: true });
        yield _a.userService.updateUserById(userId, { phone: phone, phone_code: phone_code });
        return verification;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = PrivacySettingService;
