import httpStatus from "http-status";
import { Op, Sequelize, where } from "sequelize";
import UserSubscriptions from "../../../database/models/user_subscriptions.model";
import UserSubscription from "../../../database/models/user_subscriptions.model";
import ApiError from "../../../utils/ApiError";
import httpMessages from "../../../config/httpMessages";
import SubscriptionPlan from "../../../database/models/subscription_plans.mode";
import User from "../../../database/models/user.model";

export default class UserSubscriptionService {
    constructor() { }

    /**
     * Create a UserSubscription
     * @param {Object} body
     * @returns {Promise<UserSubscription>}
     */
    static createUserSubscription = async (body: any) => {
        try {
            const details: UserSubscriptions = await UserSubscription.create(body);
            return details;
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Return UserSubscriptions
     * @param {Object} options
     * @param {number} [options.page] - Current page number (optional)
     * @param {number} [options.limit] - Number of items per page (optional)
     * @param {string} [options.search] - Search term for filtering (optional)
     * @returns {Promise<UserSubscription[]>}
     */
    static getUserSubscriptions = async (options: {
        page?: number;
        limit?: number;
        search?: string;
        is_active?: boolean;
    }) => {
        try {
            const { page, limit, search, is_active } = options;
            const whereCondition: any = {};

            if (is_active !== undefined) {
                whereCondition.is_active = is_active;
            }


            const queryOption: any = {
                where: whereCondition,
                order: [["createdAt", "DESC"]],
                include: [
                    {
                        model: SubscriptionPlan,
                        as: "plan",
                    },
                    {
                        model: User,
                        as: "user",
                    }
                ],
            };

            if (search) {
                const escapedSearch = `%${search.toLowerCase()}%`;
                queryOption.where = {
                    ...whereCondition,
                    [Op.or]: [
                        Sequelize.literal(`LOWER(\`user\`.\`first_name\`) LIKE '${escapedSearch}'`),
                        Sequelize.literal(`LOWER(\`user\`.\`last_name\`) LIKE '${escapedSearch}'`),
                        Sequelize.literal(`LOWER(\`plan\`.\`name\`) LIKE '${escapedSearch}'`),
                    ]
                };
            }
            // If pagination is provided, apply pagination
            if (page && limit) {
                const offset = (page - 1) * limit;
                queryOption.limit = limit;
                queryOption.offset = offset;
            }
            const data = await UserSubscription.findAndCountAll(queryOption);
            if (page && limit) {
                return {
                    totalItems: data.count,
                    totalPages: Math.ceil(data.count / limit),
                    currentPage: page,
                    user_subscriptions: data.rows,
                };
            } else {
                return data.rows;
            }
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Get UserSubscription by id
     * @param {Number} id
     * @returns {Promise<UserSubscription>}
     */
    static getUserSubscriptionById = async (id: number) => {
        try {
            return UserSubscription.findOne({
                where: { id },
            }).then((data: any) => data?.toJSON());
        } catch (error: any) {
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
    };

    /**
     * Update role by id
     * @param {Number} Id
     * @param {Object} updateBody
     * @returns {Promise<Role>}
     */
    static updateUserSubscriptionById = async (Id: number, updateBody: any) => {
        const details = await UserSubscription.findByPk(Id);
        if (!details) {
            throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER_SUBSCRIPTION.NOT_FOUND);
        }

        Object.assign(details, updateBody);
        await details.save();
        return details;
    };

    /**
     * Delete role by id
     * @param {Number} Id
     * @returns {Promise<Role>}
     */
    static deleteUserSubscriptionById = async (Id: number) => {
        try {
            const details: any = await UserSubscription.findByPk(Id);
            if (!details) {
                throw new ApiError(httpStatus.NOT_FOUND, httpMessages.ROLES.NOT_FOUND);
            }
            await details.destroy();
            return details;
        } catch (error: any) {
            throw new ApiError(
                error.status || httpStatus.BAD_REQUEST,
                error.message || "Error deleting Role."
            );
        }
    };
}
