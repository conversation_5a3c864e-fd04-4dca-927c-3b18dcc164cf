"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const sequelize_1 = require("sequelize");
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../../config/httpMessages"));
const help_category_model_1 = __importDefault(require("../../../database/models/help_category.model"));
const help_question_model_1 = __importDefault(require("../../../database/models/help_question.model"));
class HelpCategoryService {
    constructor() { }
}
_a = HelpCategoryService;
/**
 * Create a HelpCategory
 * @param {Object} body
 * @returns {Promise<HelpCategory>}
 */
HelpCategoryService.createHelpCategory = (body) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (yield _a.getHelpCategoryByTitle(body.title)) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.HELP.NAME_ALREADY_TAKEN);
        }
        const helpCategory = yield help_category_model_1.default.create(body);
        return yield _a.getHelpCategoryById(helpCategory.id);
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get HelpCategory by title
 * @param {String} title
 * @returns {Promise<HelpCategory>}
 */
HelpCategoryService.getHelpCategoryByTitle = (title) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return help_category_model_1.default.findOne({
            where: { title },
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get all HelpCategories
 * @param {Object} option
 * @returns {Promise<HelpCategory[]>}
 */
HelpCategoryService.getHelpCategories = (option) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search } = option;
        const whereCondition = {};
        if (search) {
            whereCondition.title = { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` };
        }
        const queryOption = {
            where: whereCondition,
            order: [["createdAt", "DESC"]],
        };
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const helpCategories = yield help_category_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: helpCategories.count,
                totalPages: Math.ceil(helpCategories.count / limit),
                currentPage: page,
                helpCategories: helpCategories.rows,
            };
        }
        else {
            return helpCategories.rows;
        }
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get HelpCategory by id
 * @param {Number} id
 * @returns {Promise<HelpCategory>}
 */
HelpCategoryService.getHelpCategoryById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return help_category_model_1.default.findOne({
            where: { id },
            include: [
                {
                    model: help_question_model_1.default,
                    as: "questions",
                    attributes: ["question", "description"],
                },
            ],
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Update role by id
 * @param {Number} Id
 * @param {Object} updateBody
 * @returns {Promise<Role>}
 */
HelpCategoryService.updateHelpCategoryById = (Id, updateBody) => __awaiter(void 0, void 0, void 0, function* () {
    const details = yield help_category_model_1.default.findByPk(Id);
    if (!details) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.HELP.NOT_FOUND);
    }
    Object.assign(details, updateBody);
    yield details.save();
    return details;
});
/**
 * Delete role by id
 * @param {Number} Id
 * @returns {Promise<Role>}
 */
HelpCategoryService.deleteHelpCategoryById = (Id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const details = yield help_category_model_1.default.findByPk(Id);
        if (!details) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.HELP.NOT_FOUND);
        }
        yield details.destroy();
        return details;
    }
    catch (error) {
        throw new ApiError_1.default(error.status || http_status_1.default.BAD_REQUEST, error.message || "Error deleting Role.");
    }
});
exports.default = HelpCategoryService;
