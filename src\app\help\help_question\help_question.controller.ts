 import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../../utils/response";
import httpMessages from "../../../config/httpMessages";
import HelpQuestionService from "./help_question.service";
import ApiError from "../../../utils/ApiError";
import HelpCategoryService from "../help_category/help_category.service";

export default class HelpQuestionController {
  static helpQuestionService = HelpQuestionService;
  constructor() { }

  static getAll = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search } = request.query;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
      };
      const questions = await this.helpQuestionService.getHelpQuestions(option);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.HELP_QUESTION.SUCCESS,
        data: questions,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const body = request.body;
      const question = await this.helpQuestionService.createHelpQuestion(body);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.HELP_QUESTION.ADD_SUCCESS,
        data: question,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static showById = catchAsync(async (request: Request, response: Response) => {
    try {
      const questionId: number = parseInt(request.params.id, 10);
      const question = await this.helpQuestionService.getHelpQuestionById(questionId);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.HELP_QUESTION.DETAILS.SUCCESS,
        data: question,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static update = catchAsync(async (request: Request, response: Response) => {
    try {
      const questionId: number = parseInt(request.params.id, 10);
      const body = request.body;
      const question = await this.helpQuestionService.updateHelpQuestionById(questionId, body);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.HELP_QUESTION.UPDATE_SUCCESS,
        data: question,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static delete = catchAsync(async (request: Request, response: Response) => {
    try {
      const questionId: number = parseInt(request.params.id, 10);
      await this.helpQuestionService.deleteHelpQuestionById(questionId);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.HELP_QUESTION.DELETE,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });
}





