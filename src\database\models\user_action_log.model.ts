import {
    AllowNull,
    <PERSON>I<PERSON>rement,
    <PERSON>ongsTo,
    Column,
    DataType,
    ForeignKey,
    Model,
    PrimaryKey,
    Table,
    Index,
    Unique,
} from "sequelize-typescript";
import User from "./user.model";

export interface UserActionLogI {
    id?: number;
    user_id: number;
    action_type: "interest_sent" | "contact_viewed" | "profile_viewed" | "chat_initiated";
    target_user_id: number;
    subscription_id: number;
    action_date: Date;
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: "user_action_logs",
    timestamps: true,
    indexes: [
        {
            unique: true,
            fields: ['user_id', 'action_type', 'target_user_id', 'subscription_id'],
            name: 'unique_user_action_target_subscription'
        }
    ]
})
class UserActionLog extends Model<UserActionLogI> implements UserActionLogI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @AllowNull(false)
    @Column(DataType.ENUM("interest_sent", "contact_viewed", "profile_viewed", "chat_initiated"))
    action_type: "interest_sent" | "contact_viewed" | "profile_viewed" | "chat_initiated";

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    target_user_id: number;

    @AllowNull(false)
    @Column(DataType.INTEGER)
    subscription_id: number;

    @AllowNull(false)
    @Column(DataType.DATE)
    action_date: Date;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE" })
    user: User;

    @BelongsTo(() => User, { foreignKey: "target_user_id", onDelete: "CASCADE" })
    targetUser: User;
}

export default UserActionLog;
