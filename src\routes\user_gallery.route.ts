import express from "express";
import UserProfileController from "../app/user/user_profile/user_profile.controller";
import { auth } from "../middlewares/auth";
import { fileUploadMiddleware } from "../middlewares/fileUploadMiddleware";
import UserGalleryController from "../app/user/user_gallery/user_gallery.controller";


const router = express.Router();

router.get("",auth, UserGalleryController.getAll);
router.post("",auth,fileUploadMiddleware, UserGalleryController.create);
router.put("/:id",auth,fileUploadMiddleware, UserGalleryController.update);
router.get("/:id",auth, UserGalleryController.showById);
router.delete("/:id",auth, UserGalleryController.delete);

export default router;
