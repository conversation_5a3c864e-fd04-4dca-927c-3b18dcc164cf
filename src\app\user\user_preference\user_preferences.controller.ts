import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../../utils/response";
import httpMessages from "../../../config/httpMessages";
import ApiError from "../../../utils/ApiError";
import UserPreferenceService from "./user_preferences.service";
import UserPreference from "../../../database/models/user_preferences.model";

export default class UserPreferenceController {
  static userPreferenceService = UserPreferenceService;
  constructor() { }

  static getAll = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search } = request.query;
      const decoded = request.decoded;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
      };
      const list = await this.userPreferenceService.getMatchingUsersByPreference( decoded,option);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USERPRE.SUCCESS,
        data: list,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const body = { ...request.body };
      body['user_id'] = request.decoded;
      const craetedData: UserPreference = await this.userPreferenceService.createUserPreference(body);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USERPRE.ADD_SUCCESS,
        data: craetedData,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static showById = catchAsync(async (request: Request, response: Response) => {
    try {
      const Id: number = parseInt(request.params.id, 10);
      const details = await this.userPreferenceService.getUserPreferenceById(Id);
      if (!details) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USERPRE.NOT_FOUND);
      }
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USERPRE.DETAILS.SUCCESS,
        data: details,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static update = catchAsync(async (request: Request, response: Response) => {
    try {
      const Id: number = parseInt(request.params.id, 10);
      const body = { ...request.body };

      const updatedData = await this.userPreferenceService.updateUserPreferenceById(Id, body);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USERPRE.UPDATE_SUCCESS,
        data: updatedData,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static delete = catchAsync(async (request: Request, response: Response) => {
    try {
      const id: number = parseInt(request.params.id, 10);
      await this.userPreferenceService.deleteUserPreferenceById(id);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USERPRE.DELETE,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });
}
