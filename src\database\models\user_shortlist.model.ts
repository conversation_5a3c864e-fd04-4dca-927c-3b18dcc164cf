import {
    Table,
    Column,
    Model,
    <PERSON>Type,
    PrimaryKey,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    Index,
} from "sequelize-typescript";
import User from "./user.model";

interface UserShortlistI {
    id: number;
    user_id: number;
    shortlisted_user_id: number;
    createdAt?: Date;
    updatedAt?: Date; 
}

@Table({
    tableName: "user_shortlists",
    timestamps: true,
})
class UserShortlist extends Model<UserShortlistI> implements UserShortlistI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    shortlisted_user_id: number;

    @BelongsTo(() => User, 'shortlisted_user_id')
    shortlisted_user: User;
}

export default UserShortlist;