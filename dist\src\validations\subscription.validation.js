"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cancelSubscriptionValidation = exports.getSubscriptionByIdValidation = exports.canPerformActionValidation = exports.trackUsageValidation = exports.purchaseSubscriptionValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.purchaseSubscriptionValidation = {
    body: joi_1.default.object().keys({
        plan_id: joi_1.default.number().integer().positive().required().messages({
            "number.base": "Plan ID must be a number.",
            "number.integer": "Plan ID must be an integer.",
            "number.positive": "Plan ID must be a positive number.",
            "any.required": "Plan ID is required.",
        }),
        payment_id: joi_1.default.number().integer().positive().optional().messages({
            "number.base": "Payment ID must be a number.",
            "number.integer": "Payment ID must be an integer.",
            "number.positive": "Payment ID must be a positive number.",
        }),
    }),
};
exports.trackUsageValidation = {
    body: joi_1.default.object().keys({
        action_type: joi_1.default.string().valid("interest_sent", "contact_viewed", "profile_viewed", "chat_initiated").required().messages({
            "string.base": "Action type must be a string.",
            "any.only": "Action type must be one of: interest_sent, contact_viewed, profile_viewed, chat_initiated.",
            "any.required": "Action type is required.",
        }),
        target_user_id: joi_1.default.number().integer().positive().required().messages({
            "number.base": "Target user ID must be a number.",
            "number.integer": "Target user ID must be an integer.",
            "number.positive": "Target user ID must be a positive number.",
            "any.required": "Target user ID is required.",
        }),
    }),
};
exports.canPerformActionValidation = {
    query: joi_1.default.object().keys({
        action_type: joi_1.default.string().valid("interest_sent", "contact_viewed", "profile_viewed", "chat_initiated").required().messages({
            "string.base": "Action type must be a string.",
            "any.only": "Action type must be one of: interest_sent, contact_viewed, profile_viewed, chat_initiated.",
            "any.required": "Action type is required.",
        }),
        target_user_id: joi_1.default.number().integer().positive().optional().messages({
            "number.base": "Target user ID must be a number.",
            "number.integer": "Target user ID must be an integer.",
            "number.positive": "Target user ID must be a positive number.",
        }),
    }),
};
exports.getSubscriptionByIdValidation = {
    params: joi_1.default.object().keys({
        subscription_id: joi_1.default.number().integer().positive().required().messages({
            "number.base": "Subscription ID must be a number.",
            "number.integer": "Subscription ID must be an integer.",
            "number.positive": "Subscription ID must be a positive number.",
            "any.required": "Subscription ID is required.",
        }),
    }),
};
exports.cancelSubscriptionValidation = {
    body: joi_1.default.object().keys({
        reason: joi_1.default.string().max(500).optional().messages({
            "string.base": "Reason must be a string.",
            "string.max": "Reason cannot exceed 500 characters.",
        }),
    }),
};
