import httpStatus from "http-status";
import bcrypt from "bcrypt";

import ApiError from "../../utils/ApiError";
import httpMessages from "../../config/httpMessages";
import { Op } from "sequelize";
import { Sequelize } from "sequelize-typescript";
import User from "../../database/models/user.model";
import UserVerification from "../../database/models/user_verifications.model";
import UserGallery from "../../database/models/user_gallery.model";
import UserSubscription from "../../database/models/user_subscriptions.model";
import SubscriptionPlan from "../../database/models/subscription_plans.mode";

export default class UserService {
  constructor() {}

  static isPasswordMatch = async (user: any, password: string) =>
    await bcrypt.compare(password, user.password);

  /**
   * Login with username and password
   * @param {string} email
   * @param {string} password
   * @returns {Promise<User>}
   */
  static loginUserWithEmailAndPassword = async (
    email: string,
    password: string
  ) => {
    const user = await this.getUserByEmail(email, {
      shouldReturnPassword: true,
    }).then((data: any) => data?.toJSON());

    if (!user) {
      throw new ApiError(
        httpStatus.UNAUTHORIZED,
        httpMessages.LOGIN.INCORRECT_EMAIL
      );
    }

    if (!user || !(await this.isPasswordMatch(user, password))) {
      throw new ApiError(
        httpStatus.UNAUTHORIZED,
        httpMessages.LOGIN.INCORRECT_PASS
      );
    }
    user.password = undefined;
    return user;
  };

  /**
   * Get user by email
   * @param {string} email
   * @param {any} options
   * @returns {Promise<User>}
   */
  static getUserByEmail = async (email: string, options: any = {}) => {
    const { shouldReturnPassword = false } = options;

    let excludeAttr: string[] = [];
    if (!shouldReturnPassword) {
      excludeAttr = ["password"];
    }
    return User.findOne({
      where: { email },
      attributes: {
        exclude: excludeAttr,
      },
    });
  };

  static getUserByPhone = async (phone: string) => {
    return User.findOne({
      where: { phone },
      attributes: {
        exclude: ["password"],
      },
    });
  };

  /**
   * Get user
   * @param {any} whereOptions
   * @returns {Promise<User>}
   */
  static getUser = async (whereOptions: any = {}) => {
    return User.findOne({
      where: whereOptions,
      attributes: {
        exclude: ["password"],
      },
    });
  };

  /**
   * Create a user
   * @param {Object} userBody
   * @returns {Promise<User>}
   */
  static createUser = async (userBody: any) => {
    try {
      if (userBody.password !== userBody.confirmPassword) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.REGISTER.PASSWORD_MISMATCH
        );
      } else if (await this.getUserByEmail(userBody.email)) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.REGISTER.EMAIL_ALREADY_TAKEN
        );
      } else if (
        userBody.phone &&
        (await this.getUserByPhone(userBody.phone))
      ) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.REGISTER.PHONE_ALREADY_TAKEN
        );
      }

      const user: User = await User.create(userBody);
      let verificationBody: any = {
        user_id: user.id,
        email: user.email,
        is_email_verified: false,
        phone: user.phone,
        phone_code: user.phone_code,
      };
      await UserVerification.create(verificationBody);

      return this.getUserByEmail(user.email).then((data: any) =>
        data?.toJSON()
      );
    } catch (error: any) {
      const errorMessage =
        error.original?.message || error.message || "Unknown error occurred";
      throw new ApiError(httpStatus.BAD_REQUEST, errorMessage);
    }
  };

  // static getRole = async (roleName: string) => {
  //   return await Role.findOne({
  //     where: { roleName },
  //   });
  // };

  /**
   * Return users
   * @param {Object} options
   * @param {number} [options.page] - Current page number (optional)
   * @param {number} [options.limit] - Number of items per page (optional)
   * @param {string} [options.search] - Search term for filtering (optional)
   * @returns {Promise<User[]>}
   */
  static getUsers = async (options: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }) => {
    try {
      const { page, limit, search, status } = options;
      const whereCondition: any = search
        ? {
            [Op.or]: [
              { email: { [Op.like]: `%${search.toLowerCase()}%` } },
              { first_name: { [Op.like]: `%${search.toLowerCase()}%` } },
              { last_name: { [Op.like]: `%${search.toLowerCase()}%` } },
            ],
          }
        : {};

      let verificationWhereCondition: any = {};
      if (status) {
        whereCondition.status = status;
      }

      const queryOption: any = {
        where: whereCondition,
        include: [
          {
            model: UserVerification,
            as: "verification",
            where: verificationWhereCondition,
            attributes: [],
          },
        ],
        attributes: { exclude: ["password"] },
        order: [["createdAt", "DESC"]],
      };
      // If pagination is provided, apply pagination
      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }
      const totalItems: any = await User.count(queryOption);
      const users = await User.findAll(queryOption);

      if (page && limit) {
        return {
          totalItems: totalItems,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
          users: users,
        };
      } else {
        return users;
      }
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get user by id
   * @param {Number} id
   * @returns {Promise<User>}
   */
  static getUserById = async (id: number) => {
    try {
      return User.findOne({
        where: { id },
        attributes: { exclude: ["password"] },
        include: [
          {
            model: UserSubscription,
            as: "userSubscription",
            where: {
              is_active: true,
            },
            limit: 1,
            include: [
              {
                model: SubscriptionPlan,
                as: "plan",
                attributes: ["name"],
              },
            ],
          },
        ],
      }).then((data: any) => data?.toJSON());
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Get user by id
   * @param {number} id
   * @returns {Promise<User>}
   */
  static getUserByIdWithRoles = async (id: number): Promise<any> => {
    return User.findOne({
      where: { id },
    }).then((data: any) => data?.toJSON());
  };

  /**
   * Update user by id
   * @param {Number} userId
   * @param {Object} updateBody
   * @returns {Promise<User>}
   */
  static updateUserById = async (userId: number, updateBody: any) => {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER.NOT_FOUND);
    }
    // if (user.role_id == 1) {
    //   throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER.NOT_UPDATE);
    // }
    // if (updateBody.email) {
    //   const userByEmail: any = await this.getUserByEmail(updateBody.email);
    //   if (userId === userByEmail.id) {
    //     throw new ApiError(httpStatus.BAD_REQUEST, "Email already taken");
    //   }
    // }
    Object.assign(user, updateBody);
    await user.save();
    return user;
  };

  /**
   * Update user by id
   * @param {Number} userId
   * @param {Object} userProfile
   * @returns {Promise<User>}
   */
  static updateUserByProfile = async (userId: number, userProfile: any) => {
    if (userProfile.email) {
      const fetchedUser: any = await this.getUserByEmail(userProfile.email);
      if (fetchedUser.id != userId) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.REGISTER.EMAIL_ALREADY_TAKEN
        );
      }
    }

    const user = await this.getUserById(userId);
    if (!user) {
      throw new ApiError(httpStatus.NOT_FOUND, "User not found");
    }

    Object.assign(user, userProfile);
    const updatedUser = Object.assign(await user.save());
    updatedUser.password = undefined;

    return updatedUser;
  };

  /**
   * Delete user by id
   * @param {Number} userId
   * @returns {Promise<User>}
   */
  static deleteUserById = async (userId: number) => {
    try {
      const user: any = await User.findByPk(userId);
      if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, "User not found");
      }
      await user.destroy();
      return user;
    } catch (error: any) {
      throw new ApiError(
        error.status || httpStatus.BAD_REQUEST,
        error.message || "Error deleting User."
      );
    }
  };

  /**
   * Update password
   * @param {string} userId
   * @param {string} currentPassword
   * @param {string} newPassword
   * @returns {Promise<User>}
   */
  static updatePassword = async (
    userId: number,
    currentPassword: string,
    newPassword: string
  ) => {
    try {
      const user: any = await User.findByPk(userId);
      if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, "User not found");
      }
      if (!(await this.isPasswordMatch(user, currentPassword))) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.USER.PROFILE.PASSWORD_DOES_NOT_MATCH
        );
      }
      const bcryptedPassword = await bcrypt.hash(newPassword, 10);
      Object.assign(user, { password: bcryptedPassword });
      return await user.save();
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  /**
   * Change email
   * @param {string} userId
   * @param {string} newEmail
   * @returns {Promise<User>}
   */
  static changeEmail = async (userId: number, newEmail: string) => {
    try {
      let whereCondition: any = { email: newEmail };
      if (userId) {
        whereCondition.id = { [Op.ne]: userId };
      }

      const userByEmail: any = await User.findOne({
        where: whereCondition,
      });

      if (userByEmail) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          httpMessages.REGISTER.EMAIL_ALREADY_TAKEN
        );
      }
      const user: any = await User.findByPk(userId);
      if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, "User not found");
      }
      Object.assign(user, { email: newEmail });
      return await user.save();
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  static completeProfile = async (userProfileBody: any) => {
    try {
      const updatedUser = await this.updateUserById(userProfileBody.user_id, {
        profile_image: userProfileBody.profile_image,
        profile_bio: userProfileBody.profile_bio,
      });
      const gallery_images = Object.keys(userProfileBody)
      .filter((key) => key.startsWith("gallery_image["))
      .map((key) => userProfileBody[key]);
      if (gallery_images?.length) {
        let payload: any = [];
        for (
          let index = 0;
          index < gallery_images.length;
          index++
        ) {
          const element = gallery_images[index];
          payload.push({
            user_id: userProfileBody.user_id,
            gallery_image: element,
          });
        }
        await UserGallery.bulkCreate(payload);
      }
      await UserVerification.update(
        { is_profile_completed: true },
        { where: { user_id: userProfileBody.user_id } }
      );
      return updatedUser;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };
}
