"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const sequelize_1 = require("sequelize");
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const message_model_1 = __importDefault(require("../../database/models/message.model"));
const user_model_1 = __importDefault(require("../../database/models/user.model"));
const EncryptionResponse_1 = require("../../middlewares/EncryptionResponse");
const moment_1 = __importDefault(require("moment"));
const chat_model_1 = __importDefault(require("../../database/models/chat.model"));
class MessageService {
}
_a = MessageService;
/**
 * Send a new message
 * @param {number} chatId - Chat ID
 * @param {number} senderId - Sender user ID
 * @param {number} receiverId - Receiver user ID
 * @param {string} content - Message content
 * @returns {Promise<Message>}
 */
MessageService.sendMessage = (chatId, senderId, receiverId, content) => __awaiter(void 0, void 0, void 0, function* () {
    // Encrypt message content
    const iv = "v@XI!kaW3BK,@8ki";
    const secretKey = "Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn";
    const encryptedContent = (0, EncryptionResponse_1.encryptData)(content, secretKey, iv);
    // Create message
    const message = yield message_model_1.default.create({
        chat_id: chatId,
        sender_id: senderId,
        receiver_id: receiverId,
        content: encryptedContent,
        is_encrypted: true,
        is_delivered: false,
        is_read: false,
        delivered_at: null,
        read_at: null,
    });
    // Update last message time in chat
    yield chat_model_1.default.update({ last_message_at: new Date() }, { where: { id: chatId } });
    return message;
});
/**
 * Get messages for a chat with pagination
 * @param {number} chatId - Chat ID
 * @param {number} page - Page number
 * @param {number} limit - Number of messages per page
 * @returns {Promise<{ messages: Message[], total: number, totalPages: number, currentPage: number }>}
 */
MessageService.getChatMessages = (chatId_1, ...args_1) => __awaiter(void 0, [chatId_1, ...args_1], void 0, function* (chatId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    const { count, rows } = yield message_model_1.default.findAndCountAll({
        where: { chat_id: chatId },
        include: [
            {
                model: user_model_1.default,
                as: "sender",
                attributes: ["id", "first_name", "last_name"],
            },
            {
                model: user_model_1.default,
                as: "receiver",
                attributes: ["id", "first_name", "last_name"],
            },
        ],
        order: [["createdAt", "DESC"]],
        limit,
        offset,
    });
    return {
        messages: rows,
        total: count,
        totalPages: Math.ceil(count / limit),
        currentPage: page,
    };
});
/**
 * Mark message as delivered
 * @param {number} messageId - Message ID
 * @returns {Promise<Message>}
 */
MessageService.markAsDelivered = (messageId) => __awaiter(void 0, void 0, void 0, function* () {
    const message = yield message_model_1.default.findByPk(messageId);
    if (!message) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Message not found");
    }
    if (!message.is_delivered) {
        yield message.update({
            is_delivered: true,
            delivered_at: new Date(),
        });
    }
    return message;
});
/**
 * Mark message as read
 * @param {number} messageId - Message ID
 * @returns {Promise<Message>}
 */
MessageService.markAsRead = (messageId) => __awaiter(void 0, void 0, void 0, function* () {
    const message = yield message_model_1.default.findByPk(messageId);
    if (!message) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Message not found");
    }
    if (!message.is_read) {
        yield message.update({
            is_read: true,
            read_at: new Date(),
        });
    }
    return message;
});
/**
 * Mark all unread messages in a chat as read
 * @param {number} chatId - Chat ID
 * @param {number} userId - User ID (receiver)
 * @returns {Promise<number>} - Number of messages marked as read
 */
MessageService.markAllAsRead = (chatId, userId) => __awaiter(void 0, void 0, void 0, function* () {
    const result = yield message_model_1.default.update({
        is_read: true,
        read_at: new Date(),
    }, {
        where: {
            chat_id: chatId,
            receiver_id: userId,
            is_read: false,
        },
    });
    return result[0]; // Number of rows affected
});
/**
 * Delete messages older than specified days
 * @param {number} days - Number of days
 * @returns {Promise<number>} - Number of messages deleted
 */
MessageService.deleteOldMessages = (days) => __awaiter(void 0, void 0, void 0, function* () {
    const cutoffDate = (0, moment_1.default)().subtract(days, "days").toDate();
    // Find chats with auto-delete enabled
    const chats = yield chat_model_1.default.findAll({
        where: {
            auto_delete_days: {
                [sequelize_1.Op.not]: null,
            },
        },
    });
    let totalDeleted = 0;
    // For each chat, delete messages older than its auto-delete setting
    for (const chat of chats) {
        if (chat.auto_delete_days) {
            const chatCutoffDate = (0, moment_1.default)()
                .subtract(chat.auto_delete_days, "days")
                .toDate();
            const result = yield message_model_1.default.destroy({
                where: {
                    chat_id: chat.id,
                    createdAt: {
                        [sequelize_1.Op.lt]: chatCutoffDate,
                    },
                },
            });
            totalDeleted += result;
        }
    }
    return totalDeleted;
});
exports.default = MessageService;
