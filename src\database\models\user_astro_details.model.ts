import {
    Table,
    Column,
    Model,
    DataType,
    PrimaryKey,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    Index,
} from "sequelize-typescript";
import User from "./user.model";
import Country from "./country.model";
import City from "./city.model";

interface UserAstroDetailsI {
    id: number;
    user_id: number;
    countryOfBirth?: number;
    birthCity?: number;
    birthTime?: string;
    daylight_savings?: string;
    rashi?: string;
    nakshatra?: string;
    manglik?: string;
    horoscope_match?: boolean;
}

@Table({
    tableName: "user_astro_details",
    timestamps: false,
})
class UserAstroDetails extends Model<UserAstroDetailsI> implements UserAstroDetailsI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    @ForeignKey(() => Country)
    @AllowNull(true)
    @Column(DataType.INTEGER)
    countryOfBirth?: number;

    @BelongsTo(() => Country, 'countryOfBirth')
    country: Country;

    @ForeignKey(() => City)
    @AllowNull(true)
    @Column(DataType.INTEGER)
    birthCity?: number;

    @BelongsTo(() => City, 'birthCity')
    cities: City;

    @AllowNull(true)
    @Column(DataType.STRING(10))
    birthTime?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    daylight_savings?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    rashi?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    nakshatra?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    manglik?: string;

    @AllowNull(true)
    @Column(DataType.BOOLEAN)
    horoscope_match?: boolean;
}

export default UserAstroDetails;