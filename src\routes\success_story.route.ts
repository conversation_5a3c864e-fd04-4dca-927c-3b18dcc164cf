import express from "express";
import { auth } from "../middlewares/auth";
import SuccessStoryController from "../app/success_story/success_story.controller";
import { validate } from "../middlewares/middleware";
import { 
    successStoryValidation, 
    updateSuccessStoryStatusValidation, 
    getSuccessStoryByIdValidation, 
    deleteSuccessStoryValidation 
} from "../validations/success_story.validation";
import { fileUploadMiddleware } from "../middlewares/fileUploadMiddleware";

const router = express.Router();

// Public routes
router.get("/approved", SuccessStoryController.getApproved);

// User routes (require authentication)
router.get("/user", auth, SuccessStoryController.getUserStories);
router.post("", auth, fileUploadMiddleware, SuccessStoryController.create);

// Admin routes (require authentication)
router.get("", auth, SuccessStoryController.getAll);
router.get("/:id", auth, SuccessStoryController.showById);
router.put("/:id/status", auth, validate(updateSuccessStoryStatusValidation), SuccessStoryController.updateStatus);
router.delete("/:id", auth, validate(deleteSuccessStoryValidation), SuccessStoryController.delete);

export default router;
