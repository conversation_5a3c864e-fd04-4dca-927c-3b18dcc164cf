import {
    AllowNull,
    AutoIncrement,
    BelongsTo,
    Column,
    DataType,
    Foreign<PERSON>ey,
    Model,
    PrimaryKey,
    Table
} from "sequelize-typescript";
import User from "./user.model";
import SubscriptionPlan from "./subscription_plans.mode";

export interface UserSubscriptionI {
    id?: number;
    user_id: number;
    plan_id: number;
    payment_id?: number;
    payment_status: "pending" | "completed" | "failed" | "refunded";
    start_date: Date;
    end_date: Date;
    auto_renew: boolean;
    issued_at: Date;
    expires_at: Date;
    revoked_at?: Date;
    description?: string;
    // Usage tracking fields
    interest_sent_count: number;
    contact_viewed_count: number;
    profile_viewed_count: number;
    chat_initiated_count: number;

    is_active: boolean;
    token: string;

    // Timestamps
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: "user_subscriptions",
    timestamps: true
})
class UserSubscription extends Model<UserSubscriptionI> implements UserSubscriptionI {
    @AutoIncrement
    @PrimaryKey
    @Column
    id: number;

    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @ForeignKey(() => SubscriptionPlan)
    @AllowNull(false)
    @Column
    plan_id: number;

    @AllowNull(true)
    @Column(DataType.INTEGER)
    payment_id?: number;

    @AllowNull(false)
    @Column(DataType.ENUM("pending", "completed", "failed", "refunded"))
    payment_status: "pending" | "completed" | "failed" | "refunded";

    @AllowNull(true)
    @Column(DataType.DATE)
    start_date: Date;

    @AllowNull(true)
    @Column(DataType.DATE)
    end_date: Date;

    @AllowNull(true)
    @Column(DataType.BOOLEAN)
    auto_renew: boolean;

    @AllowNull(true)
    @Column(DataType.DATE)
    issued_at: Date;

    @AllowNull(true)
    @Column(DataType.DATE)
    expires_at: Date;

    @AllowNull(true)
    @Column(DataType.DATE)
    revoked_at?: Date;

    @AllowNull(true)
    @Column(DataType.STRING)
    description?: string;

    // Usage tracking fields
    @AllowNull(false)
    @Column({ type: DataType.INTEGER, defaultValue: 0 })
    interest_sent_count: number;

    @AllowNull(false)
    @Column({ type: DataType.INTEGER, defaultValue: 0 })
    contact_viewed_count: number;

    @AllowNull(false)
    @Column({ type: DataType.INTEGER, defaultValue: 0 })
    profile_viewed_count: number;

    @AllowNull(false)
    @Column({ type: DataType.INTEGER, defaultValue: 0 })
    chat_initiated_count: number;

    @AllowNull(true)
    @Column(DataType.BOOLEAN)
    is_active: boolean;

    @AllowNull(true)
    @Column(DataType.TEXT)
    token: string;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    @BelongsTo(() => SubscriptionPlan, { foreignKey: "plan_id", onDelete: "CASCADE", })
    plan: User;
}

export default UserSubscription;

