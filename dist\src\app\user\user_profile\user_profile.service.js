"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const user_model_1 = __importDefault(require("../../../database/models/user.model"));
const user_education_career_model_1 = __importDefault(require("../../../database/models/user_education_career.model"));
const user_basic_details_model_1 = __importDefault(require("../../../database/models/user_basic_details.model"));
const user_astro_details_model_1 = __importDefault(require("../../../database/models/user_astro_details.model"));
const user_location_details_model_1 = __importDefault(require("../../../database/models/user_location_details.model"));
const user_lifestyle_model_1 = __importDefault(require("../../../database/models/user_lifestyle.model"));
const user_family_details_model_1 = __importDefault(require("../../../database/models/user_family_details.model"));
const user_verifications_model_1 = __importDefault(require("../../../database/models/user_verifications.model"));
const database_1 = require("../../../database/database");
const sequelize_1 = require("sequelize");
const user_hobbies_model_1 = __importDefault(require("../../../database/models/user_hobbies.model"));
const user_gallery_model_1 = __importDefault(require("../../../database/models/user_gallery.model"));
const user_shortlist_model_1 = __importDefault(require("../../../database/models/user_shortlist.model"));
const country_model_1 = __importDefault(require("../../../database/models/country.model"));
const city_model_1 = __importDefault(require("../../../database/models/city.model"));
const user_preferences_model_1 = __importDefault(require("../../../database/models/user_preferences.model"));
class UserProfileService {
    constructor() { }
}
_a = UserProfileService;
UserProfileService.getUserProfiles = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, filters = {}, currentUserId } = options;
        console.log('filters: ', filters);
        const whereCondition = (filters === null || filters === void 0 ? void 0 : filters.member_id)
            ? {
                [sequelize_1.Op.or]: [
                    { member_id: { [sequelize_1.Op.like]: `%${filters === null || filters === void 0 ? void 0 : filters.member_id.toLowerCase()}%` } },
                ],
            }
            : {};
        if (currentUserId) {
            whereCondition.id = { [sequelize_1.Op.ne]: currentUserId };
            const currentUser = yield user_model_1.default.findOne({
                where: { id: currentUserId },
            });
            if ((currentUser === null || currentUser === void 0 ? void 0 : currentUser.gender) === 'male') {
                whereCondition.gender = 'female';
            }
            else {
                whereCondition.gender = 'male';
            }
        }
        whereCondition.status = 'approved';
        whereCondition.is_hide_profile = false;
        if (filters === null || filters === void 0 ? void 0 : filters.last_name)
            whereCondition.last_name = filters === null || filters === void 0 ? void 0 : filters.last_name;
        // Sub-filters (joins)
        const basicWhere = {};
        const locationWhere = {};
        const educationWhere = {};
        const lifestyleWhere = {};
        const familyWhere = {};
        const astroWhere = {};
        const hobbiesWhere = {};
        // Fill where conditions
        if (filters === null || filters === void 0 ? void 0 : filters.religion)
            basicWhere.religion = filters.religion;
        if (filters === null || filters === void 0 ? void 0 : filters.caste)
            basicWhere.caste = filters.caste;
        if (filters === null || filters === void 0 ? void 0 : filters.gotra)
            basicWhere.gotra = filters.gotra;
        if (filters === null || filters === void 0 ? void 0 : filters.marital_status)
            basicWhere.marital_status = filters.marital_status;
        if (filters === null || filters === void 0 ? void 0 : filters.country_of_citizenship)
            basicWhere.country_of_citizenship = filters.country_of_citizenship;
        if (filters === null || filters === void 0 ? void 0 : filters.country_living_in)
            locationWhere.country_living_in = filters.country_living_in;
        if (filters === null || filters === void 0 ? void 0 : filters.city)
            locationWhere.city = filters.city;
        if (filters === null || filters === void 0 ? void 0 : filters.residency_status)
            locationWhere.residency_status = filters.residency_status;
        if (filters === null || filters === void 0 ? void 0 : filters.education)
            educationWhere.education = filters.education;
        if (filters === null || filters === void 0 ? void 0 : filters.profession)
            educationWhere.profession = filters.profession;
        if (filters === null || filters === void 0 ? void 0 : filters.employment_status)
            educationWhere.employment_status = filters.employment_status;
        if ((filters === null || filters === void 0 ? void 0 : filters.age) && (filters.age.start || filters.age.end)) {
            lifestyleWhere.age = {};
            if (filters.age.start)
                lifestyleWhere.age[sequelize_1.Op.gte] = filters.age.start;
            if (filters.age.end)
                lifestyleWhere.age[sequelize_1.Op.lte] = filters.age.end;
        }
        if ((filters === null || filters === void 0 ? void 0 : filters.height_range) && (filters.height_range.start || filters.height_range.end)) {
            lifestyleWhere.height_cm = {};
            if (filters.height_range.start)
                lifestyleWhere.height_cm[sequelize_1.Op.gte] = filters.height_range.start;
            if (filters.height_range.end)
                lifestyleWhere.height_cm[sequelize_1.Op.lte] = filters.height_range.end;
        }
        if (filters === null || filters === void 0 ? void 0 : filters.body_type)
            lifestyleWhere.body_type = filters.body_type;
        if (filters === null || filters === void 0 ? void 0 : filters.complexion)
            lifestyleWhere.complexion = filters.complexion;
        if (filters === null || filters === void 0 ? void 0 : filters.diet)
            lifestyleWhere.diet = filters.diet;
        if (filters === null || filters === void 0 ? void 0 : filters.smoking_habit)
            lifestyleWhere.smoking_habit = filters.smoking_habit;
        if (filters === null || filters === void 0 ? void 0 : filters.drinking_habit)
            lifestyleWhere.drinking_habit = filters.drinking_habit;
        if (filters === null || filters === void 0 ? void 0 : filters.any_disability)
            lifestyleWhere.any_disability = filters.any_disability;
        if (filters === null || filters === void 0 ? void 0 : filters.countryOfBirth)
            astroWhere.countryOfBirth = filters.countryOfBirth;
        if (filters === null || filters === void 0 ? void 0 : filters.birthCity)
            astroWhere.birthCity = filters.birthCity;
        if (filters === null || filters === void 0 ? void 0 : filters.family_type)
            familyWhere.family_type = filters.family_type;
        if (filters === null || filters === void 0 ? void 0 : filters.father_occupation)
            familyWhere.father_occupation = filters.father_occupation;
        if (filters === null || filters === void 0 ? void 0 : filters.mother_occupation)
            familyWhere.mother_occupation = filters.mother_occupation;
        if (filters === null || filters === void 0 ? void 0 : filters.number_of_siblings)
            familyWhere.number_of_siblings = filters.number_of_siblings;
        if (filters === null || filters === void 0 ? void 0 : filters.maternal_surname)
            familyWhere.maternal_surname = filters.maternal_surname;
        if (filters === null || filters === void 0 ? void 0 : filters.hobbies)
            hobbiesWhere.hobbies = filters.hobbies;
        if (filters === null || filters === void 0 ? void 0 : filters.interests)
            hobbiesWhere.interests = filters.interests;
        const queryOption = {
            where: whereCondition,
            distinct: true,
            // attributes: {
            //   include: [
            //     [
            //       Sequelize.literal("`lifestyle`.`age`"),
            //       "age",
            //     ],
            //     [
            //       Sequelize.literal("`lifestyle`.`height_cm`"),
            //       "height_cm",
            //     ],
            //   ],
            // },
            include: [
                {
                    model: user_basic_details_model_1.default,
                    as: "basicDetails",
                    attributes: ["religion", "caste", "gotra", 'marital_status', 'country_of_citizenship'],
                    where: Object.keys(basicWhere).length ? basicWhere : undefined,
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ['name'],
                        },
                    ],
                },
                {
                    model: user_location_details_model_1.default,
                    as: "locationDetails",
                    where: Object.keys(locationWhere).length ? locationWhere : undefined,
                    attributes: ["city", "country_living_in"],
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ['name'],
                        },
                        {
                            model: city_model_1.default,
                            as: "cities",
                            attributes: ['name'],
                        },
                    ],
                },
                {
                    model: user_education_career_model_1.default,
                    as: "educationCareer",
                    where: Object.keys(educationWhere).length ? educationWhere : undefined,
                    attributes: ["education", "profession"]
                },
                {
                    model: user_lifestyle_model_1.default,
                    as: "lifestyle",
                    where: Object.keys(lifestyleWhere).length ? lifestyleWhere : undefined,
                    attributes: ["age", "height_cm"]
                },
                {
                    model: user_family_details_model_1.default,
                    as: "familyDetails",
                    where: Object.keys(familyWhere).length ? familyWhere : undefined,
                    attributes: ["family_type", "father_occupation", "mother_occupation"]
                },
                {
                    model: user_hobbies_model_1.default,
                    as: "hobbies",
                    where: Object.keys(hobbiesWhere).length ? hobbiesWhere : undefined,
                    attributes: ["hobbies", "interests"]
                },
                {
                    model: user_gallery_model_1.default,
                    as: "userGallery",
                },
                {
                    model: user_shortlist_model_1.default,
                    as: "shortlisted_user"
                }
            ],
            order: [["createdAt", "DESC"]],
        };
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        // Count total items
        const totalItems = yield user_model_1.default.count({
            where: whereCondition,
        });
        const user = yield user_model_1.default.findAll(queryOption);
        if (page && limit) {
            return {
                totalItems: totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                user: user,
            };
        }
        else {
            return user;
        }
    }
    catch (error) {
        console.log('error: ', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
UserProfileService.createUserDetails = (userBody, userId) => __awaiter(void 0, void 0, void 0, function* () {
    const transaction = yield database_1.sequelize.transaction();
    try {
        const { verfication, familyDetails, educationCareer, lifestyle, basicDetails, locationDetails, astroDetails, hobbies } = userBody;
        const upsertModel = (model, data) => __awaiter(void 0, void 0, void 0, function* () {
            if (!data)
                return;
            if (data.id) {
                yield model.update(data, { where: { id: data.id }, transaction });
            }
            else {
                yield model.create(data, { transaction });
            }
        });
        let message = "";
        if (basicDetails) {
            yield upsertModel(user_basic_details_model_1.default, basicDetails);
            message = "Basic Details";
        }
        ;
        if (locationDetails) {
            yield upsertModel(user_location_details_model_1.default, locationDetails);
            message = "Location Details";
        }
        ;
        if (educationCareer) {
            yield upsertModel(user_education_career_model_1.default, educationCareer);
            message = "Education Details";
        }
        ;
        if (lifestyle) {
            yield upsertModel(user_lifestyle_model_1.default, lifestyle);
            message = "Lifestyle Details";
        }
        ;
        if (astroDetails) {
            yield upsertModel(user_astro_details_model_1.default, astroDetails);
            message = "Astro Details";
        }
        ;
        if (familyDetails) {
            yield upsertModel(user_family_details_model_1.default, familyDetails);
            message = "Family Details";
        }
        ;
        if (hobbies) {
            yield upsertModel(user_hobbies_model_1.default, hobbies);
            message = "Hobbies";
        }
        if (verfication) {
            yield upsertModel(user_verifications_model_1.default, verfication);
            message = "Verification";
        }
        ;
        yield transaction.commit();
        const user = yield _a.getUserProfileById(userId);
        return { user, message };
    }
    catch (error) {
        yield transaction.rollback();
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
UserProfileService.getUserProfileById = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_model_1.default.findOne({
            where: { id: userId },
            include: [
                {
                    model: user_basic_details_model_1.default,
                    as: "basicDetails",
                    attributes: {
                        include: [
                            [
                                sequelize_1.Sequelize.literal("`basicDetails->country`.`name`"),
                                "country_of_citizenship_name",
                            ],
                        ],
                    },
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: [],
                        },
                    ],
                },
                {
                    model: user_location_details_model_1.default,
                    as: "locationDetails",
                    attributes: {
                        include: [
                            [sequelize_1.Sequelize.literal("`locationDetails->country`.`name`"), "country_living_in_name"],
                            [sequelize_1.Sequelize.literal("`locationDetails->cities`.`name`"), "city_name"],
                        ],
                    },
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: [],
                        },
                        {
                            model: city_model_1.default,
                            as: "cities",
                            attributes: [],
                        },
                    ],
                },
                {
                    model: user_education_career_model_1.default,
                    as: "educationCareer",
                },
                {
                    model: user_lifestyle_model_1.default,
                    as: "lifestyle",
                },
                {
                    model: user_astro_details_model_1.default,
                    as: "astroDetails",
                    attributes: {
                        include: [
                            [sequelize_1.Sequelize.literal("`astroDetails->country`.`name`"), "countryOfBirth_name"],
                            [sequelize_1.Sequelize.literal("`astroDetails->cities`.`name`"), "birthCity_name"],
                        ],
                    },
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: [],
                        },
                        {
                            model: city_model_1.default,
                            as: "cities",
                            attributes: [],
                        },
                    ],
                },
                {
                    model: user_family_details_model_1.default,
                    as: "familyDetails",
                },
                {
                    model: user_gallery_model_1.default,
                    as: "userGallery",
                },
                {
                    model: user_hobbies_model_1.default,
                    as: "hobbies",
                },
                {
                    model: user_verifications_model_1.default,
                    as: "verification",
                },
                {
                    model: user_preferences_model_1.default,
                    as: "userPreference",
                    attributes: ["id"]
                }
            ],
        });
        if (!user) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "User not found");
        }
        return user;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, error.message);
    }
});
UserProfileService.createUserProfile = (userProfileBody) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userProfile = yield user_gallery_model_1.default.create(userProfileBody);
        yield user_verifications_model_1.default.update({ is_profile_completed: true }, { where: { user_id: userProfileBody.user_id } });
        return userProfile;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
UserProfileService.getUserSearchProfiles = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, filters = {} } = options;
        console.log('filters: ', filters);
        const whereCondition = search
            ? {
                [sequelize_1.Op.or]: [
                    { firstName: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                ],
            }
            : {};
        whereCondition.status = 'approved';
        whereCondition.is_hide_profile = false;
        if (filters === null || filters === void 0 ? void 0 : filters.last_name)
            whereCondition.last_name = filters === null || filters === void 0 ? void 0 : filters.last_name;
        if (filters === null || filters === void 0 ? void 0 : filters.gender)
            whereCondition.gender = filters.gender;
        // Sub-filters (joins)
        const basicWhere = {};
        const locationWhere = {};
        const educationWhere = {};
        const lifestyleWhere = {};
        const familyWhere = {};
        const astroWhere = {};
        const hobbiesWhere = {};
        // Fill where conditions
        if (filters === null || filters === void 0 ? void 0 : filters.religion)
            basicWhere.religion = filters.religion;
        if (filters === null || filters === void 0 ? void 0 : filters.caste)
            basicWhere.caste = filters.caste;
        if (filters === null || filters === void 0 ? void 0 : filters.marital_status)
            basicWhere.marital_status = filters.marital_status;
        if (filters === null || filters === void 0 ? void 0 : filters.country_living_in)
            locationWhere.country_living_in = filters.country_living_in;
        if ((filters === null || filters === void 0 ? void 0 : filters.age) && (filters.age.start || filters.age.end)) {
            lifestyleWhere.age = {};
            if (filters.age.start)
                lifestyleWhere.age[sequelize_1.Op.gte] = filters.age.start;
            if (filters.age.end)
                lifestyleWhere.age[sequelize_1.Op.lte] = filters.age.end;
        }
        const queryOption = {
            where: whereCondition,
            distinct: true,
            // attributes: {
            //   include: [
            //     [
            //       Sequelize.literal("`lifestyle`.`age`"),
            //       "age",
            //     ],
            //     [
            //       Sequelize.literal("`lifestyle`.`height_cm`"),
            //       "height_cm",
            //     ],
            //   ],
            // },
            include: [
                {
                    model: user_basic_details_model_1.default,
                    as: "basicDetails",
                    attributes: ["religion", "caste", "gotra", 'marital_status', 'country_of_citizenship'],
                    where: Object.keys(basicWhere).length ? basicWhere : undefined,
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ['name'],
                        },
                    ],
                },
                {
                    model: user_location_details_model_1.default,
                    as: "locationDetails",
                    where: Object.keys(locationWhere).length ? locationWhere : undefined,
                    attributes: ["city", "country_living_in"],
                    include: [
                        {
                            model: country_model_1.default,
                            as: "country",
                            attributes: ['name'],
                        },
                        {
                            model: city_model_1.default,
                            as: "cities",
                            attributes: ['name'],
                        },
                    ],
                },
                {
                    model: user_education_career_model_1.default,
                    as: "educationCareer",
                    where: Object.keys(educationWhere).length ? educationWhere : undefined,
                    attributes: ["education", "profession"]
                },
                {
                    model: user_lifestyle_model_1.default,
                    as: "lifestyle",
                    where: Object.keys(lifestyleWhere).length ? lifestyleWhere : undefined,
                    attributes: ["age", "height_cm"]
                },
                {
                    model: user_family_details_model_1.default,
                    as: "familyDetails",
                    where: Object.keys(familyWhere).length ? familyWhere : undefined,
                    attributes: ["family_type", "father_occupation", "mother_occupation"]
                },
                {
                    model: user_hobbies_model_1.default,
                    as: "hobbies",
                    where: Object.keys(hobbiesWhere).length ? hobbiesWhere : undefined,
                    attributes: ["hobbies", "interests"]
                },
                {
                    model: user_shortlist_model_1.default,
                    as: "shortlisted_user"
                }
            ],
            order: [["createdAt", "DESC"]],
        };
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        // Count total items
        const totalItems = yield user_model_1.default.count({
            where: whereCondition,
        });
        const user = yield user_model_1.default.findAll(queryOption);
        if (page && limit) {
            return {
                totalItems: totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                user: user,
            };
        }
        else {
            return user;
        }
    }
    catch (error) {
        console.log('error: ', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = UserProfileService;
