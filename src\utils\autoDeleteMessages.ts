import MessageService from "../app/message/message.service";

/**
 * Function to delete old messages based on chat auto-delete settings
 * This should be called by a cron job or scheduled task
 */
export const runAutoDeleteMessages = async () => {
  try {
    const deletedCount = await MessageService.deleteOldMessages(1); // Pass 1 as a placeholder, actual days are taken from chat settings
    console.log(`Auto-deleted ${deletedCount} old messages`);
    return deletedCount;
  } catch (error) {
    console.error('Error auto-deleting messages:', error);
    throw error;
  }
};
