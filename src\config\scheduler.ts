import cron from 'node-cron';
import { runAutoDeleteMessages } from '../utils/autoDeleteMessages';
import { runAutoUnhideProfiles } from '../utils/autoUnhideProfiles';
import SubscriptionService from '../app/subscription/subscription.service';

/**
 * Initialize all scheduled tasks
 */
export const initScheduler = () => {
  // Schedule auto-delete messages task (keep your existing schedule)
  cron.schedule('0 0 * * *', async () => {
    console.log('Running scheduled task: Auto-delete messages');
    try {
      // await runAutoDeleteMessages();
    } catch (error) {
      console.error('Error in auto-delete messages task:', error);
    }
  });

  // Schedule auto-unhide profiles task at midnight (00:00) every day
  cron.schedule('0 0 * * *', async () => {
    console.log('Running scheduled task: Auto-unhide profiles');
    try {
      await runAutoUnhideProfiles();
    } catch (error) {
      console.error('Error in auto-unhide profiles task:', error);
    }
  });

  cron.schedule('0 0 * * *', async () => {
    console.log('Running scheduled task: Auto-delete messages');
    try {
      try {
        console.log('Running daily subscription expiry check...');
        const result = await SubscriptionService.checkAndUpdateExpiredSubscriptions();
        console.log(`Subscription expiry check completed. Updated ${result.updatedCount} subscriptions.`);

        if (result.updatedCount > 0) {
          console.log('Expired subscriptions:', result.expiredSubscriptions);
        }
      } catch (error) {
        console.error('Error in subscription expiry check:', error);
      }
    } catch (error) {
      console.error('Error in auto-delete messages task:', error);
    }
  });

};
