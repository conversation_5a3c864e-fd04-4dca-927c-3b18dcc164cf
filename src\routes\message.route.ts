import express from "express";
import { auth } from "../middlewares/auth";
import MessageController from "../app/message/message.controller";

const router = express.Router();

// Apply auth middleware to all message routes
router.use(auth);

// Message routes
router.post("/", MessageController.sendMessage);
router.put("/:id/delivered", MessageController.markAsDelivered);
router.put("/:id/read", MessageController.markAsRead);

export default router;
