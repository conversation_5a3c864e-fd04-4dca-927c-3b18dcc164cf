"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.geolocationValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.geolocationValidation = {
    body: joi_1.default.object().keys({
        id: joi_1.default.number().optional().allow(null, ""),
        name: joi_1.default.string().trim().required().messages({
            "string.empty": "Name is required.",
            "string.base": "Name must be a string.",
        }),
        phoneCode: joi_1.default.string().trim().required().messages({
            "string.empty": "Phone code is required.",
            "string.base": "Phone code must be a string.",
            "string.min": "Phone code must be at least 1 characters long.",
        }),
        timezone: joi_1.default.string().trim().optional().allow(null, ""),
        currency: joi_1.default.string().trim().optional().allow(null, ""),
        currency_symbol: joi_1.default.string().trim().optional().allow(null, ""),
        flag: joi_1.default.string().trim().optional().allow(null, ""),
        is_active: joi_1.default.boolean().optional().allow(null, ""),
        cities: joi_1.default.array().items(joi_1.default.object().keys({
            id: joi_1.default.number().optional().allow(null, ""),
            name: joi_1.default.string().trim().required().messages({
                "string.empty": "City name is required.",
                "string.base": "City name must be a string.",
            }),
        })).required().messages({
            "array.base": "Cities must be an array.",
            "any.required": "Cities is required.",
        }),
    }),
};
