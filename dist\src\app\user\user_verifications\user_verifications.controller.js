"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const user_verifications_service_1 = __importDefault(require("./user_verifications.service"));
const catchAsync_1 = __importDefault(require("../../../utils/catchAsync"));
const response_1 = __importStar(require("../../../utils/response"));
const httpMessages_1 = __importDefault(require("../../../config/httpMessages"));
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const user_service_1 = __importDefault(require("../user.service"));
class UserVerificationController {
    constructor() { }
}
_a = UserVerificationController;
UserVerificationController.userVerificationService = user_verifications_service_1.default;
UserVerificationController.userService = user_service_1.default;
UserVerificationController.getAll = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search } = request.query;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search ? search : "",
        };
        const list = yield _a.userVerificationService.getUserVerifications(option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER_VERIFICATION.SUCCESS,
            data: list,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserVerificationController.create = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const body = Object.assign({}, request.body);
        const craetedData = yield _a.userVerificationService.createUserVerification(body);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER_VERIFICATION.ADD_SUCCESS,
            data: craetedData,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserVerificationController.showById = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const details = yield _a.userVerificationService.getUserVerificationById(userId);
        if (!details) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER_VERIFICATION.NOT_FOUND);
        }
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER_VERIFICATION.DETAILS.SUCCESS,
            data: details,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserVerificationController.update = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const Id = parseInt(request.params.id, 10);
        const body = Object.assign({}, request.body);
        const updatedData = yield _a.userVerificationService.updateUserVerificationById(Id, body);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER_VERIFICATION.UPDATE_SUCCESS,
            data: updatedData,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserVerificationController.delete = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = parseInt(request.params.id, 10);
        yield _a.userVerificationService.deleteUserVerificationById(id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER_VERIFICATION.DELETE,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserVerificationController.sendPhoneOtp = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { phone, phone_code } = request.body;
        const userId = request.decoded;
        const verification = yield _a.userVerificationService.sendPhoneOtp(userId, phone, phone_code);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "OTP sent successfully",
            data: verification,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserVerificationController.verifyPhoneOtp = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { phone, otp, phone_code } = request.body;
        const userId = request.decoded;
        const verification = yield _a.userVerificationService.verifyPhoneOtp(userId, phone, otp, phone_code);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Phone verified successfully",
            data: verification,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserVerificationController.uploadIdDocuments = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = request.decoded;
        const verification = yield _a.userVerificationService.uploadIdDocuments(userId, request.body);
        yield _a.userService.updateUserById(userId, { status: 'pending' });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "ID documents uploaded successfully",
            data: verification,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = UserVerificationController;
