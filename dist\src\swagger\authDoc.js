"use strict";
/**
 * @swagger
 * /auth/register:
 *   post:
 *     tags:
 *       - Auth
 *     summary: User registration
 *     description: Register a new user with their details, including email, phone number, and password.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - first_name
 *               - last_name
 *               - email
 *               - phone
 *               - password
 *               - confirmPassword
 *               - date_of_birth
 *               - gender
 *               - status
 *             properties:
 *               first_name:
 *                 type: string
 *                 example: John
 *               middle_name:
 *                 type: string
 *                 example: Michael
 *               last_name:
 *                 type: string
 *                 example: Doe
 *               gender:
 *                 type: string
 *                 enum: ['male', 'female', 'other']
 *                 example: male
 *               profile_created_for:
 *                 type: string
 *                 example: myself
 *               date_of_birth:
 *                 type: string
 *                 format: date
 *                 example: 1990-01-01
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               phone:
 *                 type: string
 *                 example: 9876543210
 *               password:
 *                 type: string
 *                 example: John@123
 *               confirmPassword:
 *                 type: string
 *                 example: <PERSON>@123
 *               is_email_verified:
 *                 type: boolean
 *                 example: false
 *               is_phone_verified:
 *                 type: boolean
 *                 example: false
 *               terms_condition:
 *                 type: boolean
 *                 example: true
 *               status:
 *                 type: string
 *                 enum: ['active', 'inactive', 'blocked']
 *                 example: active
 *     responses:
 *       201:
 *         description: User successfully registered.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User registered successfully.
 *                 data:
 *                   type: string
 *                   example: null
 *       400:
 *         description: Invalid data or missing required fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Validation failed.
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */
/**
 * @swagger
 * /auth/login:
 *   post:
 *     tags:
 *       - Auth
 *     summary: User login
 *     description: Authenticate a user using email and password.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 example: John@123
 *     responses:
 *       200:
 *         description: User successfully logged in.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Login successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 4
 *                     first_name:
 *                       type: string
 *                       example: John
 *                     middle_name:
 *                       type: string
 *                       example: Michael
 *                     last_name:
 *                       type: string
 *                       example: Doe
 *                     gender:
 *                       type: string
 *                       example: male
 *                     profile_created_for:
 *                       type: string
 *                       example: myself
 *                     date_of_birth:
 *                       type: string
 *                       format: date
 *                       example: 1990-01-01T00:00:00.000Z
 *                     email:
 *                       type: string
 *                       example: <EMAIL>
 *                     phone:
 *                       type: string
 *                       example: 9876543210
 *                     email_otp:
 *                       type: string
 *                       example: 8078
 *                     email_otp_expiry:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T12:26:06.000Z
 *                     is_email_verified:
 *                       type: boolean
 *                       example: false
 *                     is_phone_verified:
 *                       type: boolean
 *                       example: false
 *                     status:
 *                       type: string
 *                       example: active
 *                     terms_condition:
 *                       type: boolean
 *                       example: true
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T12:11:06.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T12:11:07.000Z
 *                     token:
 *                       type: object
 *                       properties:
 *                         access:
 *                           type: object
 *                           properties:
 *                             token:
 *                               type: string
 *                               example: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *                             expires:
 *                               type: string
 *                               format: date-time
 *                               example: 2025-07-16T23:01:55.969Z
 *                         refresh:
 *                           type: object
 *                           properties:
 *                             token:
 *                               type: string
 *                               example: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *                             expires:
 *                               type: string
 *                               format: date-time
 *                               example: 2026-03-04T12:21:55.974Z
 *       400:
 *         description: Invalid email or password.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid email or password.
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */
/**
 * @swagger
 * /auth/resend-email-otp:
 *   post:
 *     tags:
 *       - Auth
 *     summary: Resend email OTP
 *     description: Resend the OTP to the user's registered email address.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *     responses:
 *       200:
 *         description: OTP resent successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: OTP resent successfully.
 *                 data:
 *                   type: "null"
 *                   example: null
 *       400:
 *         description: Invalid request or email not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Email not found.
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */
/**
 * @swagger
 * /auth/verify-email-otp:
 *   post:
 *     tags:
 *       - Auth
 *     summary: User login
 *     description: Authenticate a user using email and password.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - otp
 *             properties:
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               otp:
 *                 type: string
 *                 example: John@123
 *     responses:
 *       200:
 *         description: User successfully logged in.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Login successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 4
 *                     first_name:
 *                       type: string
 *                       example: John
 *                     middle_name:
 *                       type: string
 *                       example: Michael
 *                     last_name:
 *                       type: string
 *                       example: Doe
 *                     gender:
 *                       type: string
 *                       example: male
 *                     profile_created_for:
 *                       type: string
 *                       example: myself
 *                     date_of_birth:
 *                       type: string
 *                       format: date
 *                       example: 1990-01-01T00:00:00.000Z
 *                     email:
 *                       type: string
 *                       example: <EMAIL>
 *                     phone:
 *                       type: string
 *                       example: 9876543210
 *                     email_otp:
 *                       type: string
 *                       example: 8078
 *                     email_otp_expiry:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T12:26:06.000Z
 *                     is_email_verified:
 *                       type: boolean
 *                       example: false
 *                     is_phone_verified:
 *                       type: boolean
 *                       example: false
 *                     status:
 *                       type: string
 *                       example: active
 *                     terms_condition:
 *                       type: boolean
 *                       example: true
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T12:11:06.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-08T12:11:07.000Z
 *                     token:
 *                       type: object
 *                       properties:
 *                         access:
 *                           type: object
 *                           properties:
 *                             token:
 *                               type: string
 *                               example: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *                             expires:
 *                               type: string
 *                               format: date-time
 *                               example: 2025-07-16T23:01:55.969Z
 *                         refresh:
 *                           type: object
 *                           properties:
 *                             token:
 *                               type: string
 *                               example: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *                             expires:
 *                               type: string
 *                               format: date-time
 *                               example: 2026-03-04T12:21:55.974Z
 *       400:
 *         description: Invalid email or password.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid email or password.
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */
