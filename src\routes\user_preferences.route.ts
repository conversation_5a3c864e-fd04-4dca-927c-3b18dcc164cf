import express from "express";
import UserProfileController from "../app/user/user_profile/user_profile.controller";
import { auth } from "../middlewares/auth";
import { fileUploadMiddleware } from "../middlewares/fileUploadMiddleware";
import UserPreferenceController from "../app/user/user_preference/user_preferences.controller";


const router = express.Router();

router.get("",auth, UserPreferenceController.getAll);
router.post("",auth, UserPreferenceController.create);
router.put("/:id",auth, UserPreferenceController.update);
router.get("/:id",auth, UserPreferenceController.showById);
router.delete("/:id",auth, UserPreferenceController.delete);

export default router;
