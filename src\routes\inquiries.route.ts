import express from "express";
import InquiryController from "../app/inquiries/inquiries.controller";
import { auth } from "../middlewares/auth";

const router = express.Router();

// Public routes
router.post("", InquiryController.create);

// Admin routes (require authentication)
router.get("", auth, InquiryController.getAll);
router.get("/:id", auth, InquiryController.showById);
router.put("/:id/status", auth, InquiryController.updateStatus);
router.delete("/:id", auth, InquiryController.delete);

export default router;
