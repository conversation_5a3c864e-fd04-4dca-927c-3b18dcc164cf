"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const user_model_1 = __importDefault(require("../../database/models/user.model"));
const inquiries_model_1 = __importDefault(require("../../database/models/inquiries.model"));
const success_story_model_1 = __importDefault(require("../../database/models/success_story.model"));
const user_basic_details_model_1 = __importDefault(require("../../database/models/user_basic_details.model"));
const user_location_details_model_1 = __importDefault(require("../../database/models/user_location_details.model"));
const user_education_career_model_1 = __importDefault(require("../../database/models/user_education_career.model"));
const user_lifestyle_model_1 = __importDefault(require("../../database/models/user_lifestyle.model"));
const user_family_details_model_1 = __importDefault(require("../../database/models/user_family_details.model"));
const user_astro_details_model_1 = __importDefault(require("../../database/models/user_astro_details.model"));
const user_hobbies_model_1 = __importDefault(require("../../database/models/user_hobbies.model"));
const user_gallery_model_1 = __importDefault(require("../../database/models/user_gallery.model"));
const user_invitation_model_1 = __importDefault(require("../../database/models/user_invitation.model"));
const sequelize_1 = require("sequelize");
const user_preferences_model_1 = __importDefault(require("../../database/models/user_preferences.model"));
const user_subscriptions_model_1 = __importDefault(require("../../database/models/user_subscriptions.model"));
class DashboardService {
    constructor() { }
}
_a = DashboardService;
DashboardService.getDashboardData = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Get user profile data
        const user = yield user_model_1.default.findOne({
            where: { id: userId },
            attributes: ["id", "first_name", "last_name", "email", "profile_image"],
            include: [
                {
                    model: user_basic_details_model_1.default,
                    as: "basicDetails",
                    required: false,
                },
                {
                    model: user_location_details_model_1.default,
                    as: "locationDetails",
                    required: false,
                },
                {
                    model: user_education_career_model_1.default,
                    as: "educationCareer",
                    required: false,
                },
                {
                    model: user_lifestyle_model_1.default,
                    as: "lifestyle",
                    required: false,
                },
                {
                    model: user_family_details_model_1.default,
                    as: "familyDetails",
                    required: false,
                },
                {
                    model: user_astro_details_model_1.default,
                    as: "astroDetails",
                    required: false,
                },
                {
                    model: user_hobbies_model_1.default,
                    as: "hobbies",
                    required: false,
                },
                {
                    model: user_gallery_model_1.default,
                    as: "userGallery",
                    required: false,
                },
                {
                    model: user_preferences_model_1.default,
                    as: "userPreference",
                    required: false,
                },
            ],
        });
        // Calculate profile completion
        const profileCompletion = _a.calculateProfileCompletion(user);
        // Get invitation counts
        const invitationCounts = yield _a.getInvitationCounts(userId);
        return {
            user,
            profileCompletion,
            invitationCounts,
        };
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
DashboardService.calculateProfileCompletion = (user) => {
    const sections = [
        {
            name: "Basic Details",
            isComplete: !!user.basicDetails,
            weight: 20,
            description: "Add your personal details",
            icon: "fas fa-user",
        },
        {
            name: "Location Details",
            isComplete: !!user.locationDetails,
            weight: 15,
            description: "Add your location details",
            icon: "fas fa-map-marker-alt",
        },
        {
            name: "Education & Career",
            isComplete: !!user.educationCareer,
            weight: 15,
            description: "Add your education and career details",
            icon: "fas fa-graduation-cap",
        },
        {
            name: "Lifestyle",
            isComplete: !!user.lifestyle,
            weight: 15,
            description: "Add your lifestyle details",
            icon: "fas fa-shield-heart",
        },
        {
            name: "Family Details",
            isComplete: !!user.familyDetails,
            weight: 15,
            description: "Add your family details",
            icon: "fas fa-users",
        },
        {
            name: "Astro Details",
            isComplete: !!user.astroDetails,
            weight: 10,
            description: "Add your astrological details",
            icon: "fas fa-venus-mars",
        },
        {
            name: "Preferences",
            isComplete: !!user.userPreference,
            weight: 10,
            description: "Add your preferences",
            icon: "fas fa-cog",
        },
        // { name: 'Hobbies & Interests', isComplete: !!user.hobbies, weight: 10,description: 'Add your hobbies and interests',icon:'interests' },
        // { name: 'Profile Bio & Photos', isComplete: !!(user.profileBioDetails && user.profileBioDetails.profile_image), weight: 10,description: 'Add your profile bio and photos',icon:'face' }
    ];
    // Calculate total completion percentage
    let completedWeight = 0;
    sections.forEach((section) => {
        if (section.isComplete) {
            completedWeight += section.weight;
        }
    });
    // Round to nearest whole number
    const completionPercentage = Math.round(completedWeight);
    return {
        completionPercentage,
        sections: sections.map((section) => ({
            name: section.name,
            isComplete: section.isComplete,
            weight: section.weight,
            description: section.description,
            icon: section.icon,
        })),
        pendingSections: sections
            .filter((section) => !section.isComplete)
            .map((section) => section.name),
    };
};
DashboardService.getInvitationCounts = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    // Count invitations by status
    const sentCount = yield user_invitation_model_1.default.count({
        where: { sender_id: userId },
    });
    const receivedCount = yield user_invitation_model_1.default.count({
        where: { receiver_id: userId },
    });
    const acceptedCount = yield user_invitation_model_1.default.count({
        where: {
            [sequelize_1.Op.or]: [
                { sender_id: userId, status: "accepted" },
                { receiver_id: userId, status: "accepted" },
            ],
        },
    });
    const rejectedCount = yield user_invitation_model_1.default.count({
        where: {
            [sequelize_1.Op.or]: [
                { sender_id: userId, status: "declined" },
                { receiver_id: userId, status: "declined" },
            ],
        },
    });
    const pendingCount = yield user_invitation_model_1.default.count({
        where: {
            [sequelize_1.Op.or]: [
                { sender_id: userId, status: "pending" },
                { receiver_id: userId, status: "pending" },
            ],
        },
    });
    return {
        sent: sentCount,
        received: receivedCount,
        accepted: acceptedCount,
        rejected: rejectedCount,
        pending: pendingCount,
    };
});
DashboardService.getAdminDashboardData = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const totalUsers = yield user_model_1.default.count();
        const totalNewUsers = yield user_model_1.default.count({
            where: { status: "new" },
        });
        const totalPendingUsers = yield user_model_1.default.count({
            where: { status: "pending" },
        });
        const totalApprovedUsers = yield user_model_1.default.count({
            where: { status: "approved" },
        });
        const totalRejectedUsers = yield user_model_1.default.count({
            where: { status: "rejected" },
        });
        const totalBlockedUsers = yield user_model_1.default.count({
            where: { status: "blocked" },
        });
        const totalDeactivatedUsers = yield user_model_1.default.count({
            where: { status: "deactivated" },
        });
        const totalActiveUserSubscription = yield user_subscriptions_model_1.default.count({
            where: { is_active: true },
        });
        const totalInquiries = yield inquiries_model_1.default.count();
        const totalSuccessStories = yield success_story_model_1.default.count();
        return {
            totalUsers,
            totalNewUsers,
            totalPendingUsers,
            totalApprovedUsers,
            totalRejectedUsers,
            totalBlockedUsers,
            totalDeactivatedUsers,
            totalInquiries,
            totalSuccessStories,
            totalActiveUserSubscription,
        };
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = DashboardService;
