"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middlewares/auth");
const privacy_setting_controller_1 = __importDefault(require("../app/privacy_setting/privacy_setting.controller"));
const middleware_1 = require("../middlewares/middleware");
const privacy_setting_validation_1 = require("../validations/privacy_setting.validation");
const router = express_1.default.Router();
router.get("", auth_1.auth, privacy_setting_controller_1.default.getAll);
router.post("", auth_1.auth, (0, middleware_1.validate)(privacy_setting_validation_1.privacySettingValidation), privacy_setting_controller_1.default.create);
router.post("/hide-profile", auth_1.auth, privacy_setting_controller_1.default.hideProfile);
router.post("/delete-profile", auth_1.auth, privacy_setting_controller_1.default.deleteProfile);
router.post("/resend-email-otp", auth_1.auth, privacy_setting_controller_1.default.resendEmailOtp);
router.post("/verify-email-otp", auth_1.auth, privacy_setting_controller_1.default.verifyEmailOtp);
router.post("/send-phone-otp", auth_1.auth, privacy_setting_controller_1.default.sendPhoneOtp);
router.post("/verify-phone-otp", auth_1.auth, privacy_setting_controller_1.default.verifyPhoneOtp);
router.get("/:id", auth_1.auth, privacy_setting_controller_1.default.showById);
router.put("/:id", auth_1.auth, privacy_setting_controller_1.default.update);
router.delete("/:id", auth_1.auth, privacy_setting_controller_1.default.delete);
exports.default = router;
