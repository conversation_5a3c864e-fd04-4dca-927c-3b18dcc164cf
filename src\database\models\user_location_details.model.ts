import {
    Table,
    Model,
    Column,
    DataType,
    PrimaryKey,
    AutoIncrement,
    ForeignKey,
    AllowNull,
    BelongsTo,
    Default,
    Index,
} from "sequelize-typescript";
import User from "./user.model"; // Adjust import path based on your structure
import Country from "./country.model";
import City from "./city.model";

interface UserLocationDetailsI {
    id: number;
    user_id: number;
    country_living_in: number;
    city: number;
    residency_status: string;
}

@Table({
    tableName: "user_location_details",
    timestamps: false,
})
class UserLocationDetails extends Model<UserLocationDetailsI> implements UserLocationDetailsI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    @ForeignKey(() => Country)
    @AllowNull(true)
    @Column(DataType.INTEGER)
    country_living_in: number;

    @BelongsTo(() => Country, 'country_living_in')
    country: Country;

    @ForeignKey(() => City)
    @AllowNull(true)
    @Column(DataType.INTEGER)
    city: number;

    @BelongsTo(() => City, 'city')
    cities: City;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    residency_status: string;
}

export default UserLocationDetails;