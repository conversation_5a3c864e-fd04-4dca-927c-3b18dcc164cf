import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";
import httpMessages from "../../config/httpMessages";
import ApiError from "../../utils/ApiError";
import PaypalService from "./paypal.service";

export default class PaypalController {
    static paypalService = PaypalService;
    constructor() { }

    // Create PayPal order
    static createOrder = catchAsync(async (request: Request, response: Response) => {
        try {
            const { plan_id, amount, currency } = request.body;
            const user_id = request.decoded;

            const order = await this.paypalService.createOrder({
                user_id,
                plan_id,
                amount,
                currency
            });

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "PayPal order created successfully",
                data: order,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Capture PayPal payment
    static capturePayment = catchAsync(async (request: Request, response: Response) => {
        try {
            const { order_id } = request.params;
            const user_id = request.decoded;

            const capturedPayment = await this.paypalService.capturePayment(order_id, user_id);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Payment captured successfully",
                data: capturedPayment,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Get payment details
    static getPaymentDetails = catchAsync(async (request: Request, response: Response) => {
        try {
            const { payment_id } = request.params;
            const user_id = request.decoded.id;

            const paymentDetails = await this.paypalService.getPaymentDetails(payment_id, user_id);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Payment details retrieved successfully",
                data: paymentDetails,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Get user payment history
    static getUserPayments = catchAsync(async (request: Request, response: Response) => {
        try {
            const user_id = request.decoded;
            const { page, limit, search } = request.query;
            const option = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
                search: search ? (search as string) : "",
                user_id: user_id.id
            };

            const payments = await this.paypalService.getUserPayments(option);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "User payments retrieved successfully",
                data: payments,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // PayPal webhook handler
    static handleWebhook = catchAsync(async (request: Request, response: Response) => {
        try {
            const webhookEvent = request.body;
            const webhookId = request.headers['paypal-transmission-id'] as string;
            console.log('webhookId: ', webhookId);

            await this.paypalService.handleWebhookEvent(webhookEvent, webhookId);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Webhook processed successfully",
                data: { received: true },
            });
        } catch (error: any) {
            console.error('Webhook processing error:', error);
            return errorResponse(response, error);
        }
    });

    // Get payment analytics (admin)
    static getPaymentAnalytics = catchAsync(async (request: Request, response: Response) => {
        try {
            const { start_date, end_date, user_id } = request.query;

            const analytics = await this.paypalService.getPaymentAnalytics({
                start_date: start_date as string,
                end_date: end_date as string,
                user_id: user_id ? Number(user_id) : undefined
            });

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Payment analytics retrieved successfully",
                data: analytics,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Refund payment
    static refundPayment = catchAsync(async (request: Request, response: Response) => {
        try {
            const { payment_id } = request.params;
            const { amount, reason } = request.body;

            const refund = await this.paypalService.refundPayment(payment_id, {
                amount,
                reason
            });

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Payment refunded successfully",
                data: refund,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    // Success page handler
    static handleSuccess = catchAsync(async (request: Request, response: Response) => {
        try {
            const { token, PayerID } = request.query;

            // Redirect to frontend with success parameters
            const redirectUrl = `${process.env.FRONTEND_URL}/payment/success?token=${token}&PayerID=${PayerID}`;
            response.redirect(redirectUrl);
        } catch (error: any) {
            const redirectUrl = `${process.env.FRONTEND_URL}/payment/error`;
            response.redirect(redirectUrl);
        }
    });

    // Cancel page handler
    static handleCancel = catchAsync(async (request: Request, response: Response) => {
        try {
            const { token } = request.query;

            // Redirect to frontend with cancel parameters
            const redirectUrl = `${process.env.FRONTEND_URL}/payment/cancel?token=${token}`;
            response.redirect(redirectUrl);
        } catch (error: any) {
            const redirectUrl = `${process.env.FRONTEND_URL}/payment/error`;
            response.redirect(redirectUrl);
        }
    });

    // Error page handler
    static getTransactions = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page, limit, search, status } = request.query;
            const option = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
                search: search ? (search as string) : "",
                status: status ? (status as string) : undefined,
            };
            const transactions = await this.paypalService.getTransactions(option);

            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: "Transactions retrieved successfully",
                data: transactions,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

}
