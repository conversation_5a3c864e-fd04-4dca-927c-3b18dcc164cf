"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const bcrypt_1 = __importDefault(require("bcrypt"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const user_service_1 = __importDefault(require("../user/user.service"));
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
const token_service_1 = __importDefault(require("../../common/services/token.service"));
const token_model_1 = __importDefault(require("../../database/models/token.model"));
const enums_1 = require("../../database/config/enums");
const user_verifications_model_1 = __importDefault(require("../../database/models/user_verifications.model"));
const user_model_1 = __importDefault(require("../../database/models/user.model"));
// const client = twilio(process.env.TWILIO_ACCOUNT_SID!, process.env.TWILIO_AUTH_TOKEN!);
class AuthService {
    constructor() { }
}
_a = AuthService;
AuthService.userService = user_service_1.default;
AuthService.tokenService = token_service_1.default;
AuthService.isPasswordMatch = (user, password) => __awaiter(void 0, void 0, void 0, function* () { return yield bcrypt_1.default.compare(password, user.password); });
/**
 * Login with username and password
 * @param {string} email
 * @param {string} password
 * @returns {Promise<User>}
 */
AuthService.loginUserWithEmailAndPassword = (email, password) => __awaiter(void 0, void 0, void 0, function* () {
    const MAX_FAILED_ATTEMPTS = 3;
    const SUSPENSION_TIME_MINUTES = 30;
    const user = yield _a.userService
        .getUserByEmail(email, { shouldReturnPassword: true })
        .then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
    if (!user) {
        throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.LOGIN.INCORRECT_EMAIL);
    }
    if (user.isSuspended) {
        const minutesPassed = (Date.now() - new Date(user.suspendedAt).getTime()) / 60000;
        if (minutesPassed < SUSPENSION_TIME_MINUTES) {
            throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, `Account suspended. Please try again in ${Math.ceil(SUSPENSION_TIME_MINUTES - minutesPassed)} minutes.`);
        }
        else {
            yield user_model_1.default.update({ isSuspended: false, failedLoginAttempts: 0, suspendedAt: null }, { where: { id: user.id } });
        }
    }
    if (!(yield _a.isPasswordMatch(user, password))) {
        yield user_model_1.default.update({ failedLoginAttempts: user.failedLoginAttempts + 1 }, { where: { id: user.id } });
        if (user.failedLoginAttempts + 1 >= MAX_FAILED_ATTEMPTS) {
            yield user_model_1.default.update({ isSuspended: true, suspendedAt: new Date() }, { where: { id: user.id } });
            throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, `Account suspended. Please try again in ${SUSPENSION_TIME_MINUTES} minutes.`);
        }
        throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.LOGIN.INCORRECT_PASS);
    }
    yield user_model_1.default.update({ failedLoginAttempts: 0 }, { where: { id: user.id } });
    user.password = undefined;
    return yield user_model_1.default.findOne({
        where: { id: user.id },
        attributes: { exclude: ["password"] },
        include: [
            {
                model: user_verifications_model_1.default,
                as: "verification",
            },
        ],
    }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
});
/**
 * Logout
 * @param {string} refreshToken
 * @returns {Promise}
 */
AuthService.logout = (token) => __awaiter(void 0, void 0, void 0, function* () {
    const refreshTokenDoc = yield token_model_1.default.findOne({
        where: {
            token,
            type: enums_1.TOKEN_TYPES.REFRESH,
            blacklisted: false
        }
    });
    if (!refreshTokenDoc) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.REFRESH_TOKEN_ERROR);
    }
    yield refreshTokenDoc.destroy();
});
/**
 * Refresh auth tokens
 * @param {string} refreshToken
 * @returns {Promise<Object>}
 */
AuthService.refreshAuth = (refreshToken) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const refreshTokenDoc = yield _a.tokenService.verifyToken(refreshToken, enums_1.TOKEN_TYPES.REFRESH);
        const user = yield _a.userService.getUserById(refreshTokenDoc.userId);
        if (!user) {
            throw new Error();
        }
        return _a.tokenService.generateAccessToken(user);
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED);
    }
});
/**
 * Reset password
 * @param {string} resetPasswordToken
 * @param {string} newPassword
 * @returns {Promise}
 */
AuthService.resetPassword = (resetPasswordToken, newPassword) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const resetPasswordTokenDoc = yield _a.tokenService.verifyToken(resetPasswordToken, enums_1.TOKEN_TYPES.RESET_PASSWORD);
        const user = yield user_model_1.default.findByPk(resetPasswordTokenDoc.user_id);
        if (!user) {
            throw new Error(httpMessages_1.default.USER.EMAIL_NOT_FOUND);
        }
        Object.assign(user, { password: newPassword });
        yield user.save();
        yield token_model_1.default.destroy({
            where: { user_id: user.id, type: enums_1.TOKEN_TYPES.RESET_PASSWORD }
        });
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = AuthService;
