"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getActiveUsers = exports.initializeSocket = void 0;
const socket_io_1 = require("socket.io");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("../config/config");
const chat_model_1 = __importDefault(require("../database/models/chat.model"));
const message_model_1 = __importDefault(require("../database/models/message.model"));
const EncryptionResponse_1 = require("../middlewares/EncryptionResponse");
// Store active users
const activeUsers = new Map();
const initializeSocket = (server) => {
    const io = new socket_io_1.Server(server, {
        cors: {
            origin: '*',
            methods: ['GET', 'POST']
        }
    });
    // Middleware for authentication
    io.use((socket, next) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            const token = socket.handshake.auth.token;
            if (!token) {
                return next(new Error('Authentication error: Token not provided'));
            }
            let checkToken = (token === null || token === void 0 ? void 0 : token.split(" ")[1]) || "";
            const decoded = jsonwebtoken_1.default.verify(checkToken, config_1.config.jwt.secret);
            const user = decoded.sub;
            if (!user) {
                return next(new Error('Authentication error: Invalid token'));
            }
            socket.data.user_id = user;
            next();
        }
        catch (error) {
            next(new Error('Authentication error: Invalid token'));
        }
    }));
    io.on('connection', (socket) => {
        console.log('User connected:================', socket.id);
        // Store user connection
        if (socket.data.user_id) {
            const userId = socket.data.user_id;
            activeUsers.set(userId, socket.id);
            socket.emit("online-users", Array.from(activeUsers.keys()));
            // Notify friends that user is online
            socket.broadcast.emit('user_status', { userId, status: 'online' });
        }
        // Handle private messages
        socket.on('private_message', (data) => __awaiter(void 0, void 0, void 0, function* () {
            try {
                const { chatId, receiverId, content, temp_id } = data;
                const senderId = socket.data.user_id;
                // Encrypt message content
                const iv = 'v@XI!kaW3BK,@8ki';
                const secretKey = 'Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn';
                const encryptedContent = (0, EncryptionResponse_1.encryptData)(content, secretKey, iv);
                // Save message to database
                const message = yield message_model_1.default.create({
                    chat_id: chatId,
                    sender_id: senderId,
                    receiver_id: receiverId,
                    content: content,
                    is_encrypted: true,
                    is_delivered: false,
                    is_read: false,
                    delivered_at: null,
                    read_at: null
                });
                // Update last message time in chat
                yield chat_model_1.default.update({ last_message_at: new Date() }, { where: { id: chatId } });
                // Send message to receiver if online
                const receiverSocketId = activeUsers.get(receiverId);
                if (receiverSocketId) {
                    io.to(receiverSocketId).emit('new_message', {
                        messageId: message.id,
                        chatId,
                        senderId,
                        content: content,
                        createdAt: message.createdAt
                    });
                    // Mark as delivered
                    yield message_model_1.default.update({ is_delivered: true, delivered_at: new Date() }, { where: { id: message.id } });
                    // Notify sender that message was delivered
                    socket.emit('message_delivered', { messageId: message.id, temp_id: temp_id });
                }
                socket.emit('sent_message', { messageId: message.id, temp_id: temp_id });
            }
            catch (error) {
                console.error('Error sending message:', error);
                socket.emit('error', { message: 'Failed to send message' });
            }
        }));
        // Handle message read status
        socket.on('message_read', (data) => __awaiter(void 0, void 0, void 0, function* () {
            try {
                const { messageId } = data;
                // Update message as read
                yield message_model_1.default.update({ is_read: true, read_at: new Date(), is_delivered: true, delivered_at: new Date() }, { where: { id: messageId } });
                const message = yield message_model_1.default.findByPk(messageId);
                if (message) {
                    // Notify sender that message was read
                    const senderSocketId = activeUsers.get(message.sender_id);
                    if (senderSocketId) {
                        io.to(senderSocketId).emit('message_delivered', { messageId });
                        io.to(senderSocketId).emit('message_read', { messageId });
                    }
                }
            }
            catch (error) {
                console.error('Error marking message as read:', error);
            }
        }));
        socket.on('typing_start', (data) => {
            const { chatId, userId } = data;
            const senderId = socket.data.user_id;
            const receiverSocketId = activeUsers.get(userId);
            if (receiverSocketId) {
                io.to(receiverSocketId).emit('user_typing_start', { chatId, senderId });
            }
        });
        socket.on('typing_stop', (data) => {
            const { chatId, userId } = data;
            const senderId = socket.data.user_id;
            const receiverSocketId = activeUsers.get(userId);
            if (receiverSocketId) {
                io.to(receiverSocketId).emit('user_typing_stop', { chatId, senderId });
            }
        });
        // Handle disconnection
        socket.on('disconnect', () => {
            console.log('User disconnected:=====================', socket.id);
            if (socket.data.user_id) {
                const userId = socket.data.user_id;
                activeUsers.delete(userId);
                // Notify friends that user is offline
                socket.broadcast.emit('user_status', { userId, status: 'offline' });
            }
        });
    });
    return io;
};
exports.initializeSocket = initializeSocket;
const getActiveUsers = () => {
    return activeUsers;
};
exports.getActiveUsers = getActiveUsers;
