"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const moment_1 = __importDefault(require("moment"));
const sequelize_1 = require("sequelize");
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const config_1 = require("../../config/config");
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
const enums_1 = require("../../database/config/enums");
const token_model_1 = __importDefault(require("../../database/models/token.model"));
const user_service_1 = __importDefault(require("../../app/user/user.service"));
const user_subscriptions_model_1 = __importDefault(require("../../database/models/user_subscriptions.model"));
class TokenService {
    constructor() { }
}
_a = TokenService;
TokenService.userService = user_service_1.default;
/**
 * Generate token
 * @param {ObjectId} userId
 * @param {Moment} expires
 * @param {string} type
 * @param {string} [secret]
 * @returns {string}
 */
TokenService.generateToken = (userId, expires, type, secret) => {
    var _b;
    if (secret === void 0) { secret = (_b = config_1.config === null || config_1.config === void 0 ? void 0 : config_1.config.jwt) === null || _b === void 0 ? void 0 : _b.secret; }
    const payload = {
        sub: userId,
        iat: (0, moment_1.default)().unix(),
        exp: expires.unix(),
        type
    };
    return `Bearer ${jsonwebtoken_1.default.sign(payload, secret)}`;
};
/**
 * Get token by userId
 * @param {number} userId
 * @returns {Promise<Token>}
 */
TokenService.getTokenByUser = (userId, type) => token_model_1.default.findOne({ where: { user_id: userId, type } });
/**
 * Save a token
 * @param {string} token
 * @param {number} userId
 * @param {Moment} expires
 * @param {string} type
 * @param {boolean} [blacklisted]
 * @returns {Promise<Token>}
 */
TokenService.saveToken = (token_1, userId_1, expires_1, type_1, ...args_1) => __awaiter(void 0, [token_1, userId_1, expires_1, type_1, ...args_1], void 0, function* (token, userId, expires, type, blacklisted = false) {
    const tokenResponse = yield _a.getTokenByUser(userId, type);
    if (tokenResponse) {
        Object.assign(tokenResponse, {
            token,
            user_id: userId,
            expires_at: expires.toDate(),
            type,
            blacklisted
        });
        return yield tokenResponse.save();
    }
    else {
        return yield token_model_1.default.create({
            token,
            user_id: userId,
            expires_at: expires.toDate(),
            type,
            blacklisted
        });
    }
});
/**
 * Generate auth tokens
 * @param {User} user
 * @returns {Promise<Object>}
 */
TokenService.generateAuthTokens = (user, is_admin) => __awaiter(void 0, void 0, void 0, function* () {
    const userId = user === null || user === void 0 ? void 0 : user.id;
    // Generate access token
    const accessTokenExpires = (0, moment_1.default)().add(config_1.config.jwt.accessExpirationMinutes, "minutes");
    const accessToken = _a.generateToken(userId, accessTokenExpires, enums_1.TOKEN_TYPES.ACCESS);
    // Check if user has an active subscription
    const subscription = yield user_subscriptions_model_1.default.findOne({
        where: {
            user_id: userId,
            is_active: true,
            // Make sure subscription hasn't expired
            end_date: {
                [sequelize_1.Op.gt]: new Date()
            }
        }
    });
    // Generate refresh token
    const refreshTokenExpires = (0, moment_1.default)().add(config_1.config.jwt.refreshExpirationDays, "days");
    const refreshToken = _a.generateToken(userId, refreshTokenExpires, enums_1.TOKEN_TYPES.REFRESH);
    // If user has an active subscription, save the refresh token in the database
    if (subscription) {
        // Save the refresh token in the token table
        yield _a.saveToken(refreshToken, userId, refreshTokenExpires, enums_1.TOKEN_TYPES.REFRESH);
        // Update the subscription token if needed
        if (!subscription.token) {
            subscription.token = refreshToken;
            yield subscription.save();
        }
    }
    return {
        access: {
            token: accessToken,
            expires: accessTokenExpires.toDate()
        },
        refresh: {
            token: refreshToken,
            expires: refreshTokenExpires.toDate()
        }
    };
});
/**
 * Verify token and return token doc (or throw an error if it is not valid)
 * @param {string} bearerToken
 * @param {string} type
 * @returns {Promise<Token>}
 */
TokenService.verifyToken = (bearerToken, type) => __awaiter(void 0, void 0, void 0, function* () {
    const token = bearerToken.split(" ")[1];
    const payload = jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret);
    console.log('payload: ', payload);
    const tokenDoc = yield token_model_1.default.findOne({
        where: {
            token: bearerToken,
            type,
            user_id: payload.sub,
            blacklisted: false
        }
    });
    if (!tokenDoc) {
        throw new Error(httpMessages_1.default.USER.AUTH.TOKEN.EXPIRED);
    }
    return tokenDoc;
});
/**
 * Generate reset password token
 * @param {string} email
 * @returns {Promise<string>}
 */
TokenService.generateResetPasswordToken = (email) => __awaiter(void 0, void 0, void 0, function* () {
    const user = yield _a.userService.getUserByEmail(email);
    if (!user) {
        throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER.EMAIL_NOT_FOUND);
    }
    const expires = (0, moment_1.default)().add(config_1.config.jwt.resetPasswordExpirationMinutes, "minutes");
    const resetPasswordToken = _a.generateToken(user.id, expires, enums_1.TOKEN_TYPES.RESET_PASSWORD);
    yield _a.saveToken(resetPasswordToken, user.id, expires, enums_1.TOKEN_TYPES.RESET_PASSWORD);
    return resetPasswordToken;
});
/**
 * Generate verify email token
 * @param {User} user
 * @returns {Promise<string>}
 */
TokenService.generateVerifyEmailToken = (user) => __awaiter(void 0, void 0, void 0, function* () {
    const expires = (0, moment_1.default)().add(config_1.config.jwt.verifyEmailExpirationMinutes, "minutes");
    const verifyEmailToken = _a.generateToken(user.id, expires, enums_1.TOKEN_TYPES.VERIFY_EMAIL);
    yield _a.saveToken(verifyEmailToken, user.id, expires, enums_1.TOKEN_TYPES.VERIFY_EMAIL);
    return verifyEmailToken;
});
/**
 * Generate Access tokens
 * @param {User} user
 * @returns {Promise<Object>}
 */
TokenService.generateAccessToken = (user) => __awaiter(void 0, void 0, void 0, function* () {
    const accessTokenExpires = (0, moment_1.default)().add(config_1.config.jwt.accessExpirationMinutes, "minutes");
    const accessToken = _a.generateToken(user.id, accessTokenExpires, enums_1.TOKEN_TYPES.ACCESS);
    return {
        token: accessToken,
        expires: accessTokenExpires.toDate()
    };
});
exports.default = TokenService;
