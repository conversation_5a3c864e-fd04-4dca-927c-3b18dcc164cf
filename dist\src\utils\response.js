"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sentResponse = void 0;
const errorResponse = (response, error) => {
    const statusCode = error.statusCode || 500;
    const message = error.message || "Internal server error!";
    response.status(statusCode).json({
        success: false,
        message,
    });
};
const sentResponse = (response, success) => {
    const statusCode = (success === null || success === void 0 ? void 0 : success.statusCode) || 500;
    const message = (success === null || success === void 0 ? void 0 : success.message) || "Internal server error!";
    const data = (success === null || success === void 0 ? void 0 : success.data) || null;
    return response.status(statusCode).send({
        success: true,
        message,
        data,
    });
};
exports.sentResponse = sentResponse;
exports.default = errorResponse;
