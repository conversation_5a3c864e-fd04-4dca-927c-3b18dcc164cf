"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../../utils/catchAsync"));
const response_1 = __importStar(require("../../../utils/response"));
const httpMessages_1 = __importDefault(require("../../../config/httpMessages"));
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const user_preferences_service_1 = __importDefault(require("./user_preferences.service"));
class UserPreferenceController {
    constructor() { }
}
_a = UserPreferenceController;
UserPreferenceController.userPreferenceService = user_preferences_service_1.default;
UserPreferenceController.getAll = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search } = request.query;
        const decoded = request.decoded;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search ? search : "",
        };
        const list = yield _a.userPreferenceService.getMatchingUsersByPreference(decoded, option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USERPRE.SUCCESS,
            data: list,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserPreferenceController.create = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const body = Object.assign({}, request.body);
        body['user_id'] = request.decoded;
        const craetedData = yield _a.userPreferenceService.createUserPreference(body);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USERPRE.ADD_SUCCESS,
            data: craetedData,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserPreferenceController.showById = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const Id = parseInt(request.params.id, 10);
        const details = yield _a.userPreferenceService.getUserPreferenceById(Id);
        if (!details) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USERPRE.NOT_FOUND);
        }
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USERPRE.DETAILS.SUCCESS,
            data: details,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserPreferenceController.update = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const Id = parseInt(request.params.id, 10);
        const body = Object.assign({}, request.body);
        const updatedData = yield _a.userPreferenceService.updateUserPreferenceById(Id, body);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USERPRE.UPDATE_SUCCESS,
            data: updatedData,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
UserPreferenceController.delete = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const id = parseInt(request.params.id, 10);
        yield _a.userPreferenceService.deleteUserPreferenceById(id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USERPRE.DELETE,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = UserPreferenceController;
