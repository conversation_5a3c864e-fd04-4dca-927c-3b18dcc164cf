import express from "express";

import { registerValidation, loginValidation, logoutValidation, refreshTokensValidation, forgotPasswordValidation, resetPasswordValidation, verifyEmailValidation, verifyOtpValidation, resentOtpValidation } from "../validations/auth.validation";
import { validate } from "../middlewares/middleware";
import AuthController from "../app/auth/auth.controller";
import { fileUploadMiddleware } from "../middlewares/fileUploadMiddleware";

const router = express.Router();

router.post("/register", validate(registerValidation), AuthController.register);
router.post("/login", validate(loginValidation), AuthController.login);
router.post("/logout", validate(logoutValidation), AuthController.logout);
router.post(
  "/refresh-tokens",
  validate(refreshTokensValidation),
  AuthController.refreshTokens
);
router.post(
  "/forgot-password",
  validate(forgotPasswordValidation),
  AuthController.forgotPassword
);
router.post(
  "/reset-password",
  validate(resetPasswordValidation),
  AuthController.resetPassword
);
// router.post('/send-verification-email', auth, AuthController.sendVerificationEmail);
router.post('/verify-email-otp', validate(verifyOtpValidation), AuthController.verifyEmailOtp);
router.post('/resend-email-otp', validate(resentOtpValidation), AuthController.resendEmailOtp);



export default router;
