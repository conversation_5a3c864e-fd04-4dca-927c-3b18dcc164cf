import httpStatus from "http-status";
import ApiError from "../../utils/ApiError";
import PromoCode from "../../database/models/promo_codes.model";
import PromoCodeUsage from "../../database/models/promo_code_usage.model";
import { Op } from "sequelize";

interface CreatePromoCodeParams {
    code: string;
    description?: string;
    discount_type: "percentage" | "fixed_amount";
    discount_value: number;
    minimum_purchase_amount?: number;
    maximum_discount_amount?: number;
    usage_limit?: number;
    start_date?: Date;
    expiry_date?: Date;
    applicable_plans?: number[];
    first_time_users_only?: boolean;
    is_active?: boolean;
    created_by?: number;
}

interface UpdatePromoCodeParams {
    description?: string;
    discount_value?: number;
    minimum_purchase_amount?: number;
    maximum_discount_amount?: number;
    usage_limit?: number;
    start_date?: Date;
    expiry_date?: Date;
    applicable_plans?: number[];
    first_time_users_only?: boolean;
    is_active?: boolean;
}

export default class AdminPromoCodeService {
    /**
     * Create a new promo code
     * @param {CreatePromoCodeParams} params
     * @returns {Promise<PromoCode>}
     */
    static createPromoCode = async (params: CreatePromoCodeParams): Promise<PromoCode> => {
        try {
            const {
                code,
                description,
                discount_type,
                discount_value,
                minimum_purchase_amount,
                maximum_discount_amount,
                usage_limit,
                start_date,
                expiry_date,
                applicable_plans,
                first_time_users_only = false,
                is_active = true,
                created_by
            } = params;

            // Check if promo code already exists
            const existingPromoCode = await PromoCode.findOne({
                where: { code: code.toUpperCase() }
            });

            if (existingPromoCode) {
                throw new ApiError(httpStatus.CONFLICT, "Promo code already exists");
            }

            // Validate discount value based on type
            if (discount_type === "percentage" && discount_value > 100) {
                throw new ApiError(httpStatus.BAD_REQUEST, "Percentage discount cannot exceed 100%");
            }

            // Validate dates
            if (start_date && expiry_date && start_date >= expiry_date) {
                throw new ApiError(httpStatus.BAD_REQUEST, "Start date must be before expiry date");
            }

            let body: any = {
                code: code.toUpperCase(),
                description,
                discount_type,
                discount_value,
                minimum_purchase_amount,
                maximum_discount_amount,
                usage_limit,
                used_count: 0,
                start_date,
                expiry_date,
                applicable_plans: applicable_plans ? JSON.stringify(applicable_plans) : null,
                first_time_users_only,
                is_active,
                created_by
            }

            const promoCode = await PromoCode.create(body);

            return promoCode;

        } catch (error: any) {
            console.error('Create promo code error:', error);
            throw error;
        }
    };

    /**
     * Get all promo codes with pagination and search
     * @param {Object} options
     * @returns {Promise<Object>}
     */
    static getPromoCodes = async (options: {
        page?: number;
        limit?: number;
        search?: string;
        is_active?: boolean;
    }) => {
        try {
            const { page, limit, search, is_active } = options;
            const whereCondition: any = search
                ? {
                    [Op.or]: [
                        { code: { [Op.like]: `%${search.toLowerCase()}%` } },
                        { description: { [Op.like]: `%${search.toLowerCase()}%` } },
                    ],
                }
                : {};

            if (is_active !== undefined) {
                whereCondition.is_active = is_active;
            }

            const queryOption: any = {
                where: whereCondition,
                include: [
                    {
                        model: PromoCodeUsage,
                        attributes: ['id'],
                        required: false
                    }
                ],
                order: [["createdAt", "DESC"]],
            };



            if (page && limit) {
                const offset = (page - 1) * limit;
                queryOption.limit = limit;
                queryOption.offset = offset;
            }
            const promoCodes = await PromoCode.findAndCountAll(queryOption);
            if (page && limit) {
                return {
                    totalItems: promoCodes.count,
                    totalPages: Math.ceil(promoCodes.count / limit),
                    currentPage: page,
                    promoCodes: promoCodes.rows,
                };
            } else {
                return promoCodes.rows;
            }

        } catch (error: any) {
            console.error('Get promo codes error:', error);
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error.message);
        }
    };

    /**
     * Get promo code by ID
     * @param {number} id
     * @returns {Promise<PromoCode>}
     */
    static getPromoCodeById = async (id: number): Promise<PromoCode> => {
        try {
            const promoCode = await PromoCode.findByPk(id, {
                include: [
                    {
                        model: PromoCodeUsage,
                        attributes: ['id', 'user_id', 'discount_amount', 'used_at'],
                        limit: 10,
                        order: [['used_at', 'DESC']]
                    }
                ]
            });

            if (!promoCode) {
                throw new ApiError(httpStatus.NOT_FOUND, "Promo code not found");
            }

            return promoCode;

        } catch (error: any) {
            console.error('Get promo code by ID error:', error);
            throw error;
        }
    };

    /**
     * Update promo code
     * @param {number} id
     * @param {UpdatePromoCodeParams} params
     * @returns {Promise<PromoCode>}
     */
    static updatePromoCode = async (id: number, params: UpdatePromoCodeParams): Promise<PromoCode> => {
        try {
            const promoCode = await PromoCode.findByPk(id);

            if (!promoCode) {
                throw new ApiError(httpStatus.NOT_FOUND, "Promo code not found");
            }

            // Validate discount value if being updated
            if (params.discount_value && promoCode.discount_type === "percentage" && params.discount_value > 100) {
                throw new ApiError(httpStatus.BAD_REQUEST, "Percentage discount cannot exceed 100%");
            }

            // Validate dates if being updated
            const newStartDate = params.start_date || promoCode.start_date;
            const newExpiryDate = params.expiry_date || promoCode.expiry_date;

            if (newStartDate && newExpiryDate && newStartDate >= newExpiryDate) {
                throw new ApiError(httpStatus.BAD_REQUEST, "Start date must be before expiry date");
            }

            const updateData: any = { ...params };

            if (params.applicable_plans) {
                updateData.applicable_plans = JSON.stringify(params.applicable_plans);
            }

            await promoCode.update(updateData);

            return promoCode;

        } catch (error: any) {
            console.error('Update promo code error:', error);
            throw error;
        }
    };

    /**
     * Delete promo code
     * @param {number} id
     * @returns {Promise<void>}
     */
    static deletePromoCode = async (id: number): Promise<void> => {
        try {
            const promoCode = await PromoCode.findByPk(id);

            if (!promoCode) {
                throw new ApiError(httpStatus.NOT_FOUND, "Promo code not found");
            }

            // Check if promo code has been used
            const usageCount = await PromoCodeUsage.count({
                where: { promo_code_id: id }
            });

            if (usageCount > 0) {
                throw new ApiError(httpStatus.BAD_REQUEST, "Cannot delete promo code that has been used");
            }

            await promoCode.destroy();

        } catch (error: any) {
            console.error('Delete promo code error:', error);
            throw error;
        }
    };

    /**
     * Get promo code usage statistics
     * @param {number} id
     * @returns {Promise<Object>}
     */
    static getPromoCodeStats = async (id: number) => {
        try {
            const promoCode = await PromoCode.findByPk(id);

            if (!promoCode) {
                throw new ApiError(httpStatus.NOT_FOUND, "Promo code not found");
            }

            const usageStats = await PromoCodeUsage.findAll({
                where: { promo_code_id: id },
                attributes: [
                    [PromoCodeUsage.sequelize!.fn('COUNT', PromoCodeUsage.sequelize!.col('id')), 'total_uses'],
                    [PromoCodeUsage.sequelize!.fn('SUM', PromoCodeUsage.sequelize!.col('discount_amount')), 'total_discount_given'],
                    [PromoCodeUsage.sequelize!.fn('SUM', PromoCodeUsage.sequelize!.col('original_amount')), 'total_original_amount'],
                    [PromoCodeUsage.sequelize!.fn('SUM', PromoCodeUsage.sequelize!.col('final_amount')), 'total_final_amount']
                ],
                raw: true
            });

            return {
                promoCode,
                stats: usageStats[0] || {
                    total_uses: 0,
                    total_discount_given: 0,
                    total_original_amount: 0,
                    total_final_amount: 0
                }
            };

        } catch (error: any) {
            console.error('Get promo code stats error:', error);
            throw error;
        }
    };
}
