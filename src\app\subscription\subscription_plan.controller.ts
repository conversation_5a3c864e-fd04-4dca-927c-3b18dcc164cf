import { Request, Response } from "express";
import httpStatus from "http-status";
import SubscriptionPlanService from "./subscription_plans.service";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";
import httpMessages from "../../config/httpMessages";
import SubscriptionPlan from "../../database/models/subscription_plans.mode";
import ApiError from "../../utils/ApiError";
export default class SubscriptionPlanController {
    static userPreferenceService = SubscriptionPlanService;
    constructor() { }

    static getAll = catchAsync(async (request: Request, response: Response) => {
        try {
            const { page, limit, search } = request.query;
            const option = {
                page: page ? parseInt(page as string, 10) : undefined,
                limit: limit ? parseInt(limit as string, 10) : undefined,
                search: search ? (search as string) : "",
            };
            const list = await this.userPreferenceService.getSubscriptionPlans(option);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.SUBSCRIPTION_PLAN.SUCCESS,
                data: list,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static create = catchAsync(async (request: Request, response: Response) => {
        try {
            const body = { ...request.body };
            const craetedData: SubscriptionPlan = await this.userPreferenceService.createSubscriptionPlan(body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.SUBSCRIPTION_PLAN.ADD_SUCCESS,
                data: craetedData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static showById = catchAsync(async (request: Request, response: Response) => {
        try {
            const Id: number = parseInt(request.params.id, 10);
            const details = await this.userPreferenceService.getSubscriptionPlanById(Id);
            if (!details) {
                throw new ApiError(httpStatus.NOT_FOUND, httpMessages.SUBSCRIPTION_PLAN.NOT_FOUND);
            }
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.SUBSCRIPTION_PLAN.DETAILS.SUCCESS,
                data: details,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });

    static update = catchAsync(async (request: Request, response: Response) => {
        try {
            const Id: number = parseInt(request.params.id, 10);
            const body = { ...request.body };

            const updatedData = await this.userPreferenceService.updateSubscriptionPlanById(Id, body);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.SUBSCRIPTION_PLAN.UPDATE_SUCCESS,
                data: updatedData,
            });
        } catch (error) {
            return errorResponse(response, error);
        }
    });

    static delete = catchAsync(async (request: Request, response: Response) => {
        try {
            const id: number = parseInt(request.params.id, 10);
            await this.userPreferenceService.deleteSubscriptionPlanById(id);
            return sentResponse(response, {
                statusCode: httpStatus.OK,
                message: httpMessages.SUBSCRIPTION_PLAN.DELETE,
            });
        } catch (error: any) {
            return errorResponse(response, error);
        }
    });
}
