import httpStatus from "http-status";
import { Op } from "sequelize";
import ApiError from "../../utils/ApiError";
import Message from "../../database/models/message.model";
import User from "../../database/models/user.model";
import { encryptData } from "../../middlewares/EncryptionResponse";
import moment from "moment";
import Chat from "../../database/models/chat.model";

class MessageService {
  /**
   * Send a new message
   * @param {number} chatId - Chat ID
   * @param {number} senderId - Sender user ID
   * @param {number} receiverId - Receiver user ID
   * @param {string} content - Message content
   * @returns {Promise<Message>}
   */
  static sendMessage = async (
    chatId: number,
    senderId: number,
    receiverId: number,
    content: string
  ) => {
    // Encrypt message content
    const iv = "v@XI!kaW3BK,@8ki";
    const secretKey = "Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn";
    const encryptedContent = encryptData(content, secretKey, iv);

    // Create message
    const message = await Message.create({
      chat_id: chatId,
      sender_id: senderId,
      receiver_id: receiverId,
      content: encryptedContent,
      is_encrypted: true,
      is_delivered: false,
      is_read: false,
      delivered_at: null,
      read_at: null,
    });

    // Update last message time in chat
    await Chat.update(
      { last_message_at: new Date() },
      { where: { id: chatId } }
    );

    return message;
  };

  /**
   * Get messages for a chat with pagination
   * @param {number} chatId - Chat ID
   * @param {number} page - Page number
   * @param {number} limit - Number of messages per page
   * @returns {Promise<{ messages: Message[], total: number, totalPages: number, currentPage: number }>}
   */
  static getChatMessages = async (
    chatId: number,
    page: number = 1,
    limit: number = 20
  ) => {
    const offset = (page - 1) * limit;

    const { count, rows } = await Message.findAndCountAll({
      where: { chat_id: chatId },
      include: [
        {
          model: User,
          as: "sender",
          attributes: ["id", "first_name", "last_name"],
        },
        {
          model: User,
          as: "receiver",
          attributes: ["id", "first_name", "last_name"],
        },
      ],
      order: [["createdAt", "DESC"]],
      limit,
      offset,
    });

    return {
      messages: rows,
      total: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    };
  };

  /**
   * Mark message as delivered
   * @param {number} messageId - Message ID
   * @returns {Promise<Message>}
   */
  static markAsDelivered = async (messageId: number) => {
    const message = await Message.findByPk(messageId);

    if (!message) {
      throw new ApiError(httpStatus.NOT_FOUND, "Message not found");
    }

    if (!message.is_delivered) {
      await message.update({
        is_delivered: true,
        delivered_at: new Date(),
      });
    }

    return message;
  };

  /**
   * Mark message as read
   * @param {number} messageId - Message ID
   * @returns {Promise<Message>}
   */
  static markAsRead = async (messageId: number) => {
    const message = await Message.findByPk(messageId);

    if (!message) {
      throw new ApiError(httpStatus.NOT_FOUND, "Message not found");
    }

    if (!message.is_read) {
      await message.update({
        is_read: true,
        read_at: new Date(),
      });
    }

    return message;
  };

  /**
   * Mark all unread messages in a chat as read
   * @param {number} chatId - Chat ID
   * @param {number} userId - User ID (receiver)
   * @returns {Promise<number>} - Number of messages marked as read
   */
  static markAllAsRead = async (chatId: number, userId: number) => {
    const result = await Message.update(
      {
        is_read: true,
        read_at: new Date(),
      },
      {
        where: {
          chat_id: chatId,
          receiver_id: userId,
          is_read: false,
        },
      }
    );

    return result[0]; // Number of rows affected
  };

  /**
   * Delete messages older than specified days
   * @param {number} days - Number of days
   * @returns {Promise<number>} - Number of messages deleted
   */
  static deleteOldMessages = async (days: number) => {
    const cutoffDate = moment().subtract(days, "days").toDate();

    // Find chats with auto-delete enabled
    const chats = await Chat.findAll({
      where: {
        auto_delete_days: {
          [Op.not]: null,
        },
      },
    });

    let totalDeleted = 0;

    // For each chat, delete messages older than its auto-delete setting
    for (const chat of chats) {
      if (chat.auto_delete_days) {
        const chatCutoffDate = moment()
          .subtract(chat.auto_delete_days, "days")
          .toDate();

        const result = await Message.destroy({
          where: {
            chat_id: chat.id,
            createdAt: {
              [Op.lt]: chatCutoffDate,
            },
          },
        });

        totalDeleted += result;
      }
    }

    return totalDeleted;
  };
}

export default MessageService;
