/**
 * @swagger
 * /user:
 *   get:
 *     tags:
 *       - User
 *     summary: Get all users
 *     description: Retrieve a list of all users with their details and pagination info.
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           example: 1
 *         description: Page number for pagination.
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Number of records per page.
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           example: "john"
 *         description: Search by first name, last name, or email.
 *     responses:
 *       200:
 *         description: Successfully retrieved the user list.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Users retrieved successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                       example: 25
 *                     totalPages:
 *                       type: integer
 *                       example: 3
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     users:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           firstName:
 *                             type: string
 *                             example: John
 *                           middleName:
 *                             type: string
 *                             example: A.
 *                           lastName:
 *                             type: string
 *                             example: Doe
 *                           gender:
 *                             type: string
 *                             example: male
 *                           profileCreatedFor:
 *                             type: string
 *                             example: myself
 *                           dateOfBirth:
 *                             type: string
 *                             format: date
 *                             example: 1995-05-15
 *                           email:
 *                             type: string
 *                             example: <EMAIL>
 *                           phone:
 *                             type: string
 *                             example: 9876543210
 *                           is_email_verified:
 *                             type: boolean
 *                             example: true
 *                           is_phone_verified:
 *                             type: boolean
 *                             example: false
 *                           status:
 *                             type: string
 *                             enum: [active, inactive, blocked]
 *                             example: active
 *                           terms_condition:
 *                             type: boolean
 *                             example: true
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                             example: 2025-05-01T10:00:00.000Z
 *                           updatedAt:
 *                             type: string
 *                             format: date-time
 *                             example: 2025-05-05T14:23:00.000Z
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Unable to retrieve users due to a server error.
 */


/**
 * @swagger
 * /user/{id}:
 *   get:
 *     tags:
 *       - User
 *     summary: Get a user by ID
 *     description: Retrieve the details of a single user using their unique ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *         description: The unique identifier of the user.
 *     responses:
 *       200:
 *         description: Successfully retrieved the user.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User retrieved successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     firstName:
 *                       type: string
 *                       example: John
 *                     middleName:
 *                       type: string
 *                       example: A.
 *                     lastName:
 *                       type: string
 *                       example: Doe
 *                     gender:
 *                       type: string
 *                       example: male
 *                     profileCreatedFor:
 *                       type: string
 *                       example: myself
 *                     dateOfBirth:
 *                       type: string
 *                       format: date
 *                       example: 1995-05-15
 *                     email:
 *                       type: string
 *                       example: <EMAIL>
 *                     phone:
 *                       type: string
 *                       example: 9876543210
 *                     is_email_verified:
 *                       type: boolean
 *                       example: true
 *                     is_phone_verified:
 *                       type: boolean
 *                       example: false
 *                     status:
 *                       type: string
 *                       enum: [active, inactive, blocked]
 *                       example: active
 *                     terms_condition:
 *                       type: boolean
 *                       example: true
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-01T10:00:00.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-05T14:23:00.000Z
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: User not found.
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Unable to retrieve user due to a server error.
 */
