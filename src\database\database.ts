import { Sequelize } from "sequelize-typescript";
import User from "./models/user.model";
import Role from "./models/role.model";
import Token from "./models/token.model";
import UserAstroDetails from "./models/user_astro_details.model";
import UserBasicDetails from "./models/user_basic_details.model";
import UserEducationCareer from "./models/user_education_career.model";
import UserFamilyDetails from "./models/user_family_details.model";
import UserLifestyle from "./models/user_lifestyle.model";
import UserLocationDetails from "./models/user_location_details.model";
import UserVerification from "./models/user_verifications.model";
import Admin from "./models/admin.model";
import UserHobbies from "./models/user_hobbies.model";
import UserShortlist from "./models/user_shortlist.model";
import City from "./models/city.model";
import Country from "./models/country.model";
import UserPreference from "./models/user_preferences.model";
import UserSubscription from "./models/user_subscriptions.model";
import SubscriptionPlan from "./models/subscription_plans.mode";
import PrivacySetting from "./models/privacy_settings.model";
import UserInvitation from "./models/user_invitation.model";
import Chat from "./models/chat.model";
import Message from "./models/message.model";
import SuccessStory from "./models/success_story.model";
import PaypalPayment from "./models/paypal_payment.model";
import PaypalWebhookEvent from "./models/paypal_webhook_event.model";
import UserActionLog from "./models/user_action_log.model";
import KhaltiPayment from "./models/khalti_payment.model";
import KhaltiWebhookEvent from "./models/khalti_webhook_event.model";
import SuccessStoryMedia from "./models/success_story_media.model";
import HelpCategory from "./models/help_category.model";
import HelpQuestion from "./models/help_question.model";
  import Inquiry from "./models/inquiries.model";
import UserGallery from "./models/user_gallery.model";
import Module from "./models/modules.model";
import RolePermission from "./models/role_permissions.model";

const databaseName: string = process.env.DATABASE_NAME || "";
const databaseUser: string = process.env.DATABASE_USER || "";
const databasePassword: string = process.env.DATABASE_PASSWORD || "";
const host: string = process.env.DATABASE_HOST || "";
const port: number | undefined = parseInt(process.env.DATABASE_PORT || '', 10);
const dialect: string = process.env.DATABASE_DIALECT || "";

export const sequelize = new Sequelize(
  databaseName,
  databaseUser,
  databasePassword,
  {
    host,
    port,
    dialect: dialect as any,
    models: [
      User,
      Role,
      Admin,
      Token,
      PrivacySetting,
      UserSubscription,
      SubscriptionPlan,
      UserBasicDetails,
      UserLocationDetails,
      UserEducationCareer,
      UserLifestyle,
      UserAstroDetails,
      UserFamilyDetails,
      UserGallery,
      UserHobbies,
      UserVerification,
      UserShortlist,
      Country,
      City,
      UserPreference,
      UserInvitation,
      Chat,
      Message,
      SuccessStory,
      SuccessStoryMedia,
      PaypalPayment,
      PaypalWebhookEvent,
      UserActionLog,
      KhaltiPayment,
      KhaltiWebhookEvent,
      HelpCategory,
      HelpQuestion,
      Inquiry,
      Module,
      RolePermission
    ]
  }
);
