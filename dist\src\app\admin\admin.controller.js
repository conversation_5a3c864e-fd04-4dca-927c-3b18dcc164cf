"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const response_1 = __importStar(require("../../utils/response"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
const admin_service_1 = __importDefault(require("./admin.service"));
const token_service_1 = __importDefault(require("../../common/services/token.service"));
class AdminController {
    constructor() { }
}
_a = AdminController;
AdminController.adminService = admin_service_1.default;
AdminController.tokenService = token_service_1.default;
AdminController.login = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { username, password } = request.body;
        const admin = yield _a.adminService.loginUserWithUsernameAndPassword(username, password);
        const tokens = yield _a.tokenService.generateAuthTokens(admin);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.LOGIN.SUCCESS,
            data: Object.assign(Object.assign({}, admin), { token: tokens }),
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
AdminController.getAll = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search } = request.query;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search ? search : "",
        };
        const admins = yield _a.adminService.getAdmins(option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.SUCCESS,
            data: admins,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
AdminController.create = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const adminData = Object.assign({}, request.body);
        const admin = yield _a.adminService.createAdmin(adminData);
        // admin.permissions
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.REGISTER.SUCCESS,
            data: admin,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
AdminController.showById = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { adminId } = request.params;
        if (adminId === "me") {
            let decoded = request.decoded;
            console.log("decoded: ===", decoded);
            if (!decoded) {
                throw new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED);
            }
            // Set the id to the decoded id
            const admin = yield _a.adminService.getAdminById(decoded);
            console.log("admin: ", admin);
            if (!admin) {
                throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER.NOT_FOUND);
            }
            return (0, response_1.sentResponse)(response, {
                statusCode: http_status_1.default.OK,
                message: httpMessages_1.default.USER.DETAILS.SUCCESS,
                data: admin,
            });
        }
        const admin = yield _a.adminService.getAdminById(parseInt(adminId));
        if (!admin) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER.NOT_FOUND);
        }
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.DETAILS.SUCCESS,
            data: admin,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
AdminController.update = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const adminId = parseInt(request.params.adminId, 10);
        const adminData = Object.assign({}, request.body);
        console.log("adminData: ", adminData);
        const admin = yield _a.adminService.updateAdminById(adminId, adminData);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.UPDATE_SUCCESS,
            data: admin,
        });
    }
    catch (error) {
        console.log("error: ", error);
        return (0, response_1.default)(response, error);
    }
}));
AdminController.delete = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const adminId = parseInt(request.params.adminId, 10);
        yield _a.adminService.deleteAdminById(adminId);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: httpMessages_1.default.USER.DELETE,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = AdminController;
