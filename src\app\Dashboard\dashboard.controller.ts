import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";
import DashboardService from "./dashboard.service";

export default class DashboardController {
  static dashboardService = DashboardService;

  static getDashboardData = catchAsync(async (request: Request, response: Response) => {
    try {
        const userId = request.decoded;
      const data = await this.dashboardService.getDashboardData(userId);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Dashboard data retrieved successfully",
        data,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static getAdminDashboardData = catchAsync(async (request: Request, response: Response) => {
    try {
      const data = await this.dashboardService.getAdminDashboardData();
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: "Admin dashboard data retrieved successfully",
        data,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });
}
