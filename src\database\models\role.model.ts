import {
  AllowNull,
  AutoIncrement,
  BelongsTo,
  BelongsToMany,
  Column,
  DataType,
  HasMany,
  Model,
  NotEmpty,
  PrimaryKey,
  Table
} from "sequelize-typescript";
import Admin from "./admin.model";
import RolePermission from "./role_permissions.model";

export interface RoleI {
  id: number;
  role_name: string;
  description: string;
}

@Table({
  tableName: "roles",
  timestamps: true
})
class Role extends Model<RoleI> implements RoleI {
  @AutoIncrement
  @PrimaryKey
  @Column
  id: number;

  @AllowNull(false)
  @NotEmpty
  @Column(DataType.STRING)
  role_name: string;

  @AllowNull(true)
  @Column(DataType.TEXT)
  description: string;

  @HasMany(() => Admin, { foreignKey: "role_id", onDelete: "CASCADE", })
  admins: Admin[];

  @HasMany(() => RolePermission, { foreignKey: "role_id", onDelete: "CASCADE", })
  rolePermissions: RolePermission[];
}

export default Role;



