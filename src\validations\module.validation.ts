    import Joi from "joi";

export const moduleValidation = {
  body: Joi.object().keys({
    name: Joi.string().trim().required().messages({
      "string.empty": "Name is required.",
      "string.base": "Name must be a string.",
    }),
    route: Joi.string().trim().required().messages({
      "string.empty": "Route is required.",
      "string.base": "Route must be a string.",
    }),
    parent_id: Joi.number().optional().allow(null, ""),
    icon: Joi.string().trim().optional().allow(null, ""),
    sort_order: Joi.number().optional().allow(null, ""),
    is_active: Joi.boolean().optional().allow(null, ""),
  }),
};


