"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_validation_1 = require("../validations/auth.validation");
const middleware_1 = require("../middlewares/middleware");
const auth_controller_1 = __importDefault(require("../app/auth/auth.controller"));
const router = express_1.default.Router();
router.post("/register", (0, middleware_1.validate)(auth_validation_1.registerValidation), auth_controller_1.default.register);
router.post("/login", (0, middleware_1.validate)(auth_validation_1.loginValidation), auth_controller_1.default.login);
router.post("/logout", (0, middleware_1.validate)(auth_validation_1.logoutValidation), auth_controller_1.default.logout);
router.post("/refresh-tokens", (0, middleware_1.validate)(auth_validation_1.refreshTokensValidation), auth_controller_1.default.refreshTokens);
router.post("/forgot-password", (0, middleware_1.validate)(auth_validation_1.forgotPasswordValidation), auth_controller_1.default.forgotPassword);
router.post("/reset-password", (0, middleware_1.validate)(auth_validation_1.resetPasswordValidation), auth_controller_1.default.resetPassword);
// router.post('/send-verification-email', auth, AuthController.sendVerificationEmail);
router.post('/verify-email-otp', (0, middleware_1.validate)(auth_validation_1.verifyOtpValidation), auth_controller_1.default.verifyEmailOtp);
router.post('/resend-email-otp', (0, middleware_1.validate)(auth_validation_1.resentOtpValidation), auth_controller_1.default.resendEmailOtp);
exports.default = router;
