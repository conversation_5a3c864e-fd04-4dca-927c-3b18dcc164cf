"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const promo_codes_model_1 = __importDefault(require("../../database/models/promo_codes.model"));
const promo_code_usage_model_1 = __importDefault(require("../../database/models/promo_code_usage.model"));
const sequelize_1 = require("sequelize");
class AdminPromoCodeService {
}
_a = AdminPromoCodeService;
/**
 * Create a new promo code
 * @param {CreatePromoCodeParams} params
 * @returns {Promise<PromoCode>}
 */
AdminPromoCodeService.createPromoCode = (params) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { code, description, discount_type, discount_value, minimum_purchase_amount, maximum_discount_amount, usage_limit, start_date, expiry_date, applicable_plans, first_time_users_only = false, is_active = true, created_by } = params;
        // Check if promo code already exists
        const existingPromoCode = yield promo_codes_model_1.default.findOne({
            where: { code: code.toUpperCase() }
        });
        if (existingPromoCode) {
            throw new ApiError_1.default(http_status_1.default.CONFLICT, "Promo code already exists");
        }
        // Validate discount value based on type
        if (discount_type === "percentage" && discount_value > 100) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Percentage discount cannot exceed 100%");
        }
        // Validate dates
        if (start_date && expiry_date && start_date >= expiry_date) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Start date must be before expiry date");
        }
        let body = {
            code: code.toUpperCase(),
            description,
            discount_type,
            discount_value,
            minimum_purchase_amount,
            maximum_discount_amount,
            usage_limit,
            used_count: 0,
            start_date,
            expiry_date,
            applicable_plans: applicable_plans ? JSON.stringify(applicable_plans) : null,
            first_time_users_only,
            is_active,
            created_by
        };
        const promoCode = yield promo_codes_model_1.default.create(body);
        return promoCode;
    }
    catch (error) {
        console.error('Create promo code error:', error);
        throw error;
    }
});
/**
 * Get all promo codes with pagination and search
 * @param {Object} options
 * @returns {Promise<Object>}
 */
AdminPromoCodeService.getPromoCodes = (options) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, is_active } = options;
        const whereCondition = search
            ? {
                [sequelize_1.Op.or]: [
                    { code: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                    { description: { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` } },
                ],
            }
            : {};
        if (is_active !== undefined) {
            whereCondition.is_active = is_active;
        }
        const queryOption = {
            where: whereCondition,
            include: [
                {
                    model: promo_code_usage_model_1.default,
                    attributes: ['id'],
                    required: false
                }
            ],
            order: [["createdAt", "DESC"]],
        };
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const promoCodes = yield promo_codes_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: promoCodes.count,
                totalPages: Math.ceil(promoCodes.count / limit),
                currentPage: page,
                promoCodes: promoCodes.rows,
            };
        }
        else {
            return promoCodes.rows;
        }
    }
    catch (error) {
        console.error('Get promo codes error:', error);
        throw new ApiError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, error.message);
    }
});
/**
 * Get promo code by ID
 * @param {number} id
 * @returns {Promise<PromoCode>}
 */
AdminPromoCodeService.getPromoCodeById = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const promoCode = yield promo_codes_model_1.default.findByPk(id, {
            include: [
                {
                    model: promo_code_usage_model_1.default,
                    attributes: ['id', 'user_id', 'discount_amount', 'used_at'],
                    limit: 10,
                    order: [['used_at', 'DESC']]
                }
            ]
        });
        if (!promoCode) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Promo code not found");
        }
        return promoCode;
    }
    catch (error) {
        console.error('Get promo code by ID error:', error);
        throw error;
    }
});
/**
 * Update promo code
 * @param {number} id
 * @param {UpdatePromoCodeParams} params
 * @returns {Promise<PromoCode>}
 */
AdminPromoCodeService.updatePromoCode = (id, params) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const promoCode = yield promo_codes_model_1.default.findByPk(id);
        if (!promoCode) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Promo code not found");
        }
        // Validate discount value if being updated
        if (params.discount_value && promoCode.discount_type === "percentage" && params.discount_value > 100) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Percentage discount cannot exceed 100%");
        }
        // Validate dates if being updated
        const newStartDate = params.start_date || promoCode.start_date;
        const newExpiryDate = params.expiry_date || promoCode.expiry_date;
        if (newStartDate && newExpiryDate && newStartDate >= newExpiryDate) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Start date must be before expiry date");
        }
        const updateData = Object.assign({}, params);
        if (params.applicable_plans) {
            updateData.applicable_plans = JSON.stringify(params.applicable_plans);
        }
        yield promoCode.update(updateData);
        return promoCode;
    }
    catch (error) {
        console.error('Update promo code error:', error);
        throw error;
    }
});
/**
 * Delete promo code
 * @param {number} id
 * @returns {Promise<void>}
 */
AdminPromoCodeService.deletePromoCode = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const promoCode = yield promo_codes_model_1.default.findByPk(id);
        if (!promoCode) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Promo code not found");
        }
        // Check if promo code has been used
        const usageCount = yield promo_code_usage_model_1.default.count({
            where: { promo_code_id: id }
        });
        if (usageCount > 0) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Cannot delete promo code that has been used");
        }
        yield promoCode.destroy();
    }
    catch (error) {
        console.error('Delete promo code error:', error);
        throw error;
    }
});
/**
 * Get promo code usage statistics
 * @param {number} id
 * @returns {Promise<Object>}
 */
AdminPromoCodeService.getPromoCodeStats = (id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const promoCode = yield promo_codes_model_1.default.findByPk(id);
        if (!promoCode) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Promo code not found");
        }
        const usageStats = yield promo_code_usage_model_1.default.findAll({
            where: { promo_code_id: id },
            attributes: [
                [promo_code_usage_model_1.default.sequelize.fn('COUNT', promo_code_usage_model_1.default.sequelize.col('id')), 'total_uses'],
                [promo_code_usage_model_1.default.sequelize.fn('SUM', promo_code_usage_model_1.default.sequelize.col('discount_amount')), 'total_discount_given'],
                [promo_code_usage_model_1.default.sequelize.fn('SUM', promo_code_usage_model_1.default.sequelize.col('original_amount')), 'total_original_amount'],
                [promo_code_usage_model_1.default.sequelize.fn('SUM', promo_code_usage_model_1.default.sequelize.col('final_amount')), 'total_final_amount']
            ],
            raw: true
        });
        return {
            promoCode,
            stats: usageStats[0] || {
                total_uses: 0,
                total_discount_given: 0,
                total_original_amount: 0,
                total_final_amount: 0
            }
        };
    }
    catch (error) {
        console.error('Get promo code stats error:', error);
        throw error;
    }
});
exports.default = AdminPromoCodeService;
