"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const httpMessages_1 = __importDefault(require("../../config/httpMessages"));
const enums_1 = require("../../database/config/enums");
const axios_1 = __importDefault(require("axios"));
const token_model_1 = __importDefault(require("../../database/models/token.model"));
const user_service_1 = __importDefault(require("../../app/user/user.service"));
const token_service_1 = __importDefault(require("./token.service"));
const mailMessages_1 = require("../../config/mailMessages");
const user_model_1 = __importDefault(require("../../database/models/user.model"));
const user_verifications_model_1 = __importDefault(require("../../database/models/user_verifications.model"));
// import twilio from 'twilio';
// const client = twilio(process.env.TWILIO_ACCOUNT_SID!, process.env.TWILIO_AUTH_TOKEN!);
class EmailService {
    constructor() { }
}
_a = EmailService;
EmailService.userService = user_service_1.default;
EmailService.tokenService = token_service_1.default;
/**
* Verify email
* @param {string} verifyEmailToken
* @returns {Promise}
*/
EmailService.verifyEmail = (verifyEmailToken) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        const verifyEmailTokenDoc = yield _a.tokenService.verifyToken(verifyEmailToken, enums_1.TOKEN_TYPES.VERIFY_EMAIL);
        const user = yield _a.userService.getUserById(verifyEmailTokenDoc.user);
        if (!user) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.USER.EMAIL_NOT_FOUND);
        }
        yield token_model_1.default.destroy({
            where: { userId: user.id, type: enums_1.TOKEN_TYPES.VERIFY_EMAIL }
        });
        yield _a.userService.updateUserById(user.id, { is_email_verified: true });
    }
    catch (error) {
        const errorMessage = ((_b = error.original) === null || _b === void 0 ? void 0 : _b.message) || error.message || 'Unknown error occurred';
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage);
    }
});
/**
* send Verification Email
* @param {string} email
* @param {string} emailToken
* @returns {Promise<string>}
*/
EmailService.sendVerificationEmail = (email, emailToken) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        // const transporter = nodemailer.createTransport({
        //     service: 'gmail', // or SendGrid, etc.
        //     auth: {
        //         user: process.env.EMAIL_USER,
        //         pass: process.env.EMAIL_PASS,
        //     },
        // });
        const verifyLink = `${process.env.FRONTEND_URL}/verify-email?token=${emailToken}`;
        // const mailOptions = {
        //     from: '<EMAIL>',
        //     to: email,
        //     subject: 'Verify your email',
        //     html: `<p>Click <a href="${verifyLink}">here</a> to verify your email. This link expires in 15 minutes.</p>`,
        // };
        // await transporter.sendMail(mailOptions);
        // let html: any = await EmailTemplate({
        //     type: "SUBSCRIPTION_ACTIVE",
        //     data: {
        //         userName: customer.name,
        //         start_date: subscriptionStartDate,
        //         next_billing_date: nextBillingDate,
        //         link: '#'
        //     },
        // });
        const mailData = {
            to_addresses: [email],
            from_address: "<EMAIL>",
            from_name: "UBSIDI FINANCE",
            subject: "Invitation to Join UBSIDI FINANCE!",
            body: `<p>Click <a href="${verifyLink}">here</a> to verify your email. This link expires in 15 minutes.</p>`,
            body_type: "html",
        };
        const apiUrl = process.env.MAIL_API_URL || 'https://www.tangomycp.com/amazon-ses/public/api/send-mail';
        yield axios_1.default.post(apiUrl, mailData);
        const user = yield _a.userService.getUserByEmail(email);
        return user;
        // const verifyLink = `${process.env.FRONTEND_URL}/verify-email?token=${emailToken}`;
        // const transporter = nodemailer.createTransport({
        //   host: process.env.EMAIL_HOST,
        //   port: Number(process.env.EMAIL_PORT) || 587,
        //   secure: true, // true for port 465, false for 587
        //   auth: {
        //     user: process.env.EMAIL_USER,
        //     pass: process.env.EMAIL_PASS,
        //   },
        // });
        // // const html: any = await EmailTemplate({
        // //   type: "WELCOME",
        // //   data: {
        // //     userName: email,
        // //   },
        // // });
        // const info = await transporter.sendMail({
        //   from: `"BarBadhu Support" <${process.env.EMAIL_USER}>`,
        //   to: email,
        //   subject: "Invitation to Join Bar Badhu!",
        //   html: `<p>Click <a href="${verifyLink}">here</a> to verify your email. This link expires in 15 minutes.</p>`,
        // });
        // const user: any = await this.userService.getUserByEmail(email);
        // return user;
    }
    catch (error) {
        const errorMessage = ((_b = error.original) === null || _b === void 0 ? void 0 : _b.message) || error.message || 'Unknown error occurred';
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage);
    }
});
/**
* Generate reset password token
* @param {string} email
* @param {string} emailToken
* @returns {Promise<string>}
*/
EmailService.sendResetPasswordEmail = (email, emailToken) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        const resetPasswordLink = `${process.env.FRONTEND_URL}/reset-password?token=${emailToken}`;
        const mailData = {
            to_addresses: [email],
            from_address: "<EMAIL>",
            from_name: "Bar Badhu",
            subject: "Reset Your Password!",
            body: `<p>Click <a style="color: #007bff; text-decoration: underline; font-weight: bold;" href="${resetPasswordLink}">here</a> to reset your password. This link expires in 15 minutes.</p>`,
            body_type: "html",
        };
        const apiUrl = process.env.MAIL_API_URL || 'https://www.tangomycp.com/amazon-ses/public/api/send-mail';
        yield axios_1.default.post(apiUrl, mailData);
        const user = yield _a.userService.getUserByEmail(email);
        return user;
        // const resetPasswordLink = `${process.env.FRONTEND_URL}/#/reset-password?token=${emailToken}`;
        // const transporter = nodemailer.createTransport({
        //   host: process.env.EMAIL_HOST,
        //   port: Number(process.env.EMAIL_PORT) || 587,
        //   secure: true, // true for port 465, false for 587
        //   auth: {
        //     user: process.env.EMAIL_USER,
        //     pass: process.env.EMAIL_PASS,
        //   },
        // });
        // const info = await transporter.sendMail({
        //   from: `"BarBadhu Support" <${process.env.EMAIL_USER}>`,
        //   to: email,
        //   subject: "Reset Your Password!",
        //   html: `<p>Click <a style="color: #007bff; text-decoration: underline; font-weight: bold;" href="${resetPasswordLink}">here</a> to reset your password. This link expires in 15 minutes.</p>`,
        // });
        // const user: any = await this.userService.getUserByEmail(email);
        // return user;
    }
    catch (error) {
        const errorMessage = ((_b = error.original) === null || _b === void 0 ? void 0 : _b.message) || error.message || 'Unknown error occurred';
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage);
    }
});
/**
* send Verification Email
* @param {string} email
* @param {Number} userId
* @returns {Promise<string>}
*/
EmailService.sendOtpForVerificationEmail = (email, userId) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        const expiryMinutes = 15;
        const otpExpiry = new Date(Date.now() + 15 * 60 * 1000);
        let html = yield (0, mailMessages_1.EmailTemplate)({
            type: "SENT_OTP",
            data: {
                userName: email,
                otp: otp,
                expiryMinutes: expiryMinutes
            },
        });
        const mailData = {
            to_addresses: [email],
            from_address: "<EMAIL>",
            from_name: "Bar Badhu",
            subject: "Bar Badhu - OTP Verification!",
            body: html,
            body_type: "html",
        };
        const apiUrl = process.env.MAIL_API_URL || 'https://www.tangomycp.com/amazon-ses/public/api/send-mail';
        yield axios_1.default.post(apiUrl, mailData);
        // 2. Save OTP to user table (you should update your model accordingly)
        const verification = yield user_verifications_model_1.default.findOne({
            where: { user_id: userId },
        });
        if (!verification) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "User not found");
        }
        const userVerificationBody = {
            user_id: userId,
            email: email,
            email_otp: otp,
            email_otp_expiry: otpExpiry,
            is_email_verified: false,
        };
        const user = yield verification.update(Object.assign({}, userVerificationBody));
        return user;
        //  const otp = Math.floor(100000 + Math.random() * 900000).toString();
        // const expiryMinutes = 15;
        // const otpExpiry = new Date(Date.now() + 15 * 60 * 1000);
        // let html: any = await EmailTemplate({
        //   type: "SENT_OTP",
        //   data: {
        //     userName: email,
        //     otp: otp,
        //     expiryMinutes: expiryMinutes
        //   },
        // });
        // const transporter = nodemailer.createTransport({
        //   host: process.env.EMAIL_HOST,
        //   port: Number(process.env.EMAIL_PORT) || 587,
        //   secure: true, // true for port 465, false for 587
        //   auth: {
        //     user: process.env.EMAIL_USER,
        //     pass: process.env.EMAIL_PASS,
        //   },
        // });
        // const info = await transporter.sendMail({
        //   from: `"BarBadhu Support" <${process.env.EMAIL_USER}>`,
        //   to: email,
        //   subject: "OTP Verification",
        //   html
        // });
        // // 2. Save OTP to user table (you should update your model accordingly)
        // const verification = await UserVerification.findOne({
        //   where: { user_id: userId },
        // });
        // if (!verification) {
        //   throw new ApiError(httpStatus.NOT_FOUND, "User not found");
        // }
        // const userVerificationBody: any = {
        //   user_id: userId,
        //   email: email,
        //   email_otp: otp,
        //   email_otp_expiry: otpExpiry,
        //   is_email_verified: false,
        // };
        // const user = await verification.update({ ...userVerificationBody });
        // return user;
    }
    catch (error) {
        const errorMessage = ((_b = error.original) === null || _b === void 0 ? void 0 : _b.message) || error.message || 'Unknown error occurred';
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage);
    }
});
/**
* send Verification Email
* @param {string} email
* @param {string} otp
* @returns {Promise<string>}
*/
EmailService.verifyEmailOtp = (email, otp) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        const user = yield user_verifications_model_1.default.findOne({
            where: { email },
        });
        if (!user || !user.email_otp || !user.email_otp_expiry) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, httpMessages_1.default.OTP.INVALID_OTP_NOT_FOUND);
        }
        ;
        if (user.email_otp !== otp) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, httpMessages_1.default.OTP.INVALID_OTP);
        }
        ;
        if (new Date() > new Date(user.email_otp_expiry)) {
            throw new ApiError_1.default(http_status_1.default.GONE, httpMessages_1.default.OTP.INVALID_OTP_EXPIRED);
        }
        ;
        user.is_email_verified = true;
        user.email_otp = '';
        user.email_otp_expiry = null;
        yield user.save();
        return user_model_1.default.findOne({
            where: { id: user.user_id },
            attributes: { exclude: ["password"] },
        }).then((data) => data === null || data === void 0 ? void 0 : data.toJSON());
        ;
    }
    catch (error) {
        const errorMessage = ((_b = error.original) === null || _b === void 0 ? void 0 : _b.message) || error.message || 'Unknown error occurred';
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage);
    }
});
//     /**
//   * Generate reset password token
//   * @param {string} phone
//   * @returns {Promise<string>}
//   */
//     static sendOtp = async (phone: string) => {
//         const min = Math.pow(10, length - 1); // e.g., 100000
//         const max = Math.pow(10, length) - 1; // e.g., 999999
//         const otp = Math.floor(min + Math.random() * (max - min + 1)).toString();
//         const otpExpiry = Date.now() + 5 * 60 * 1000;
//         return await client.messages.create({
//             body: `Your OTP is: ${otp}`,
//             to: phone,
//             from: process.env.TWILIO_PHONE_NUMBER,
//         });
//     };
//     /**
//    * Generate reset password token
//    * @param {string} phone
//    * @param {string} otp
//    * @returns {Promise<string>}
//    */
//     static verifyOTP = async (phone: string, otp: string) => {
//         return await client.verify.v2
//             .services(process.env.TWILIO_VERIFY_SID!)
//             .verificationChecks.create({ to: `+91${phone}`, code: otp });
//     };
/**
* Send invitation email
* @param {string} email
* @param {Object} data
* @returns {Promise<any>}
*/
EmailService.sendInvitationEmail = (email, data) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        let html = yield (0, mailMessages_1.EmailTemplate)({
            type: "USER_INVITATION",
            data: {
                senderName: data.senderName,
                receiverName: data.receiverName,
                message: data.message,
                inviteLink: data.inviteLink
            },
        });
        const mailData = {
            to_addresses: [email],
            from_address: "<EMAIL>",
            from_name: "Bar Badhu",
            subject: "Bar Badhu - New Invitation",
            body: html,
            body_type: "html",
        };
        const apiUrl = process.env.MAIL_API_URL || 'https://www.tangomycp.com/amazon-ses/public/api/send-mail';
        yield axios_1.default.post(apiUrl, mailData);
        return true;
        // let html: any = await EmailTemplate({
        //   type: "USER_INVITATION",
        //   data: {
        //     senderName: data.senderName,
        //     receiverName: data.receiverName,
        //     message: data.message,
        //     inviteLink: data.inviteLink
        //   },
        // });
        // const transporter = nodemailer.createTransport({
        //   host: process.env.EMAIL_HOST,
        //   port: Number(process.env.EMAIL_PORT) || 587,
        //   secure: true, // true for port 465, false for 587
        //   auth: {
        //     user: process.env.EMAIL_USER,
        //     pass: process.env.EMAIL_PASS,
        //   },
        // });
        // const info = await transporter.sendMail({
        //   from: `"BarBadhu Support" <${process.env.EMAIL_USER}>`,
        //   to: email,
        //   subject: "Bar Badhu - New Invitation",
        //   html
        // });
        // return true;
    }
    catch (error) {
        const errorMessage = ((_b = error.original) === null || _b === void 0 ? void 0 : _b.message) || error.message || 'Unknown error occurred';
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage);
    }
});
/**
* Send invitation accepted email
* @param {string} email
* @param {Object} data
* @returns {Promise<any>}
*/
EmailService.sendInvitationAcceptedEmail = (email, data) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        let html = yield (0, mailMessages_1.EmailTemplate)({
            type: "INVITATION_ACCEPTED",
            data: {
                senderName: data.senderName,
                receiverName: data.receiverName,
                message: data.message
            },
        });
        const mailData = {
            to_addresses: [email],
            from_address: "<EMAIL>",
            from_name: "Bar Badhu",
            subject: "Bar Badhu - Invitation Accepted",
            body: html,
            body_type: "html",
        };
        const apiUrl = process.env.MAIL_API_URL || 'https://www.tangomycp.com/amazon-ses/public/api/send-mail';
        yield axios_1.default.post(apiUrl, mailData);
        return true;
        // let html: any = await EmailTemplate({
        //   type: "INVITATION_ACCEPTED",
        //   data: {
        //     senderName: data.senderName,
        //     receiverName: data.receiverName,
        //     message: data.message
        //   },
        // });
        // const transporter = nodemailer.createTransport({
        //   host: process.env.EMAIL_HOST,
        //   port: Number(process.env.EMAIL_PORT) || 587,
        //   secure: true, // true for port 465, false for 587
        //   auth: {
        //     user: process.env.EMAIL_USER,
        //     pass: process.env.EMAIL_PASS,
        //   },
        // });
        // const info = await transporter.sendMail({
        //   from: `"BarBadhu Support" <${process.env.EMAIL_USER}>`,
        //   to: email,
        //   subject: "Bar Badhu - Invitation Accepted",
        //   html
        // });
        // return true;
    }
    catch (error) {
        const errorMessage = ((_b = error.original) === null || _b === void 0 ? void 0 : _b.message) || error.message || 'Unknown error occurred';
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage);
    }
});
/**
* Send invitation declined email
* @param {string} email
* @param {Object} data
* @returns {Promise<any>}
*/
EmailService.sendInvitationDeclinedEmail = (email, data) => __awaiter(void 0, void 0, void 0, function* () {
    // Similar implementation as above
});
/**
* Send success story submission confirmation email
* @param {string} email
* @param {Object} data
* @returns {Promise<any>}
*/
EmailService.sendSuccessStorySubmissionEmail = (email, data) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        const mailData = {
            to_addresses: [email],
            from_address: "<EMAIL>",
            from_name: "Bar Badhu",
            subject: "Bar Badhu - Success Story Submission",
            body: `
                <!DOCTYPE html>
                <html lang="en">
                <head>
                  <meta charset="UTF-8">
                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <title>Success Story Submission</title>
                  <style>
                    body {
                      font-family: Arial, sans-serif;
                      background-color: #f4f4f9;
                      padding: 20px;
                    }
                    .email-container {
                      background-color: #ffffff;
                      border-radius: 8px;
                      padding: 30px;
                      max-width: 600px;
                      margin: 0 auto;
                      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    }
                    .header {
                      font-size: 24px;
                      font-weight: bold;
                      color: #333333;
                      text-align: center;
                      margin-bottom: 20px;
                    }
                    .message {
                      color: #555555;
                      line-height: 1.6;
                    }
                    .footer {
                      margin-top: 30px;
                      font-size: 14px;
                      color: #888888;
                      text-align: center;
                    }
                  </style>
                </head>
                <body>
                  <div class="email-container">
                    <div class="header">
                      Success Story Submission
                    </div>
                    <div class="message">
                      <p>Dear <strong>${data.userName}</strong>,</p>
                      <p>Thank you for sharing your success story with us! Your submission has been received and is currently under review by our team.</p>
                      <p>We will notify you once your story has been reviewed. If approved, your story will be featured on our success stories page to inspire others.</p>
                      <p>Story ID: <strong>${data.storyId}</strong></p>
                    </div>
                    <div class="footer">
                      <p>Best regards,<br>BarBadhu Team</p>
                    </div>
                  </div>
                </body>
                </html>
                `,
            body_type: "html",
        };
        const apiUrl = process.env.MAIL_API_URL || 'https://www.tangomycp.com/amazon-ses/public/api/send-mail';
        yield axios_1.default.post(apiUrl, mailData);
        return true;
        //   let html: any = `<!DOCTYPE html>
        //           <html lang="en">
        //           <head>
        //             <meta charset="UTF-8">
        //             <meta name="viewport" content="width=device-width, initial-scale=1.0">
        //             <title>Success Story Submission</title>
        //             <style>
        //               body {
        //                 font-family: Arial, sans-serif;
        //                 background-color: #f4f4f9;
        //                 padding: 20px;
        //               }
        //               .email-container {
        //                 background-color: #ffffff;
        //                 border-radius: 8px;
        //                 padding: 30px;
        //                 max-width: 600px;
        //                 margin: 0 auto;
        //                 box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        //               }
        //               .header {
        //                 font-size: 24px;
        //                 font-weight: bold;
        //                 color: #333333;
        //                 text-align: center;
        //                 margin-bottom: 20px;
        //               }
        //               .message {
        //                 color: #555555;
        //                 line-height: 1.6;
        //               }
        //               .footer {
        //                 margin-top: 30px;
        //                 font-size: 14px;
        //                 color: #888888;
        //                 text-align: center;
        //               }
        //             </style>
        //           </head>
        //           <body>
        //             <div class="email-container">
        //               <div class="header">
        //                 Success Story Submission
        //               </div>
        //               <div class="message">
        //                 <p>Dear <strong>${data.userName}</strong>,</p>
        //                 <p>Thank you for sharing your success story with us! Your submission has been received and is currently under review by our team.</p>
        //                 <p>We will notify you once your story has been reviewed. If approved, your story will be featured on our success stories page to inspire others.</p>
        //                 <p>Story ID: <strong>${data.storyId}</strong></p>
        //               </div>
        //               <div class="footer">
        //                 <p>Best regards,<br>BarBadhu Team</p>
        //               </div>
        //             </div>
        //           </body>
        //           </html>`;
        // const transporter = nodemailer.createTransport({
        //   host: process.env.EMAIL_HOST,
        //   port: Number(process.env.EMAIL_PORT) || 587,
        //   secure: true, // true for port 465, false for 587
        //   auth: {
        //     user: process.env.EMAIL_USER,
        //     pass: process.env.EMAIL_PASS,
        //   },
        // });
        // const info = await transporter.sendMail({
        //   from: `"BarBadhu Support" <${process.env.EMAIL_USER}>`,
        //   to: email,
        //   subject: "Bar Badhu - Success Story Submission",
        //   html
        // });
        // return true;
    }
    catch (error) {
        const errorMessage = ((_b = error.original) === null || _b === void 0 ? void 0 : _b.message) || error.message || 'Unknown error occurred';
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage);
    }
});
/**
* Send success story status update email
* @param {string} email
* @param {Object} data
* @returns {Promise<any>}
*/
EmailService.sendSuccessStoryStatusUpdateEmail = (email, data) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        let statusMessage = "";
        let statusColor = "";
        if (data.status === "approved") {
            statusMessage = "We are pleased to inform you that your success story has been approved and is now published on our website.";
            statusColor = "#28a745";
        }
        else if (data.status === "rejected") {
            statusMessage = "We regret to inform you that your success story could not be approved at this time.";
            statusColor = "#dc3545";
        }
        const mailData = {
            to_addresses: [email],
            from_address: "<EMAIL>",
            from_name: "Bar Badhu",
            subject: `Bar Badhu - Success Story ${data.status === "approved" ? "Approved" : "Update"}`,
            body: `
                <!DOCTYPE html>
                <html lang="en">
                <head>
                  <meta charset="UTF-8">
                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <title>Success Story Status Update</title>
                  <style>
                    body {
                      font-family: Arial, sans-serif;
                      background-color: #f4f4f9;
                      padding: 20px;
                    }
                    .email-container {
                      background-color: #ffffff;
                      border-radius: 8px;
                      padding: 30px;
                      max-width: 600px;
                      margin: 0 auto;
                      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    }
                    .header {
                      font-size: 24px;
                      font-weight: bold;
                      color: #333333;
                      text-align: center;
                      margin-bottom: 20px;
                    }
                    .message {
                      color: #555555;
                      line-height: 1.6;
                    }
                    .status {
                      padding: 10px;
                      border-radius: 5px;
                      font-weight: bold;
                      color: white;
                      background-color: ${statusColor};
                      display: inline-block;
                      margin: 10px 0;
                    }
                    .notes {
                      background-color: #f8f9fa;
                      padding: 15px;
                      border-radius: 5px;
                      margin: 15px 0;
                    }
                    .footer {
                      margin-top: 30px;
                      font-size: 14px;
                      color: #888888;
                      text-align: center;
                    }
                  </style>
                </head>
                <body>
                  <div class="email-container">
                    <div class="header">
                      Success Story Status Update
                    </div>
                    <div class="message">
                      <p>Dear <strong>${data.userName}</strong>,</p>
                      <p>${statusMessage}</p>
                      <p>Story ID: <strong>${data.storyId}</strong></p>
                      <p>Status: <span class="status">${data.status.toUpperCase()}</span></p>
                      ${data.adminNotes ? `
                      <div class="notes">
                        <p><strong>Admin Notes:</strong></p>
                        <p>${data.adminNotes}</p>
                      </div>
                      ` : ''}
                      ${data.status === "approved" ? `
                      <p>Your story will now be visible to other users on our success stories page. Thank you for sharing your journey with our community!</p>
                      ` : ''}
                    </div>
                    <div class="footer">
                      <p>Best regards,<br>BarBadhu Team</p>
                    </div>
                  </div>
                </body>
                </html>
                `,
            body_type: "html",
        };
        const apiUrl = process.env.MAIL_API_URL || 'https://www.tangomycp.com/amazon-ses/public/api/send-mail';
        yield axios_1.default.post(apiUrl, mailData);
        return true;
        //  const transporter = nodemailer.createTransport({
        //   host: process.env.EMAIL_HOST,
        //   port: Number(process.env.EMAIL_PORT) || 587,
        //   secure: true, // true for port 465, false for 587
        //   auth: {
        //     user: process.env.EMAIL_USER,
        //     pass: process.env.EMAIL_PASS,
        //   },
        // });
        // const info = await transporter.sendMail({
        //   from: `"BarBadhu Support" <${process.env.EMAIL_USER}>`,
        //   to: email,
        //   subject: `Bar Badhu - Success Story ${data.status === "approved" ? "Approved" : "Update"}`,
        //   html
        // });
    }
    catch (error) {
        const errorMessage = ((_b = error.original) === null || _b === void 0 ? void 0 : _b.message) || error.message || 'Unknown error occurred';
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, errorMessage);
    }
});
exports.default = EmailService;
