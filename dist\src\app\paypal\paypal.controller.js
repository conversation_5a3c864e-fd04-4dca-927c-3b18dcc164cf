"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const response_1 = __importStar(require("../../utils/response"));
const paypal_service_1 = __importDefault(require("./paypal.service"));
class PaypalController {
    constructor() { }
}
_a = PaypalController;
PaypalController.paypalService = paypal_service_1.default;
// Create PayPal order
PaypalController.createOrder = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { plan_id, amount, currency } = request.body;
        const user_id = request.decoded;
        const order = yield _a.paypalService.createOrder({
            user_id,
            plan_id,
            amount,
            currency
        });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "PayPal order created successfully",
            data: order,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Capture PayPal payment
PaypalController.capturePayment = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { order_id } = request.params;
        const user_id = request.decoded;
        const capturedPayment = yield _a.paypalService.capturePayment(order_id, user_id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Payment captured successfully",
            data: capturedPayment,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Get payment details
PaypalController.getPaymentDetails = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { payment_id } = request.params;
        const user_id = request.decoded.id;
        const paymentDetails = yield _a.paypalService.getPaymentDetails(payment_id, user_id);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Payment details retrieved successfully",
            data: paymentDetails,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Get user payment history
PaypalController.getUserPayments = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user_id = request.decoded;
        const { page, limit, search } = request.query;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search ? search : "",
            user_id: user_id.id
        };
        const payments = yield _a.paypalService.getUserPayments(option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "User payments retrieved successfully",
            data: payments,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// PayPal webhook handler
PaypalController.handleWebhook = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const webhookEvent = request.body;
        const webhookId = request.headers['paypal-transmission-id'];
        console.log('webhookId: ', webhookId);
        yield _a.paypalService.handleWebhookEvent(webhookEvent, webhookId);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Webhook processed successfully",
            data: { received: true },
        });
    }
    catch (error) {
        console.error('Webhook processing error:', error);
        return (0, response_1.default)(response, error);
    }
}));
// Get payment analytics (admin)
PaypalController.getPaymentAnalytics = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { start_date, end_date, user_id } = request.query;
        const analytics = yield _a.paypalService.getPaymentAnalytics({
            start_date: start_date,
            end_date: end_date,
            user_id: user_id ? Number(user_id) : undefined
        });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Payment analytics retrieved successfully",
            data: analytics,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Refund payment
PaypalController.refundPayment = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { payment_id } = request.params;
        const { amount, reason } = request.body;
        const refund = yield _a.paypalService.refundPayment(payment_id, {
            amount,
            reason
        });
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Payment refunded successfully",
            data: refund,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
// Success page handler
PaypalController.handleSuccess = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { token, PayerID } = request.query;
        // Redirect to frontend with success parameters
        const redirectUrl = `${process.env.FRONTEND_URL}/payment/success?token=${token}&PayerID=${PayerID}`;
        response.redirect(redirectUrl);
    }
    catch (error) {
        const redirectUrl = `${process.env.FRONTEND_URL}/payment/error`;
        response.redirect(redirectUrl);
    }
}));
// Cancel page handler
PaypalController.handleCancel = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { token } = request.query;
        // Redirect to frontend with cancel parameters
        const redirectUrl = `${process.env.FRONTEND_URL}/payment/cancel?token=${token}`;
        response.redirect(redirectUrl);
    }
    catch (error) {
        const redirectUrl = `${process.env.FRONTEND_URL}/payment/error`;
        response.redirect(redirectUrl);
    }
}));
// Error page handler
PaypalController.getTransactions = (0, catchAsync_1.default)((request, response) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, status } = request.query;
        const option = {
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search ? search : "",
            status: status ? status : undefined,
        };
        const transactions = yield _a.paypalService.getTransactions(option);
        return (0, response_1.sentResponse)(response, {
            statusCode: http_status_1.default.OK,
            message: "Transactions retrieved successfully",
            data: transactions,
        });
    }
    catch (error) {
        return (0, response_1.default)(response, error);
    }
}));
exports.default = PaypalController;
