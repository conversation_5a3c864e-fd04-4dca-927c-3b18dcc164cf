"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.auth = void 0;
const http_status_1 = __importDefault(require("http-status"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const ApiError_1 = __importDefault(require("../utils/ApiError"));
const config_1 = require("../config/config");
const response_1 = __importDefault(require("../utils/response"));
const httpMessages_1 = __importDefault(require("../config/httpMessages"));
const auth = (request, response, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const authorization = request.headers.authorization;
        const token = (authorization === null || authorization === void 0 ? void 0 : authorization.split(" ")[1]) || "";
        const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret);
        console.log('decoded: ', decoded);
        const user = decoded.sub;
        console.log("user: ", user);
        if (user) {
            request["decoded"] = user;
            return next();
        }
        else {
            return (0, response_1.default)(response, new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED));
        }
    }
    catch (e) {
        return (0, response_1.default)(response, new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED));
    }
});
exports.auth = auth;
