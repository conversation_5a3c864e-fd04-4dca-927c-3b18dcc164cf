const httpMessages = require("../config/httpMessages");

export const passwordValidation = (value: any, helpers: any) => {
  if (value?.length < 8) {
    return helpers.message(httpMessages.REGISTER.SHORT_PASSWORD);
  }
  if (!value.match(/\d/) || !value.match(/[a-zA-Z]/)) {
    return helpers.message(
      "password must contain at least 1 letter and 1 number!"
    );
  }
  return value;
};

export const phoneValidation = (value: string, helpers: any) => {
  if (!value.match(/^\([0-9]{3}\)[0-9]{3}-[0-9]{4}$/)) {
    return helpers.message(httpMessages.REGISTER.INVALID_PHONE);
  }
  return value;
};

export const emptyValidation = (value: string, helpers: any) => {
  console.log("helpers: ", helpers?.state?.path[1]);
  if (value === "null" || value === "undefined") {
    let message = `The "${
      helpers?.state?.path[1] ? helpers?.state?.path[1] : ""
    }" parameter cannot be "null" or "undefined" as a string. `;
    return helpers.message(message);
  }
  return value;
};
