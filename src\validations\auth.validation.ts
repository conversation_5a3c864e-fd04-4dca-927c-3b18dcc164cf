import Joi = require("joi");
import httpMessages from "../config/httpMessages";
import { passwordValidation } from "./custom.validation";

export const registerValidation = {
  body: Joi.object().keys({
    first_name: Joi.string().trim().required().messages({
      "string.empty": "First name is required.",
      "string.base": "First name must be a string.",
    }),
    middle_name: Joi.string().trim().optional().allow(null, "").messages({
      "string.base": "Middle name must be a string.",
    }),
    last_name: Joi.string().trim().required().messages({
      "string.empty": "Last name is required.",
      "string.base": "Last name must be a string.",
    }),
    gender: Joi.string().valid('male', 'female', 'other').required().messages({
      "any.only": "Gender must be one of 'male', 'female', or 'other'.",
      "string.empty": "Gender is required.",
    }),
    profile_created_for: Joi.string().trim()  
      .optional()
      .allow(null, "")
      .messages({
        "any.only": "Profile created for must be a valid option.",
      }),
    date_of_birth: Joi.date().required().messages({
      "date.base": "Date of birth must be a valid date.",
      "any.required": "Date of birth is required.",
    }),
    email: Joi.string().email().required().messages({
      "string.empty": "Email is required.",
      "string.email": "Please provide a valid email address.",
    }),
    phone: Joi.string().trim().required().messages({
      "string.empty": "Phone number is required.",
      "string.base": "Phone number must be a string.",
    }),
    phone_code: Joi.string().trim().required().messages({
      "string.empty": "Phone number is required.",
      "string.base": "Phone number must be a string.",
    }),
    password: Joi.string()
      .trim()
      .required()
      .custom(passwordValidation)
      .messages({
        "string.empty": "Password is required.",
        "any.custom": "Password does not meet the complexity requirements.",
      }),
    confirmPassword: Joi.any()
      .equal(Joi.ref("password"))
      .required()
      .messages({
        "any.only":
          httpMessages.REGISTER?.PASSWORD_MISMATCH || "Passwords do not match.",
        "any.required": "Confirmation password is required.",
      }),
    is_email_verified: Joi.boolean().optional().default(false).messages({
      "boolean.base": "Email verified must be a boolean.",
    }),
    is_phone_verified: Joi.boolean().optional().default(false).messages({
      "boolean.base": "Phone verified must be a boolean.",
    }),
    terms_condition: Joi.boolean().optional().default(false).messages({
      "boolean.base": "Terms Condition must be a boolean.",
    }),
    status: Joi.string().valid("new", "pending", "approved", "rejected", "blocked", "deactivated").required().messages({
      "any.only": "Status must be either 'new', 'pending', 'approved', 'rejected', 'blocked', or 'deactivated'.",
      "string.empty": "Status is required.",
    }),
  }),
};

export const loginValidation = {
  body: Joi.object().keys({
    email: Joi.string().trim().required().messages({
      "string.empty": "Email is required.",
      "string.base": "Email must be a string.",
    }),
    password: Joi.string().trim().required().messages({
      "string.empty": "Password is required.",
      "string.base": "Password must be a string.",
    }),
  }),
};

export const ssoLoginValidation = {
  body: Joi.object().keys({
    code: Joi.string().trim().required(),
  }),
};

export const logoutValidation = {
  body: Joi.object().keys({
    refreshToken: Joi.string().trim().required(),
  }),
};

export const refreshTokensValidation = {
  body: Joi.object().keys({
    refreshToken: Joi.string().trim().required(),
  }),
};

export const forgotPasswordValidation = {
  body: Joi.object().keys({
    email: Joi.string().email().trim().required(),
  }),
};

export const resetPasswordValidation = {
  query: Joi.object().keys({
    token: Joi.string().trim().required(),
  }),
  body: Joi.object().keys({
    password: Joi.string().trim().required().custom(passwordValidation),
    confirmPassword: Joi.any()
      .equal(Joi.ref("password"))
      .required()
      .messages({ "any.only": httpMessages.REGISTER.PASSWORD_MISMATCH }),
  }),
};

export const verifyEmailValidation = {
  query: Joi.object().keys({
    token: Joi.string().trim().required(),
  }),
};

export const verifyOtpValidation = {
  body: Joi.object().keys({
    email: Joi.string().email().trim().required().messages({
      "string.empty": "Email is required.",
      "string.email": "Email must be a valid email address.",
    }),
    otp: Joi.string().length(6).pattern(/^[0-9]{6}$/).required().messages({
      "string.empty": "OTP is required.",
      "string.length": "OTP must be exactly 6 digits.",
      "string.pattern.base": "OTP must be a 6-digit number.",
    }),
  }),
};

export const resentOtpValidation = {
  body: Joi.object().keys({
    email: Joi.string().email().trim().required().messages({
      "string.empty": "Email is required.",
      "string.email": "Email must be a valid email address.",
    }),
  }),
};
