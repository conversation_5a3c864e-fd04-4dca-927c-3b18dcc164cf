import { Request, Response } from "express";
import httpStatus from "http-status";
import jwt from "jsonwebtoken";
import catchAsync from "../../utils/catchAsync";
import errorResponse, { sentResponse } from "../../utils/response";
import ApiError from "../../utils/ApiError";
import httpMessages from "../../config/httpMessages";

import UserService from "./user.service";
import TokenService from "../../common/services/token.service";
import { config } from "../../config/config";
import User from "../../database/models/user.model";
import moment from "moment";

export default class UserController {
  static userService = UserService;
  static tokenService = TokenService;
  constructor() { }

  static login = catchAsync(async (request: Request, response: Response) => {
    try {
      const { email, password } = request.body;
      const user: User | any =
        await this.userService.loginUserWithEmailAndPassword(email, password);
      if (!user?.hotel_users.length && user.role_id !== 1) {
        throw new ApiError(
          httpStatus.UNAUTHORIZED,
          httpMessages.USER.AUTH.NOT_ASSIGND_HOTEL
        );
      }

      const tokens = await this.tokenService.generateAuthTokens(user);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.LOGIN.SUCCESS,
        data: { ...user, token: tokens },
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static getAll = catchAsync(async (request: Request, response: Response) => {
    try {
      const { page, limit, search, status } = request.query;
      const option = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        search: search ? (search as string) : "",
        status: status ? (status as string) : undefined,
      };
      const users = await this.userService.getUsers(option);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.SUCCESS,
        data: users,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static create = catchAsync(async (request: Request, response: Response) => {
    try {
      const userData = { ...request.body };
      const user: User = await this.userService.createUser(userData);
      const data = user;
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.REGISTER.SUCCESS,
        data: user,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static showById = catchAsync(async (request: Request, response: Response) => {
    try {
      const { userId } = request.params;
      if (userId === "me") {
        let decoded = request.decoded;
        if (!decoded) {
          throw new ApiError(
            httpStatus.UNAUTHORIZED,
            httpMessages.USER.AUTH.UNAUTHORIZED
          );
        }
        // Set the id to the decoded id
        const user: User | null = await this.userService.getUserById(
          decoded
        );
        if (!user) {
          throw new ApiError(httpStatus.UNAUTHORIZED,
            httpMessages.USER.AUTH.UNAUTHORIZED);
        }
        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: httpMessages.USER.DETAILS.SUCCESS,
          data: user,
        });
      }

      const user: User | null = await this.userService.getUserById(
        parseInt(userId)
      );
      if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, httpMessages.USER.NOT_FOUND);
      }
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.DETAILS.SUCCESS,
        data: user,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static update = catchAsync(async (request: Request, response: Response) => {
    try {
      const userId: number = parseInt(request.params.userId, 10);
      const userData = { ...request.body };

      const user = await this.userService.updateUserById(userId, userData);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.UPDATE_SUCCESS,
        data: user,
      });
    } catch (error) {
      return errorResponse(response, error);
    }
  });

  static delete = catchAsync(async (request: Request, response: Response) => {
    try {
      const userId: number = parseInt(request.params.userId, 10);
      await this.userService.deleteUserById(userId);
      return sentResponse(response, {
        statusCode: httpStatus.OK,
        message: httpMessages.USER.DELETE,
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static getProfile = catchAsync(async (request: any, response: Response) => {
    try {
      const userId = request.user.id;
      const user: any = await this.userService.getUserById(userId);

      const data = { user };
      return response.status(httpStatus.CREATED).send({
        success: true,
        message: httpMessages.USER.PROFILE.SUCCESS,
        data
      });
    } catch (error: any) {
      return errorResponse(response, error);
    }
  });

  static updateProfile = catchAsync(
    async (request: any, response: Response) => {
      try {
        const userProfile = request.body;
        const userId = request.user.id;

        if (userProfile.dateOfBirth) {
          const formattedDate = new Date(userProfile.dateOfBirth);
          const ageInDays = moment().diff(formattedDate, "days");

          const minAge = 25 * 365;
          const maxAge = 65 * 365;

          if (ageInDays < minAge || ageInDays > maxAge) {
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              httpMessages.USER.PROFILE.AGE_RANGE_FAILURE
            );
          }

          userProfile.dateOfBirth = new Date(userProfile.dateOfBirth);
        }

        const profile = await this.userService.updateUserByProfile(
          userId,
          userProfile
        );
        const data = { profile };
        return response.status(httpStatus.CREATED).send({
          success: true,
          message: httpMessages.USER.PROFILE.UPDATE_SUCCESS,
          data
        });
      } catch (error: any) {
        return errorResponse(response, error);
      }
    }
  );

  static updatePassword = catchAsync(
    async (request: any, response: Response) => {
      try {
        const userId = request.decoded;
        const { currentPassword, newPassword } = request.body;

        const profile = await this.userService.updatePassword(
          userId,
          currentPassword,
          newPassword
        );
        const data = { profile };
        return response.status(httpStatus.CREATED).send({
          success: true,
          message: httpMessages.USER.PROFILE.PASSWORD_UPDATE_SUCCESS,
          data
        });
      } catch (error: any) {
        console.log('error: ', error);
        return errorResponse(response, error);
      }
    }
  );


  static changeEmail = catchAsync(
    async (request: any, response: Response) => {
      try {
        const userId = request.decoded;
        const { email } = request.body;

        const profile = await this.userService.changeEmail(
          userId,
          email
        );
        const data = { profile };
        return response.status(httpStatus.CREATED).send({
          success: true,
          message: httpMessages.USER.PROFILE.UPDATE_SUCCESS,
          data
        });
      } catch (error: any) {
        return errorResponse(response, error);
      }
    }
  );

  static completeProfile = catchAsync(
    async (request: any, response: Response) => {
      try {
        const userId = request.decoded;
        const userProfileData = { ...request.body, user_id: userId };
        const profile = await this.userService.completeProfile(
          userProfileData
        );
        const data = { profile };
        return sentResponse(response, {
          statusCode: httpStatus.OK,
          message: httpMessages.USER.PROFILE.UPDATE_SUCCESS,
          data: null,
        });
      } catch (error: any) {
        return errorResponse(response, error);
      }
    }
  );

}
