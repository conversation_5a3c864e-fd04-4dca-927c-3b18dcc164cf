/**
 * @swagger
 * /privacy_setting:
 *   get:
 *     tags:
 *       - PrivacySettings
 *     summary: Get all privacy settings
 *     description: Retrieve a list of all privacy settings with pagination.
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           example: 1
 *         description: Page number for pagination.
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Number of records per page.
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: integer
 *         description: Filter by user ID.
 *     responses:
 *       200:
 *         description: Successfully retrieved privacy settings.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Privacy settings retrieved successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                       example: 1
 *                     totalPages:
 *                       type: integer
 *                       example: 1
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     settings:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           user_id:
 *                             type: integer
 *                             example: 101
 *                           alert_matches_frequency:
 *                             type: string
 *                             enum: [Daily, Weekly, Unsubscribe]
 *                             example: Daily
 *                           alert_membership_frequency:
 *                             type: string
 *                             enum: [Daily, Weekly, Unsubscribe]
 *                             example: Weekly
 *                           alert_messages_frequency:
 *                             type: string
 *                             enum: [Daily, Weekly, Unsubscribe]
 *                             example: Unsubscribe
 *                           alert_invitations_frequency:
 *                             type: string
 *                             enum: [Daily, Weekly, Unsubscribe]
 *                             example: Daily
 *                           alert_updates_frequency:
 *                             type: string
 *                             enum: [Daily, Weekly, Unsubscribe]
 *                             example: Weekly
 *                           alert_profile_completion_frequency:
 *                             type: string
 *                             enum: [Daily, Weekly, Unsubscribe]
 *                             example: Daily
 *                           profile_visibility:
 *                             type: integer
 *                             example: 1
 *                           delete_reason:
 *                             type: string
 *                             enum: [Match Found, Match Elsewhere, Taking a Break, Unhappy Experience, Other]
 *                             example: Match Found
 *                           delete_feedback:
 *                             type: string
 *                             example: "I found a match."
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                             example: 2025-05-01T12:00:00.000Z
 *                           updatedAt:
 *                             type: string
 *                             format: date-time
 *                             example: 2025-05-07T14:30:00.000Z
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Unable to retrieve privacy settings due to a server error.
 */

/**
 * @swagger
 * /privacy_setting:
 *   post:
 *     tags:
 *       - PrivacySettings
 *     summary: Create a new privacy setting
 *     description: Create and store a new privacy setting for a user.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *             properties:
 *               user_id:
 *                 type: integer
 *                 example: 101
 *               alert_matches_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Daily
 *               alert_membership_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Weekly
 *               alert_messages_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Unsubscribe
 *               alert_invitations_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Daily
 *               alert_updates_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Weekly
 *               alert_profile_completion_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Daily
 *               profile_visibility:
 *                 type: integer
 *                 example: 1
 *               delete_reason:
 *                 type: string
 *                 enum: [Match Found, Match Elsewhere, Taking a Break, Unhappy Experience, Other]
 *                 example: Match Found
 *               delete_feedback:
 *                 type: string
 *                 example: "I found a match."
 *     responses:
 *       201:
 *         description: Privacy setting created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Privacy setting created successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     user_id:
 *                       type: integer
 *                       example: 101
 *                     alert_matches_frequency:
 *                       type: string
 *                       example: Daily
 *                     alert_membership_frequency:
 *                       type: string
 *                       example: Weekly
 *                     alert_messages_frequency:
 *                       type: string
 *                       example: Unsubscribe
 *                     alert_invitations_frequency:
 *                       type: string
 *                       example: Daily
 *                     alert_updates_frequency:
 *                       type: string
 *                       example: Weekly
 *                     alert_profile_completion_frequency:
 *                       type: string
 *                       example: Daily
 *                     profile_visibility:
 *                       type: integer
 *                       example: 1
 *                     delete_reason:
 *                       type: string
 *                       example: Match Found
 *                     delete_feedback:
 *                       type: string
 *                       example: I found a match.
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-09T12:00:00.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-09T12:00:00.000Z
 *       400:
 *         description: Bad request due to missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid input data.
 *       500:
 *         description: Server error while creating the privacy setting.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Unable to create privacy setting due to a server error.
 */

/**
 * @swagger
 * /privacy_setting/{id}:
 *   get:
 *     tags:
 *       - PrivacySettings
 *     summary: Get a privacy setting by ID
 *     description: Retrieve a specific privacy setting by its ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *         description: The ID of the privacy setting to retrieve.
 *     responses:
 *       200:
 *         description: Successfully retrieved the privacy setting.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Privacy setting retrieved successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     user_id:
 *                       type: integer
 *                       example: 101
 *                     alert_matches_frequency:
 *                       type: string
 *                       example: Daily
 *                     alert_membership_frequency:
 *                       type: string
 *                       example: Weekly
 *                     alert_messages_frequency:
 *                       type: string
 *                       example: Unsubscribe
 *                     alert_invitations_frequency:
 *                       type: string
 *                       example: Daily
 *                     alert_updates_frequency:
 *                       type: string
 *                       example: Weekly
 *                     alert_profile_completion_frequency:
 *                       type: string
 *                       example: Daily
 *                     profile_visibility:
 *                       type: integer
 *                       example: 1
 *                     delete_reason:
 *                       type: string
 *                       example: Match Found
 *                     delete_feedback:
 *                       type: string
 *                       example: I found a match.
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-09T12:00:00.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-09T12:00:00.000Z
 *       404:
 *         description: Privacy setting not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Privacy setting not found.
 *       500:
 *         description: Server error while retrieving the privacy setting.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Unable to retrieve privacy setting due to a server error.
 */

/**
 * @swagger
 * /privacy_setting/{id}:
 *   put:
 *     tags:
 *       - PrivacySettings
 *     summary: Update a privacy setting by ID
 *     description: Update an existing privacy setting for a user by its ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *         description: The ID of the privacy setting to update.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *             properties:
 *               user_id:
 *                 type: integer
 *                 example: 101
 *               alert_matches_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Daily
 *               alert_membership_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Weekly
 *               alert_messages_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Unsubscribe
 *               alert_invitations_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Daily
 *               alert_updates_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Weekly
 *               alert_profile_completion_frequency:
 *                 type: string
 *                 enum: [Daily, Weekly, Unsubscribe]
 *                 example: Daily
 *               profile_visibility:
 *                 type: integer
 *                 example: 1
 *               delete_reason:
 *                 type: string
 *                 enum: [Match Found, Match Elsewhere, Taking a Break, Unhappy Experience, Other]
 *                 example: Match Found
 *               delete_feedback:
 *                 type: string
 *                 example: "I found a match."
 *     responses:
 *       200:
 *         description: Privacy setting updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Privacy setting updated successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     user_id:
 *                       type: integer
 *                       example: 101
 *                     alert_matches_frequency:
 *                       type: string
 *                       example: Daily
 *                     alert_membership_frequency:
 *                       type: string
 *                       example: Weekly
 *                     alert_messages_frequency:
 *                       type: string
 *                       example: Unsubscribe
 *                     alert_invitations_frequency:
 *                       type: string
 *                       example: Daily
 *                     alert_updates_frequency:
 *                       type: string
 *                       example: Weekly
 *                     alert_profile_completion_frequency:
 *                       type: string
 *                       example: Daily
 *                     profile_visibility:
 *                       type: integer
 *                       example: 1
 *                     delete_reason:
 *                       type: string
 *                       example: Match Found
 *                     delete_feedback:
 *                       type: string
 *                       example: I found a match.
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-09T12:00:00.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-05-09T12:30:00.000Z
 *       400:
 *         description: Bad request due to missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid input data.
 *       404:
 *         description: Privacy setting not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Privacy setting not found.
 *       500:
 *         description: Server error while updating the privacy setting.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Unable to update privacy setting due to a server error.
 */

/**
 * @swagger
 * /privacy_setting/{id}:
 *   delete:
 *     tags:
 *       - PrivacySettings
 *     summary: Delete a privacy setting by ID
 *     description: Remove an existing privacy setting for a user by its ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *         description: The ID of the privacy setting to delete.
 *     responses:
 *       200:
 *         description: Privacy setting deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Privacy setting deleted successfully.
 *       404:
 *         description: Privacy setting not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Privacy setting not found.
 *       500:
 *         description: Server error while deleting the privacy setting.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Unable to delete privacy setting due to a server error.
 */
