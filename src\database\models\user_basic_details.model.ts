import {
    AllowNull,
    AutoIncrement,
    Column,
    <PERSON>Type,
    <PERSON><PERSON><PERSON>,
    Model,
    <PERSON><PERSON>ey,
    Table,
    BelongsTo,
    <PERSON><PERSON>ult,
    Index,
} from "sequelize-typescript";
import User from "./user.model";
import Country from "./country.model";

interface UserBasicDetailsI {
    id: number;
    user_id: number;
    country_of_citizenship: number;
    religion?: string;
    caste?: string;
    other_caste?: string;
    sub_caste?: string;
    gotra?: string;
    other_gotra?: string;
    mother_tongue?: string;
    marital_status: string;
    number_Of_children?: number;
    number_of_boys?: number;
    number_of_girls?: number;
    created_at?: Date;
    updated_at?: Date;
}

@Table({
    tableName: "user_basic_details",
    timestamps: false,
})
class UserBasicDetails extends Model<UserBasicDetailsI> implements UserBasicDetailsI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    @ForeignKey(() => Country)
    @AllowNull(true)
    @Column(DataType.INTEGER)
    country_of_citizenship: number;

    @BelongsTo(() => Country, 'country_of_citizenship')
    country: Country;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    religion?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    caste?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    other_caste?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    sub_caste?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    gotra?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    other_gotra?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    mother_tongue?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    marital_status: string;

    @AllowNull(true)
    @Column(DataType.DECIMAL(10, 2))
    number_Of_children?: number;

    @AllowNull(true)
    @Column(DataType.DECIMAL(10, 2))
    number_of_boys?: number;

    @AllowNull(true)
    @Column(DataType.DECIMAL(10, 2))
    number_of_girls?: number;
}

export default UserBasicDetails;