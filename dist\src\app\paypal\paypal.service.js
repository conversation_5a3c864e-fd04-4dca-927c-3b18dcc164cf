"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const http_status_1 = __importDefault(require("http-status"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const paypal_payment_model_1 = __importDefault(require("../../database/models/paypal_payment.model"));
const paypal_webhook_event_model_1 = __importDefault(require("../../database/models/paypal_webhook_event.model"));
const user_model_1 = __importDefault(require("../../database/models/user.model"));
const subscription_plans_mode_1 = __importDefault(require("../../database/models/subscription_plans.mode"));
const user_subscriptions_model_1 = __importDefault(require("../../database/models/user_subscriptions.model"));
const sequelize_1 = require("sequelize");
const client_id = process.env.PAYPAL_CLIENT_ID;
const client_secret = process.env.PAYPAL_CLIENT_SECRET;
class PaypalService {
    constructor() { }
}
_a = PaypalService;
PaypalService.generateAccessToken = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const response = yield axios_1.default.post(`${process.env.PAYPAL_API_URL}/v1/oauth2/token`, `grant_type=client_credentials`, {
            auth: {
                username: client_id,
                password: client_secret,
            },
        });
        return response.data.access_token;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
PaypalService.createOrder = (params) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c;
    try {
        const { user_id, plan_id, amount, currency } = params;
        const accessToken = yield _a.generateAccessToken();
        // Get subscription plan details if plan_id is provided
        let planDetails = null;
        if (plan_id) {
            planDetails = yield subscription_plans_mode_1.default.findByPk(plan_id);
            if (!planDetails) {
                throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Subscription plan not found");
            }
        }
        const orderData = {
            intent: "CAPTURE",
            purchase_units: [
                {
                    amount: {
                        currency_code: currency,
                        value: planDetails ? planDetails.price : amount.toFixed(2),
                    },
                    description: planDetails ?
                        `Subscription: ${planDetails.name}` :
                        "BarBadhu Payment",
                },
            ],
            application_context: {
                return_url: `${process.env.BASE_URL}/paypal/success`,
                cancel_url: `${process.env.BASE_URL}/paypal/cancel`,
                landing_page: "LOGIN",
                shipping_preference: "NO_SHIPPING",
                user_action: "PAY_NOW",
                brand_name: "BarBadhu",
            },
        };
        const response = yield (0, axios_1.default)({
            method: 'post',
            url: `${process.env.PAYPAL_API_URL}/v2/checkout/orders`,
            headers: {
                Authorization: `Bearer ${accessToken}`,
                "Content-Type": "application/json",
            },
            data: JSON.stringify(orderData),
        });
        // Save payment record to database
        const paymentRecord = yield paypal_payment_model_1.default.create({
            user_id,
            plan_id,
            paypal_order_id: response.data.id,
            amount: planDetails ? planDetails.price : amount,
            currency,
            status: "pending",
            payment_method: "paypal",
            description: planDetails ?
                `Subscription: ${planDetails.name}` :
                "BarBadhu Payment",
            paypal_response: response.data,
        });
        return Object.assign(Object.assign({}, response.data), { payment_id: paymentRecord.id });
    }
    catch (error) {
        console.error('PayPal create order error:', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, ((_c = (_b = error.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.message) || error.message);
    }
});
PaypalService.capturePayment = (order_id, user_id) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c, _d, _e, _f, _g;
    try {
        const accessToken = yield _a.generateAccessToken();
        // Find the payment record
        const paymentRecord = yield paypal_payment_model_1.default.findOne({
            where: { paypal_order_id: order_id, user_id }
        });
        if (!paymentRecord) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Payment record not found");
        }
        // Capture the payment
        const response = yield (0, axios_1.default)({
            method: 'post',
            url: `${process.env.PAYPAL_API_URL}/v2/checkout/orders/${order_id}/capture`,
            headers: {
                Authorization: `Bearer ${accessToken}`,
                "Content-Type": "application/json",
            },
        });
        // Update payment record
        const captureData = response.data;
        const paymentId = (_d = (_c = (_b = captureData.purchase_units[0]) === null || _b === void 0 ? void 0 : _b.payments) === null || _c === void 0 ? void 0 : _c.captures[0]) === null || _d === void 0 ? void 0 : _d.id;
        const payerId = (_e = captureData.payer) === null || _e === void 0 ? void 0 : _e.payer_id;
        yield paymentRecord.update({
            status: "completed",
            paypal_payment_id: paymentId,
            paypal_payer_id: payerId,
            paypal_response: captureData,
        });
        // If this is a subscription payment, create/update user subscription
        if (paymentRecord.plan_id) {
            yield _a.activateUserSubscription(user_id, paymentRecord.plan_id);
        }
        return Object.assign(Object.assign({}, captureData), { payment_id: paymentRecord.id });
    }
    catch (error) {
        console.error('PayPal capture payment error:', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, ((_g = (_f = error.response) === null || _f === void 0 ? void 0 : _f.data) === null || _g === void 0 ? void 0 : _g.message) || error.message);
    }
});
PaypalService.getPaymentDetails = (payment_id, user_id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const paymentRecord = yield paypal_payment_model_1.default.findOne({
            where: { id: payment_id, user_id },
            include: [
                {
                    model: user_model_1.default,
                    attributes: ['id', 'first_name', 'last_name', 'email']
                },
                {
                    model: subscription_plans_mode_1.default,
                    attributes: ['id', 'name', 'price', 'duration_days']
                }
            ]
        });
        if (!paymentRecord) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Payment not found");
        }
        return paymentRecord;
    }
    catch (error) {
        console.error('Get payment details error:', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
PaypalService.getUserPayments = (option) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, user_id } = option;
        const whereCondition = {};
        whereCondition.user_id = user_id;
        whereCondition.status = "completed";
        if (search) {
            whereCondition.title = { [sequelize_1.Op.like]: `%${search.toLowerCase()}%` };
        }
        const queryOption = {
            where: whereCondition,
            include: [
                {
                    model: subscription_plans_mode_1.default,
                    attributes: ['id', 'name', 'price', 'duration_days']
                }
            ],
            order: [["createdAt", "DESC"]],
        };
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const payments = yield paypal_payment_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: payments.count,
                totalPages: Math.ceil(payments.count / limit),
                currentPage: page,
                payments: payments.rows,
            };
        }
        else {
            return payments.rows;
        }
    }
    catch (error) {
        console.error('Get user payments error:', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
PaypalService.handleWebhookEvent = (webhookEvent, webhookId) => __awaiter(void 0, void 0, void 0, function* () {
    var _b;
    try {
        // Save webhook event for audit trail
        const webhookRecord = yield paypal_webhook_event_model_1.default.create({
            event_id: webhookEvent.id,
            event_type: webhookEvent.event_type,
            resource_type: webhookEvent.resource_type,
            resource_id: (_b = webhookEvent.resource) === null || _b === void 0 ? void 0 : _b.id,
            summary: webhookEvent.summary,
            event_data: webhookEvent,
            processed: false,
        });
        // Process different webhook events
        switch (webhookEvent.event_type) {
            case 'PAYMENT.CAPTURE.COMPLETED':
                yield _a.handlePaymentCompleted(webhookEvent);
                break;
            case 'PAYMENT.CAPTURE.DENIED':
            case 'PAYMENT.CAPTURE.DECLINED':
                yield _a.handlePaymentFailed(webhookEvent);
                break;
            case 'PAYMENT.CAPTURE.REFUNDED':
                yield _a.handlePaymentRefunded(webhookEvent);
                break;
            default:
                console.log(`Unhandled webhook event type: ${webhookEvent.event_type}`);
        }
        // Mark webhook as processed
        yield webhookRecord.update({
            processed: true,
            processed_at: new Date(),
        });
        return { success: true };
    }
    catch (error) {
        console.error('Webhook processing error:', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
PaypalService.getPaymentAnalytics = (params) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { start_date, end_date, user_id } = params;
        const whereClause = {};
        if (user_id) {
            whereClause.user_id = user_id;
        }
        if (start_date && end_date) {
            whereClause.created_at = {
                [sequelize_1.Op.between]: [new Date(start_date), new Date(end_date)]
            };
        }
        const totalPayments = yield paypal_payment_model_1.default.count({ where: whereClause });
        const completedPayments = yield paypal_payment_model_1.default.count({
            where: Object.assign(Object.assign({}, whereClause), { status: 'completed' })
        });
        const totalAmount = yield paypal_payment_model_1.default.sum('amount', {
            where: Object.assign(Object.assign({}, whereClause), { status: 'completed' })
        });
        const paymentsByStatus = yield paypal_payment_model_1.default.findAll({
            where: whereClause,
            attributes: [
                'status',
                [paypal_payment_model_1.default.sequelize.fn('COUNT', paypal_payment_model_1.default.sequelize.col('id')), 'count'],
                [paypal_payment_model_1.default.sequelize.fn('SUM', paypal_payment_model_1.default.sequelize.col('amount')), 'total_amount']
            ],
            group: ['status'],
            raw: true,
        });
        return {
            totalPayments,
            completedPayments,
            totalAmount: totalAmount || 0,
            paymentsByStatus,
        };
    }
    catch (error) {
        console.error('Get payment analytics error:', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
PaypalService.refundPayment = (payment_id, params) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c;
    try {
        const { amount, reason } = params;
        const accessToken = yield _a.generateAccessToken();
        // Find the payment record
        const paymentRecord = yield paypal_payment_model_1.default.findByPk(payment_id);
        if (!paymentRecord || !paymentRecord.paypal_payment_id) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Payment not found or not captured");
        }
        // Create refund request
        const refundData = {};
        if (amount) {
            refundData.amount = {
                value: amount.toFixed(2),
                currency_code: paymentRecord.currency
            };
        }
        if (reason) {
            refundData.note_to_payer = reason;
        }
        const response = yield (0, axios_1.default)({
            method: 'post',
            url: `${process.env.PAYPAL_API_URL}/v2/payments/captures/${paymentRecord.paypal_payment_id}/refund`,
            headers: {
                Authorization: `Bearer ${accessToken}`,
                "Content-Type": "application/json",
            },
            data: JSON.stringify(refundData),
        });
        // Update payment record
        yield paymentRecord.update({
            status: "refunded",
            refund_id: response.data.id,
            refund_amount: amount || paymentRecord.amount,
            refund_reason: reason,
        });
        return response.data;
    }
    catch (error) {
        console.error('PayPal refund error:', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, ((_c = (_b = error.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.message) || error.message);
    }
});
// Helper methods for webhook processing
PaypalService.handlePaymentCompleted = (webhookEvent) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c;
    try {
        const captureId = webhookEvent.resource.id;
        const orderId = (_c = (_b = webhookEvent.resource.supplementary_data) === null || _b === void 0 ? void 0 : _b.related_ids) === null || _c === void 0 ? void 0 : _c.order_id;
        if (orderId) {
            const paymentRecord = yield paypal_payment_model_1.default.findOne({
                where: { paypal_order_id: orderId }
            });
            if (paymentRecord) {
                yield paymentRecord.update({
                    status: "completed",
                    paypal_payment_id: captureId,
                    paypal_response: webhookEvent.resource,
                });
                // Activate subscription if applicable
                if (paymentRecord.plan_id) {
                    yield _a.activateUserSubscription(paymentRecord.user_id, paymentRecord.plan_id);
                }
            }
        }
    }
    catch (error) {
        console.error('Handle payment completed error:', error);
    }
});
PaypalService.handlePaymentFailed = (webhookEvent) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c;
    try {
        const orderId = (_c = (_b = webhookEvent.resource.supplementary_data) === null || _b === void 0 ? void 0 : _b.related_ids) === null || _c === void 0 ? void 0 : _c.order_id;
        if (orderId) {
            const paymentRecord = yield paypal_payment_model_1.default.findOne({
                where: { paypal_order_id: orderId }
            });
            if (paymentRecord) {
                yield paymentRecord.update({
                    status: "failed",
                    paypal_response: webhookEvent.resource,
                });
            }
        }
    }
    catch (error) {
        console.error('Handle payment failed error:', error);
    }
});
PaypalService.handlePaymentRefunded = (webhookEvent) => __awaiter(void 0, void 0, void 0, function* () {
    var _b, _c, _d;
    try {
        const refundId = webhookEvent.resource.id;
        const captureId = (_d = (_c = (_b = webhookEvent.resource.links) === null || _b === void 0 ? void 0 : _b.find((link) => link.rel === 'up')) === null || _c === void 0 ? void 0 : _c.href) === null || _d === void 0 ? void 0 : _d.split('/').pop();
        if (captureId) {
            const paymentRecord = yield paypal_payment_model_1.default.findOne({
                where: { paypal_payment_id: captureId }
            });
            if (paymentRecord) {
                yield paymentRecord.update({
                    status: "refunded",
                    refund_id: refundId,
                    refund_amount: parseFloat(webhookEvent.resource.amount.value),
                    paypal_response: webhookEvent.resource,
                });
            }
        }
    }
    catch (error) {
        console.error('Handle payment refunded error:', error);
    }
});
PaypalService.activateUserSubscription = (user_id, plan_id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const plan = yield subscription_plans_mode_1.default.findByPk(plan_id);
        if (!plan) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Subscription plan not found");
        }
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(startDate.getDate() + plan.duration_days);
        // Check if user already has an active subscription
        const existingSubscription = yield user_subscriptions_model_1.default.findOne({
            where: { user_id, is_active: true }
        });
        if (existingSubscription) {
            // Update existing subscription
            yield existingSubscription.update({
                plan_id,
                payment_status: "completed",
                start_date: startDate,
                end_date: endDate,
                expires_at: endDate,
                interest_sent_count: 0,
                contact_viewed_count: 0,
                profile_viewed_count: 0,
                chat_initiated_count: 0,
                is_active: true,
                token: `sub_${Date.now()}_${user_id}`,
            });
        }
        else {
            // Create new subscription
            yield user_subscriptions_model_1.default.create({
                user_id,
                plan_id,
                payment_status: "completed",
                start_date: startDate,
                end_date: endDate,
                auto_renew: false,
                issued_at: startDate,
                expires_at: endDate,
                interest_sent_count: 0,
                contact_viewed_count: 0,
                profile_viewed_count: 0,
                chat_initiated_count: 0,
                is_active: true,
                token: `sub_${Date.now()}_${user_id}`,
            });
        }
        return true;
    }
    catch (error) {
        console.error('Activate user subscription error:', error);
        throw error;
    }
});
PaypalService.getTransactions = (option) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page, limit, search, status } = option;
        const whereCondition = {};
        if (status) {
            whereCondition.status = status;
        }
        const queryOption = {
            where: whereCondition,
            include: [
                {
                    model: user_model_1.default,
                    as: 'user',
                    attributes: ['id', 'first_name', 'last_name', 'email']
                },
                {
                    model: subscription_plans_mode_1.default,
                    as: 'subscriptionPlan',
                    attributes: ['id', 'name', 'price', 'duration_days']
                }
            ],
            order: [["createdAt", "DESC"]],
        };
        if (search) {
            const escapedSearch = `%${search.toLowerCase()}%`;
            queryOption.where = Object.assign(Object.assign({}, whereCondition), { [sequelize_1.Op.or]: [
                    sequelize_1.Sequelize.literal(`LOWER(\`paypal_payment_id\`) LIKE '${escapedSearch}'`),
                    sequelize_1.Sequelize.literal(`LOWER(\`user\`.\`first_name\`) LIKE '${escapedSearch}'`),
                    sequelize_1.Sequelize.literal(`LOWER(\`user\`.\`last_name\`) LIKE '${escapedSearch}'`),
                    sequelize_1.Sequelize.literal(`LOWER(\`subscriptionPlan\`.\`name\`) LIKE '${escapedSearch}'`),
                ] });
        }
        if (page && limit) {
            const offset = (page - 1) * limit;
            queryOption.limit = limit;
            queryOption.offset = offset;
        }
        const transactions = yield paypal_payment_model_1.default.findAndCountAll(queryOption);
        if (page && limit) {
            return {
                totalItems: transactions.count,
                totalPages: Math.ceil(transactions.count / limit),
                currentPage: page,
                transactions: transactions.rows,
            };
        }
        else {
            return transactions.rows;
        }
    }
    catch (error) {
        console.error('Get transactions error:', error);
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = PaypalService;
