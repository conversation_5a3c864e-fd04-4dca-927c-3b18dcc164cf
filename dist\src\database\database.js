"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sequelize = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const user_model_1 = __importDefault(require("./models/user.model"));
const role_model_1 = __importDefault(require("./models/role.model"));
const token_model_1 = __importDefault(require("./models/token.model"));
const user_astro_details_model_1 = __importDefault(require("./models/user_astro_details.model"));
const user_basic_details_model_1 = __importDefault(require("./models/user_basic_details.model"));
const user_education_career_model_1 = __importDefault(require("./models/user_education_career.model"));
const user_family_details_model_1 = __importDefault(require("./models/user_family_details.model"));
const user_lifestyle_model_1 = __importDefault(require("./models/user_lifestyle.model"));
const user_location_details_model_1 = __importDefault(require("./models/user_location_details.model"));
const user_verifications_model_1 = __importDefault(require("./models/user_verifications.model"));
const admin_model_1 = __importDefault(require("./models/admin.model"));
const user_hobbies_model_1 = __importDefault(require("./models/user_hobbies.model"));
const user_shortlist_model_1 = __importDefault(require("./models/user_shortlist.model"));
const city_model_1 = __importDefault(require("./models/city.model"));
const country_model_1 = __importDefault(require("./models/country.model"));
const user_preferences_model_1 = __importDefault(require("./models/user_preferences.model"));
const user_subscriptions_model_1 = __importDefault(require("./models/user_subscriptions.model"));
const subscription_plans_mode_1 = __importDefault(require("./models/subscription_plans.mode"));
const privacy_settings_model_1 = __importDefault(require("./models/privacy_settings.model"));
const user_invitation_model_1 = __importDefault(require("./models/user_invitation.model"));
const chat_model_1 = __importDefault(require("./models/chat.model"));
const message_model_1 = __importDefault(require("./models/message.model"));
const success_story_model_1 = __importDefault(require("./models/success_story.model"));
const paypal_payment_model_1 = __importDefault(require("./models/paypal_payment.model"));
const paypal_webhook_event_model_1 = __importDefault(require("./models/paypal_webhook_event.model"));
const user_action_log_model_1 = __importDefault(require("./models/user_action_log.model"));
const khalti_payment_model_1 = __importDefault(require("./models/khalti_payment.model"));
const khalti_webhook_event_model_1 = __importDefault(require("./models/khalti_webhook_event.model"));
const success_story_media_model_1 = __importDefault(require("./models/success_story_media.model"));
const help_category_model_1 = __importDefault(require("./models/help_category.model"));
const help_question_model_1 = __importDefault(require("./models/help_question.model"));
const inquiries_model_1 = __importDefault(require("./models/inquiries.model"));
const user_gallery_model_1 = __importDefault(require("./models/user_gallery.model"));
const modules_model_1 = __importDefault(require("./models/modules.model"));
const role_permissions_model_1 = __importDefault(require("./models/role_permissions.model"));
const databaseName = process.env.DATABASE_NAME || "";
const databaseUser = process.env.DATABASE_USER || "";
const databasePassword = process.env.DATABASE_PASSWORD || "";
const host = process.env.DATABASE_HOST || "";
const port = parseInt(process.env.DATABASE_PORT || '', 10);
const dialect = process.env.DATABASE_DIALECT || "";
exports.sequelize = new sequelize_typescript_1.Sequelize(databaseName, databaseUser, databasePassword, {
    host,
    port,
    dialect: dialect,
    models: [
        user_model_1.default,
        role_model_1.default,
        admin_model_1.default,
        token_model_1.default,
        privacy_settings_model_1.default,
        user_subscriptions_model_1.default,
        subscription_plans_mode_1.default,
        user_basic_details_model_1.default,
        user_location_details_model_1.default,
        user_education_career_model_1.default,
        user_lifestyle_model_1.default,
        user_astro_details_model_1.default,
        user_family_details_model_1.default,
        user_gallery_model_1.default,
        user_hobbies_model_1.default,
        user_verifications_model_1.default,
        user_shortlist_model_1.default,
        country_model_1.default,
        city_model_1.default,
        user_preferences_model_1.default,
        user_invitation_model_1.default,
        chat_model_1.default,
        message_model_1.default,
        success_story_model_1.default,
        success_story_media_model_1.default,
        paypal_payment_model_1.default,
        paypal_webhook_event_model_1.default,
        user_action_log_model_1.default,
        khalti_payment_model_1.default,
        khalti_webhook_event_model_1.default,
        help_category_model_1.default,
        help_question_model_1.default,
        inquiries_model_1.default,
        modules_model_1.default,
        role_permissions_model_1.default
    ]
});
