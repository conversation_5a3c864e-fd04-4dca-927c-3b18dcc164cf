"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkActionPermission = exports.requireActiveSubscription = exports.authWithSubscription = void 0;
const http_status_1 = __importDefault(require("http-status"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const ApiError_1 = __importDefault(require("../utils/ApiError"));
const config_1 = require("../config/config");
const response_1 = __importDefault(require("../utils/response"));
const httpMessages_1 = __importDefault(require("../config/httpMessages"));
const subscription_service_1 = __importDefault(require("../app/subscription/subscription.service"));
const authWithSubscription = (request, response, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // First, validate the JWT token
        const authorization = request.headers.authorization;
        const token = (authorization === null || authorization === void 0 ? void 0 : authorization.split(" ")[1]) || "";
        const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret);
        const user = decoded.sub;
        if (!user) {
            return (0, response_1.default)(response, new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED));
        }
        // Add user to request
        request["decoded"] = user;
        // Check subscription status
        try {
            const subscriptionStatus = yield subscription_service_1.default.getActiveSubscription(user);
            // Add subscription info to request
            request["subscription"] = {
                isActive: subscriptionStatus.isActive,
                subscription: subscriptionStatus.subscription,
                plan: subscriptionStatus.plan,
                message: subscriptionStatus.message,
                remainingUsage: subscriptionStatus.remainingUsage
            };
            // If subscription is not active, add warning but don't block
            if (!subscriptionStatus.isActive) {
                request["subscriptionWarning"] = {
                    hasActiveSubscription: false,
                    message: subscriptionStatus.message,
                    requiresUpgrade: true
                };
            }
        }
        catch (subscriptionError) {
            console.error('Subscription check error:', subscriptionError);
            // Don't block request if subscription check fails
            request["subscription"] = {
                isActive: false,
                message: "Unable to verify subscription status"
            };
        }
        return next();
    }
    catch (e) {
        return (0, response_1.default)(response, new ApiError_1.default(http_status_1.default.UNAUTHORIZED, httpMessages_1.default.USER.AUTH.UNAUTHORIZED));
    }
});
exports.authWithSubscription = authWithSubscription;
// Middleware that requires active subscription
const requireActiveSubscription = (request, response, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        // First run auth with subscription
        yield (0, exports.authWithSubscription)(request, response, () => { });
        // Check if subscription is active
        if (!((_a = request.subscription) === null || _a === void 0 ? void 0 : _a.isActive)) {
            return (0, response_1.default)(response, new ApiError_1.default(http_status_1.default.FORBIDDEN, ((_b = request.subscription) === null || _b === void 0 ? void 0 : _b.message) || "Active subscription required"));
        }
        return next();
    }
    catch (error) {
        return (0, response_1.default)(response, new ApiError_1.default(http_status_1.default.UNAUTHORIZED, "Authentication failed"));
    }
});
exports.requireActiveSubscription = requireActiveSubscription;
// Middleware to check if user can perform specific action
const checkActionPermission = (actionType) => {
    return (request, response, next) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            // First run auth with subscription
            yield (0, exports.authWithSubscription)(request, response, () => { });
            const user_id = request.decoded;
            const target_user_id = request.params.id || request.body.receiver_id;
            // Check if user can perform the action
            const canPerform = yield subscription_service_1.default.canPerformAction(user_id, actionType, target_user_id ? Number(target_user_id) : undefined);
            if (!canPerform.canPerform) {
                return (0, response_1.default)(response, new ApiError_1.default(http_status_1.default.FORBIDDEN, canPerform.message));
            }
            // Add permission info to request
            request["actionPermission"] = canPerform;
            return next();
        }
        catch (error) {
            return (0, response_1.default)(response, new ApiError_1.default(http_status_1.default.UNAUTHORIZED, "Permission check failed"));
        }
    });
};
exports.checkActionPermission = checkActionPermission;
