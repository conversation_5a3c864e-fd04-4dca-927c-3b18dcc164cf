"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const sequelize_1 = require("sequelize");
const ApiError_1 = __importDefault(require("../../../utils/ApiError"));
const user_invitation_model_1 = __importDefault(require("../../../database/models/user_invitation.model"));
const user_model_1 = __importDefault(require("../../../database/models/user.model"));
const email_service_1 = __importDefault(require("../../../common/services/email.service"));
const user_basic_details_model_1 = __importDefault(require("../../../database/models/user_basic_details.model"));
const user_lifestyle_model_1 = __importDefault(require("../../../database/models/user_lifestyle.model"));
const user_education_career_model_1 = __importDefault(require("../../../database/models/user_education_career.model"));
const user_location_details_model_1 = __importDefault(require("../../../database/models/user_location_details.model"));
const subscription_service_1 = __importDefault(require("../../subscription/subscription.service"));
class UserInvitationService {
    constructor() { }
}
_a = UserInvitationService;
UserInvitationService.SubscriptionService = subscription_service_1.default;
/**
 * Create an invitation
 * @param {Object} invitationData
 * @returns {Promise<UserInvitation>}
 */
UserInvitationService.createInvitation = (invitationData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Check if invitation already exists
        const existingInvitation = yield user_invitation_model_1.default.findOne({
            where: {
                sender_id: invitationData.sender_id,
                receiver_id: invitationData.receiver_id,
                status: "pending"
            }
        });
        if (existingInvitation) {
            throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, "Invitation already sent to this user");
        }
        // await SubscriptionService.trackUsage({
        //   user_id: invitationData.sender_id,
        //   action_type: 'interest_sent',
        //   target_user_id: invitationData.receiver_id
        // });
        const invitation = yield user_invitation_model_1.default.create(invitationData);
        // Get sender and receiver details
        const sender = yield user_model_1.default.findByPk(invitationData.sender_id);
        const receiver = yield user_model_1.default.findByPk(invitationData.receiver_id);
        if (sender && receiver) {
            // Send email notification
            const inviteLink = `${process.env.FRONTEND_URL}/inbox?type=invitations`;
            let html = yield email_service_1.default.sendInvitationEmail(receiver.email, {
                senderName: `${sender.first_name} ${sender.last_name}`,
                receiverName: `${receiver.first_name} ${receiver.last_name}`,
                message: invitationData.message,
                inviteLink
            });
        }
        return invitation;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get all invitations for a user
 * @param {number} userId
 * @param {string} status - 'received', 'sent', 'accepted', 'declined'
 * @returns {Promise<UserInvitation[]>}
 */
UserInvitationService.getUserInvitations = (userId, status) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('status: ', status);
        const whereClause = {};
        if (status === 'pending') {
            whereClause.receiver_id = userId;
            whereClause.status = 'pending';
        }
        else if (status === 'sent') {
            whereClause.sender_id = userId;
            whereClause.status = 'pending';
        }
        else if (status === 'accepted') {
            whereClause[sequelize_1.Op.or] = [
                { sender_id: userId, status: 'accepted' },
                { receiver_id: userId, status: 'accepted' }
            ];
            whereClause.status = 'accepted';
        }
        else if (status === 'declined') {
            whereClause[sequelize_1.Op.or] = [
                { sender_id: userId, status: 'declined' },
                { receiver_id: userId, status: 'declined' }
            ];
            whereClause.status = 'declined';
        }
        console.log('whereClause: ', whereClause);
        const invitations = yield user_invitation_model_1.default.findAll({
            where: whereClause,
            include: [
                {
                    model: user_model_1.default,
                    as: 'sender',
                    attributes: ['id', 'first_name', 'last_name', 'profile_created_for', 'member_id', 'profile_image'],
                    include: [
                        {
                            model: user_basic_details_model_1.default,
                            attributes: ['religion', 'caste', 'gotra', 'marital_status']
                        },
                        {
                            model: user_lifestyle_model_1.default,
                            attributes: ['age', 'height_cm']
                        },
                        {
                            model: user_education_career_model_1.default,
                            attributes: ['education', 'profession']
                        },
                        {
                            model: user_location_details_model_1.default,
                            attributes: ['city', 'country_living_in']
                        }
                    ]
                },
                {
                    model: user_model_1.default,
                    as: 'receiver',
                    attributes: ['id', 'first_name', 'last_name', 'profile_created_for', 'member_id', 'profile_image'],
                    include: [
                        {
                            model: user_basic_details_model_1.default,
                            attributes: ['religion', 'caste', 'gotra', 'marital_status']
                        },
                        {
                            model: user_lifestyle_model_1.default,
                            attributes: ['age', 'height_cm']
                        },
                        {
                            model: user_education_career_model_1.default,
                            attributes: ['education', 'profession']
                        },
                        {
                            model: user_location_details_model_1.default,
                            attributes: ['city', 'country_living_in']
                        }
                    ]
                }
            ],
            order: [['createdAt', 'DESC']]
        });
        const processedInvitations = invitations.map(invitation => {
            const invitationObj = invitation.toJSON();
            if (invitation.sender_id === userId) {
                invitationObj.other_user = 'receiver';
            }
            else {
                invitationObj.other_user = 'sender';
            }
            return invitationObj;
        });
        return processedInvitations;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
* Get all invitations for a user
* @param {number} userId
* @param {string} type - 'received', 'sent', 'accepted', 'declined'
* @returns {Promise<UserInvitation[]>}
*/
UserInvitationService.all = (userId_1, ...args_1) => __awaiter(void 0, [userId_1, ...args_1], void 0, function* (userId, type = 'received') {
    try {
        const whereClause = {
            [sequelize_1.Op.or]: [
                { sender_id: userId },
                { receiver_id: userId }
            ]
        };
        const invitations = yield user_invitation_model_1.default.findAll({
            where: whereClause,
            include: [
                {
                    model: user_model_1.default,
                    as: 'sender',
                    attributes: ['id', 'first_name', 'last_name', 'profile_created_for', 'member_id', 'profile_image'],
                    include: [
                        {
                            model: user_basic_details_model_1.default,
                            attributes: ['religion', 'caste', 'gotra', 'marital_status']
                        },
                        {
                            model: user_lifestyle_model_1.default,
                            attributes: ['age', 'height_cm']
                        },
                        {
                            model: user_education_career_model_1.default,
                            attributes: ['education', 'profession']
                        },
                        {
                            model: user_location_details_model_1.default,
                            attributes: ['city', 'country_living_in']
                        }
                    ]
                },
                {
                    model: user_model_1.default,
                    as: 'receiver',
                    attributes: ['id', 'first_name', 'last_name', 'profile_created_for', 'member_id', 'profile_image'],
                    include: [
                        {
                            model: user_basic_details_model_1.default,
                            attributes: ['religion', 'caste', 'gotra', 'marital_status']
                        },
                        {
                            model: user_lifestyle_model_1.default,
                            attributes: ['age', 'height_cm']
                        },
                        {
                            model: user_education_career_model_1.default,
                            attributes: ['education', 'profession']
                        },
                        {
                            model: user_location_details_model_1.default,
                            attributes: ['city', 'country_living_in']
                        }
                    ]
                }
            ],
            order: [['createdAt', 'DESC']]
        });
        const result = {
            invitationUsers: [], // pending received invitations
            acceptedUsers: [], // accepted invitations
            sentUsers: [], // sent invitations
            declinedUsers: [] // declined invitations
        };
        invitations.forEach((invitation) => {
            if (invitation.status === 'pending') {
                result.invitationUsers.push(invitation);
            }
            if (invitation.status === 'accepted') {
                result.acceptedUsers.push(invitation);
            }
            if (invitation.status === 'declined') {
                result.declinedUsers.push(invitation);
            }
            if (invitation.sender_id === userId) {
                result.sentUsers.push(invitation);
            }
        });
        return result;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Format height from cm to feet and inches
 * @param {number} heightCm - Height in centimeters
 * @returns {string} - Formatted height string (e.g., "5'2\"")
 */
UserInvitationService.formatHeightToFeetInches = (heightCm) => {
    if (!heightCm)
        return '';
    // Convert cm to inches
    const totalInches = heightCm / 2.54;
    // Calculate feet and remaining inches
    const feet = Math.floor(totalInches / 12);
    const inches = Math.round(totalInches % 12);
    return `${feet}'${inches}"`;
};
/**
 * Format location from separate fields
 * @param {Object} locationDetails - Location details object
 * @returns {string} - Formatted location string
 */
UserInvitationService.formatLocation = (locationDetails) => {
    if (!locationDetails)
        return '';
    const parts = [];
    if (locationDetails.city)
        parts.push(locationDetails.city);
    if (locationDetails.country)
        parts.push(locationDetails.country);
    return parts.join(', ');
};
/**
 * Get context message for declined invitations
 * @param {UserInvitation} invitation - The invitation object
 * @param {number} userId - Current user ID
 * @returns {string} - Context message
 */
UserInvitationService.getDeclinedContext = (invitation, userId) => {
    if (invitation.status !== 'declined')
        return '';
    // If current user is the sender and invitation was declined
    if (invitation.sender_id === userId) {
        return 'She declined your invitation. You cannot contact her for 7 days';
    }
    // If current user is the receiver and they declined the invitation
    if (invitation.receiver_id === userId) {
        return 'You declined her Invitation';
    }
    return '';
};
/**
 * Update invitation status
 * @param {number} invitationId
 * @param {string} status - 'accepted' or 'declined'
 * @param {number} userId - the user updating the status
 * @returns {Promise<UserInvitation>}
 */
UserInvitationService.updateInvitationStatus = (invitationId, status, userId, userRole) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        let whereClause = {};
        if (userRole === 'sender') {
            whereClause = {
                id: invitationId,
                sender_id: userId,
                status: 'pending'
            };
        }
        else {
            whereClause = {
                id: invitationId,
                receiver_id: userId,
                status: 'pending'
            };
        }
        const invitation = yield user_invitation_model_1.default.findOne({
            where: whereClause,
            include: [
                { model: user_model_1.default, as: 'sender' },
                { model: user_model_1.default, as: 'receiver' }
            ]
        });
        if (!invitation) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Invitation not found or already processed");
        }
        invitation.status = status;
        yield invitation.save();
        // Send email notification to sender
        const sender = invitation.sender;
        const receiver = invitation.receiver;
        if (status === 'accepted') {
            yield email_service_1.default.sendInvitationAcceptedEmail(sender.email, {
                senderName: `${sender.first_name} ${sender.last_name}`,
                receiverName: `${receiver.first_name} ${receiver.last_name}`,
                message: invitation.message
            });
        }
        else {
            yield email_service_1.default.sendInvitationDeclinedEmail(sender.email, {
                senderName: `${sender.first_name} ${sender.last_name}`,
                receiverName: `${receiver.first_name} ${receiver.last_name}`,
                message: invitation.message
            });
        }
        return invitation;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get unread invitation count
 * @param {number} userId
 * @returns {Promise<number>}
 */
UserInvitationService.getUnreadCount = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const count = yield user_invitation_model_1.default.count({
            where: {
                receiver_id: userId,
                is_read: false
            }
        });
        return count;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Mark invitation as read
 * @param {number} invitationId
 * @param {number} userId
 * @returns {Promise<UserInvitation>}
 */
UserInvitationService.markAsRead = (invitationId, userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const invitation = yield user_invitation_model_1.default.findOne({
            where: {
                id: invitationId,
                receiver_id: userId,
                is_read: false
            }
        });
        if (!invitation) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Invitation not found or already read");
        }
        invitation.is_read = true;
        yield invitation.save();
        return invitation;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Check if an invitation exists between two users
 * @param {number} userId1 - First user ID
 * @param {number} userId2 - Second user ID
 * @returns {Promise<UserInvitation|null>} - The invitation if it exists, null otherwise
 */
UserInvitationService.checkInvitationExists = (userId1, userId2) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const invitation = yield user_invitation_model_1.default.findOne({
            where: {
                [sequelize_1.Op.or]: [
                    { sender_id: userId1, receiver_id: userId2 },
                    { sender_id: userId2, receiver_id: userId1 }
                ]
            }
        });
        return invitation;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Get invitation by ID
 * @param {number} invitationId - Invitation ID
 * @returns {Promise<UserInvitation|null>} - The invitation if it exists, null otherwise
 */
UserInvitationService.getInvitationById = (invitationId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const invitation = yield user_invitation_model_1.default.findOne({
            where: { id: invitationId },
            include: [
                { model: user_model_1.default, as: 'sender' },
                { model: user_model_1.default, as: 'receiver' }
            ]
        });
        return invitation;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
/**
 * Remove friend (change invitation status to declined)
 * @param {number} invitationId - Invitation ID
 * @param {number} userId - User ID
 * @returns {Promise<UserInvitation>} - The updated invitation
 */
UserInvitationService.removeFriend = (invitationId, userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('invitationId: ', invitationId);
        const invitation = yield user_invitation_model_1.default.findOne({
            where: {
                id: invitationId,
                status: 'accepted',
                // [Op.or]: [
                //   { sender_id: userId },
                //   { receiver_id: userId }
                // ]
            }
        });
        if (!invitation) {
            throw new ApiError_1.default(http_status_1.default.NOT_FOUND, "Accepted invitation not found");
        }
        invitation.status = 'declined';
        yield invitation.save();
        return invitation;
    }
    catch (error) {
        throw new ApiError_1.default(http_status_1.default.BAD_REQUEST, error.message);
    }
});
exports.default = UserInvitationService;
