"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const joi_1 = __importDefault(require("joi"));
const createChat = {
    body: joi_1.default.object().keys({
        user2Id: joi_1.default.number().required(),
    }),
};
const getChatById = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required(),
    }),
};
const getChatMessages = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required(),
    }),
    query: joi_1.default.object().keys({
        page: joi_1.default.number().integer().min(1),
        limit: joi_1.default.number().integer().min(1).max(100),
    }),
};
const setAutoDeleteDays = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required(),
    }),
    body: joi_1.default.object().keys({
        days: joi_1.default.number().integer().min(1).required(),
    }),
};
const deleteChat = {
    params: joi_1.default.object().keys({
        id: joi_1.default.number().required(),
    }),
};
exports.default = {
    createChat,
    getChatById,
    getChatMessages,
    setAutoDeleteDays,
    deleteChat,
};
