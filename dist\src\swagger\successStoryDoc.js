"use strict";
/**
 * @swagger
 * components:
 *   schemas:
 *     SuccessStory:
 *       type: object
 *       required:
 *         - bride_name
 *         - bride_email
 *         - groom_name
 *         - groom_email
 *         - bride_country
 *         - groom_country
 *         - meeting_date
 *         - engagement_date
 *         - marriage_date
 *         - current_country
 *         - story_text
 *       properties:
 *         id:
 *           type: integer
 *           description: The auto-generated id of the success story
 *         user_id:
 *           type: integer
 *           description: The user ID who submitted the story
 *         bride_name:
 *           type: string
 *           description: The bride's name
 *         bride_email:
 *           type: string
 *           format: email
 *           description: The bride's email address
 *         groom_name:
 *           type: string
 *           description: The groom's name
 *         groom_email:
 *           type: string
 *           format: email
 *           description: The groom's email address
 *         bride_country:
 *           type: string
 *           description: The country where the bride was living
 *         groom_country:
 *           type: string
 *           description: The country where the groom was living
 *         meeting_date:
 *           type: string
 *           format: date
 *           description: The date when the couple met
 *         engagement_date:
 *           type: string
 *           format: date
 *           description: The date of engagement
 *         marriage_date:
 *           type: string
 *           format: date
 *           description: The date of marriage
 *         current_country:
 *           type: string
 *           description: The current country of residence
 *         contact_number:
 *           type: string
 *           description: Contact number (optional)
 *         story_text:
 *           type: string
 *           description: The success story text
 *         status:
 *           type: string
 *           enum: [pending, approved, rejected]
 *           description: The status of the success story
 *         admin_notes:
 *           type: string
 *           description: Admin notes for the story (visible only to admins)
 *         allow_in_ads:
 *           type: boolean
 *           description: Whether the story can be used in advertisements
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the story was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the story was last updated
 *         media:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/SuccessStoryMedia'
 *           description: Media files associated with the success story
 *
 *     SuccessStoryMedia:
 *       type: object
 *       required:
 *         - story_id
 *         - media_type
 *         - media_path
 *       properties:
 *         id:
 *           type: integer
 *           description: The auto-generated id of the media
 *         story_id:
 *           type: integer
 *           description: The success story ID this media belongs to
 *         media_type:
 *           type: string
 *           enum: [image, video]
 *           description: The type of media
 *         media_path:
 *           type: string
 *           description: The path to the media file
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the media was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the media was last updated
 *
 *     SuccessStoryStatusUpdate:
 *       type: object
 *       required:
 *         - status
 *       properties:
 *         status:
 *           type: string
 *           enum: [pending, approved, rejected]
 *           description: The new status for the success story
 *         admin_notes:
 *           type: string
 *           description: Admin notes for the story (optional)
 */
/**
 * @swagger
 * tags:
 *   name: SuccessStories
 *   description: Success stories management
 */
/**
 * @swagger
 * /success-story/approved:
 *   get:
 *     summary: Get all approved success stories
 *     tags: [SuccessStories]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of approved success stories
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     currentPage:
 *                       type: integer
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/SuccessStory'
 */
/**
 * @swagger
 * /success-story/user:
 *   get:
 *     summary: Get current user's success stories
 *     tags: [SuccessStories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of user's success stories
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     currentPage:
 *                       type: integer
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/SuccessStory'
 *       401:
 *         description: Unauthorized
 */
/**
 * @swagger
 * /success-story:
 *   post:
 *     summary: Submit a new success story
 *     tags: [SuccessStories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - bride_name
 *               - bride_email
 *               - groom_name
 *               - groom_email
 *               - bride_country
 *               - groom_country
 *               - meeting_date
 *               - engagement_date
 *               - marriage_date
 *               - current_country
 *               - story_text
 *             properties:
 *               bride_name:
 *                 type: string
 *               bride_email:
 *                 type: string
 *                 format: email
 *               groom_name:
 *                 type: string
 *               groom_email:
 *                 type: string
 *                 format: email
 *               bride_country:
 *                 type: string
 *               groom_country:
 *                 type: string
 *               meeting_date:
 *                 type: string
 *                 format: date
 *               engagement_date:
 *                 type: string
 *                 format: date
 *               marriage_date:
 *                 type: string
 *                 format: date
 *               current_country:
 *                 type: string
 *               contact_number:
 *                 type: string
 *               story_text:
 *                 type: string
 *               allow_in_ads:
 *                 type: boolean
 *               media_files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *     responses:
 *       200:
 *         description: Success story submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/SuccessStory'
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 */
/**
 * @swagger
 * /success-story:
 *   get:
 *     summary: Get all success stories (admin)
 *     tags: [SuccessStories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, approved, rejected]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: List of all success stories
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     currentPage:
 *                       type: integer
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/SuccessStory'
 *       401:
 *         description: Unauthorized
 */
/**
 * @swagger
 * /success-story/{id}:
 *   get:
 *     summary: Get a success story by ID
 *     tags: [SuccessStories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Success story ID
 *     responses:
 *       200:
 *         description: Success story details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/SuccessStory'
 *       404:
 *         description: Success story not found
 *       401:
 *         description: Unauthorized
 */
/**
 * @swagger
 * /success-story/{id}/status:
 *   put:
 *     summary: Update a success story status (admin)
 *     tags: [SuccessStories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Success story ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SuccessStoryStatusUpdate'
 *     responses:
 *       200:
 *         description: Success story status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/SuccessStory'
 *       404:
 *         description: Success story not found
 *       401:
 *         description: Unauthorized
 */
/**
 * @swagger
 * /success-story/{id}:
 *   delete:
 *     summary: Delete a success story (admin)
 *     tags: [SuccessStories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Success story ID
 *     responses:
 *       200:
 *         description: Success story deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *       404:
 *         description: Success story not found
 *       401:
 *         description: Unauthorized
 */
