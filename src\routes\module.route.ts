import express from "express";
import { auth } from "../middlewares/auth";
import { validate } from "../middlewares/middleware";
import ModuleController from "../app/admin/module/module.controller";
import { moduleValidation } from "../validations/module.validation";
const router = express.Router();

router.get("", auth, ModuleController.getAll);
router.post("", auth,validate(moduleValidation), ModuleController.create);
router.get("/:id", auth, ModuleController.showById);
router.put("/:id", auth, ModuleController.update);
router.delete("/:id", auth, ModuleController.delete);

export default router;
