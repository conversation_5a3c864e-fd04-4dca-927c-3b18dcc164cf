"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const http_status_1 = __importDefault(require("http-status"));
const ApiError_1 = __importDefault(require("../../utils/ApiError"));
const promo_codes_model_1 = __importDefault(require("../../database/models/promo_codes.model"));
const promo_code_usage_model_1 = __importDefault(require("../../database/models/promo_code_usage.model"));
const user_subscriptions_model_1 = __importDefault(require("../../database/models/user_subscriptions.model"));
class PromoCodeService {
}
_a = PromoCodeService;
/**
 * Validate a promo code for a specific user and plan
 * @param {ValidatePromoCodeParams} params
 * @returns {Promise<PromoCodeValidationResult>}
 */
PromoCodeService.validatePromoCode = (params) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { code, user_id, plan_id, plan_price } = params;
        // Find the promo code
        const promoCode = yield promo_codes_model_1.default.findOne({
            where: {
                code: code.toUpperCase(),
                is_active: true
            }
        });
        if (!promoCode) {
            return {
                isValid: false,
                message: "Invalid promo code"
            };
        }
        // Check if promo code has started
        if (promoCode.start_date && new Date() < promoCode.start_date) {
            return {
                isValid: false,
                message: "Promo code is not yet active"
            };
        }
        // Check if promo code has expired
        if (promoCode.expiry_date && new Date() > promoCode.expiry_date) {
            return {
                isValid: false,
                message: "Promo code has expired"
            };
        }
        // Check usage limit
        if (promoCode.usage_limit && promoCode.used_count >= promoCode.usage_limit) {
            return {
                isValid: false,
                message: "Promo code usage limit exceeded"
            };
        }
        // Check if user has already used this promo code
        const existingUsage = yield promo_code_usage_model_1.default.findOne({
            where: {
                promo_code_id: promoCode.id,
                user_id
            }
        });
        if (existingUsage) {
            return {
                isValid: false,
                message: "You have already used this promo code"
            };
        }
        // Check if promo code is applicable to the selected plan
        if (promoCode.applicable_plans) {
            const applicablePlans = JSON.parse(promoCode.applicable_plans);
            if (!applicablePlans.includes(plan_id)) {
                return {
                    isValid: false,
                    message: "Promo code is not applicable to this plan"
                };
            }
        }
        // Check minimum purchase amount
        if (promoCode.minimum_purchase_amount && plan_price < promoCode.minimum_purchase_amount) {
            return {
                isValid: false,
                message: `Minimum purchase amount of $${promoCode.minimum_purchase_amount} required`
            };
        }
        // Check if it's for first-time users only
        if (promoCode.first_time_users_only) {
            const existingSubscription = yield user_subscriptions_model_1.default.findOne({
                where: {
                    user_id,
                    payment_status: "completed"
                }
            });
            if (existingSubscription) {
                return {
                    isValid: false,
                    message: "This promo code is only for first-time users"
                };
            }
        }
        // Calculate discount
        const { discountAmount, finalAmount } = _a.calculateDiscount(plan_price, promoCode.discount_type, promoCode.discount_value, promoCode.maximum_discount_amount);
        return {
            isValid: true,
            promoCode,
            discountAmount,
            finalAmount,
            message: "Promo code is valid"
        };
    }
    catch (error) {
        console.error('Validate promo code error:', error);
        throw new ApiError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, error.message);
    }
});
/**
 * Calculate discount amount based on discount type and value
 * @param {number} originalAmount
 * @param {string} discountType
 * @param {number} discountValue
 * @param {number} maxDiscountAmount
 * @returns {object}
 */
PromoCodeService.calculateDiscount = (originalAmount, discountType, discountValue, maxDiscountAmount) => {
    let discountAmount = 0;
    if (discountType === "percentage") {
        discountAmount = (originalAmount * discountValue) / 100;
        // Apply maximum discount limit if specified
        if (maxDiscountAmount && discountAmount > maxDiscountAmount) {
            discountAmount = maxDiscountAmount;
        }
    }
    else if (discountType === "fixed_amount") {
        discountAmount = Math.min(discountValue, originalAmount);
    }
    const finalAmount = Math.max(0, originalAmount - discountAmount);
    return {
        discountAmount: Math.round(discountAmount * 100) / 100, // Round to 2 decimal places
        finalAmount: Math.round(finalAmount * 100) / 100
    };
};
/**
 * Apply promo code and record usage
 * @param {ApplyPromoCodeParams} params
 * @returns {Promise<PromoCodeUsage>}
 */
PromoCodeService.applyPromoCode = (params) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { promo_code_id, user_id, subscription_id, original_amount, discount_amount, final_amount } = params;
        // Record promo code usage
        let body = {
            promo_code_id,
            user_id,
            subscription_id,
            discount_amount,
            original_amount,
            final_amount,
            used_at: new Date()
        };
        const usage = yield promo_code_usage_model_1.default.create(body);
        // Increment used count for the promo code
        yield promo_codes_model_1.default.increment('used_count', {
            where: { id: promo_code_id }
        });
        return usage;
    }
    catch (error) {
        console.error('Apply promo code error:', error);
        throw new ApiError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, error.message);
    }
});
/**
 * Get promo code by code
 * @param {string} code
 * @returns {Promise<PromoCode | null>}
 */
PromoCodeService.getPromoCodeByCode = (code) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return yield promo_codes_model_1.default.findOne({
            where: {
                code: code.toUpperCase(),
                is_active: true
            }
        });
    }
    catch (error) {
        console.error('Get promo code error:', error);
        throw new ApiError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, error.message);
    }
});
/**
 * Get user's promo code usage history
 * @param {number} user_id
 * @returns {Promise<PromoCodeUsage[]>}
 */
PromoCodeService.getUserPromoCodeUsage = (user_id) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return yield promo_code_usage_model_1.default.findAll({
            where: { user_id },
            include: [
                {
                    model: promo_codes_model_1.default,
                    attributes: ['code', 'description', 'discount_type', 'discount_value']
                }
            ],
            order: [['used_at', 'DESC']]
        });
    }
    catch (error) {
        console.error('Get user promo code usage error:', error);
        throw new ApiError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, error.message);
    }
});
exports.default = PromoCodeService;
