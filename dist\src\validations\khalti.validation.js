"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.webhookValidation = exports.checkPaymentStatusValidation = exports.getAnalyticsValidation = exports.getTransactionsValidation = exports.getUserPaymentsValidation = exports.getPaymentByIdValidation = exports.verifyPaymentValidation = exports.initiatePaymentValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.initiatePaymentValidation = {
    body: joi_1.default.object().keys({
        plan_id: joi_1.default.number().integer().positive().optional().messages({
            "number.base": "Plan ID must be a number.",
            "number.integer": "Plan ID must be an integer.",
            "number.positive": "Plan ID must be a positive number.",
        }),
        amount: joi_1.default.number().positive().when('plan_id', {
            is: joi_1.default.exist(),
            then: joi_1.default.optional(),
            otherwise: joi_1.default.required()
        }).messages({
            "number.base": "Amount must be a number.",
            "number.positive": "Amount must be a positive number.",
            "any.required": "Amount is required when plan_id is not provided.",
        }),
        mobile: joi_1.default.string().pattern(/^98\d{8}$/).optional().messages({
            "string.base": "Mobile must be a string.",
            "string.pattern.base": "Mobile must be a valid Nepali mobile number (98XXXXXXXX).",
        }),
        product_identity: joi_1.default.string().max(100).optional().messages({
            "string.base": "Product identity must be a string.",
            "string.max": "Product identity cannot exceed 100 characters.",
        }),
        product_name: joi_1.default.string().max(255).optional().messages({
            "string.base": "Product name must be a string.",
            "string.max": "Product name cannot exceed 255 characters.",
        }),
        product_url: joi_1.default.string().uri().optional().messages({
            "string.base": "Product URL must be a string.",
            "string.uri": "Product URL must be a valid URI.",
        }),
        return_url: joi_1.default.string().uri().optional().messages({
            "string.base": "Return URL must be a string.",
            "string.uri": "Return URL must be a valid URI.",
        }),
        website_url: joi_1.default.string().uri().optional().messages({
            "string.base": "Website URL must be a string.",
            "string.uri": "Website URL must be a valid URI.",
        }),
    }),
};
exports.verifyPaymentValidation = {
    body: joi_1.default.object().keys({
        pidx: joi_1.default.string().required().messages({
            "string.base": "Payment index (pidx) must be a string.",
            "any.required": "Payment index (pidx) is required.",
        }),
    }),
};
exports.getPaymentByIdValidation = {
    params: joi_1.default.object().keys({
        payment_id: joi_1.default.number().integer().positive().required().messages({
            "number.base": "Payment ID must be a number.",
            "number.integer": "Payment ID must be an integer.",
            "number.positive": "Payment ID must be a positive number.",
            "any.required": "Payment ID is required.",
        }),
    }),
};
exports.getUserPaymentsValidation = {
    query: joi_1.default.object().keys({
        page: joi_1.default.number().integer().min(1).optional().default(1).messages({
            "number.base": "Page must be a number.",
            "number.integer": "Page must be an integer.",
            "number.min": "Page must be at least 1.",
        }),
        limit: joi_1.default.number().integer().min(1).max(100).optional().default(10).messages({
            "number.base": "Limit must be a number.",
            "number.integer": "Limit must be an integer.",
            "number.min": "Limit must be at least 1.",
            "number.max": "Limit cannot exceed 100.",
        }),
        status: joi_1.default.string().valid("pending", "completed", "failed", "cancelled", "refunded", "expired").optional().messages({
            "string.base": "Status must be a string.",
            "any.only": "Status must be one of: pending, completed, failed, cancelled, refunded, expired.",
        }),
    }),
};
exports.getTransactionsValidation = {
    query: joi_1.default.object().keys({
        page: joi_1.default.number().integer().min(1).optional().default(1).messages({
            "number.base": "Page must be a number.",
            "number.integer": "Page must be an integer.",
            "number.min": "Page must be at least 1.",
        }),
        limit: joi_1.default.number().integer().min(1).max(100).optional().default(10).messages({
            "number.base": "Limit must be a number.",
            "number.integer": "Limit must be an integer.",
            "number.min": "Limit must be at least 1.",
            "number.max": "Limit cannot exceed 100.",
        }),
        status: joi_1.default.string().valid("pending", "completed", "failed", "cancelled", "refunded", "expired").optional().messages({
            "string.base": "Status must be a string.",
            "any.only": "Status must be one of: pending, completed, failed, cancelled, refunded, expired.",
        }),
        search: joi_1.default.string().max(255).optional().messages({
            "string.base": "Search must be a string.",
            "string.max": "Search cannot exceed 255 characters.",
        }),
    }),
};
exports.getAnalyticsValidation = {
    query: joi_1.default.object().keys({
        start_date: joi_1.default.date().iso().optional().messages({
            "date.base": "Start date must be a valid date.",
            "date.format": "Start date must be in ISO format (YYYY-MM-DD).",
        }),
        end_date: joi_1.default.date().iso().min(joi_1.default.ref('start_date')).optional().messages({
            "date.base": "End date must be a valid date.",
            "date.format": "End date must be in ISO format (YYYY-MM-DD).",
            "date.min": "End date must be after start date.",
        }),
        user_id: joi_1.default.number().integer().positive().optional().messages({
            "number.base": "User ID must be a number.",
            "number.integer": "User ID must be an integer.",
            "number.positive": "User ID must be a positive number.",
        }),
    }),
};
exports.checkPaymentStatusValidation = {
    params: joi_1.default.object().keys({
        pidx: joi_1.default.string().required().messages({
            "string.base": "Payment index (pidx) must be a string.",
            "any.required": "Payment index (pidx) is required.",
        }),
    }),
};
exports.webhookValidation = {
    body: joi_1.default.object().keys({
        event_id: joi_1.default.string().optional(),
        type: joi_1.default.string().required().messages({
            "string.base": "Event type must be a string.",
            "any.required": "Event type is required.",
        }),
        data: joi_1.default.object().required().messages({
            "object.base": "Event data must be an object.",
            "any.required": "Event data is required.",
        }),
    }),
};
