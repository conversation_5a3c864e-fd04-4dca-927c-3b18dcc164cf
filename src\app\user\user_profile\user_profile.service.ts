import httpStatus from "http-status";
import ApiError from "../../../utils/ApiError";
import User from "../../../database/models/user.model";
import UserEducationCareer from "../../../database/models/user_education_career.model";
import UserBasicDetails from "../../../database/models/user_basic_details.model";
import UserAstroDetails from "../../../database/models/user_astro_details.model";
import UserLocationDetails from "../../../database/models/user_location_details.model";
import UserLifestyle from "../../../database/models/user_lifestyle.model";
import UserFamilyDetails from "../../../database/models/user_family_details.model";
import UserVerification from "../../../database/models/user_verifications.model";
import { sequelize } from "../../../database/database";
import { Op, Sequelize } from "sequelize";
import UserHobbies from "../../../database/models/user_hobbies.model";
import UserGallery from "../../../database/models/user_gallery.model";
import UserShortlist from "../../../database/models/user_shortlist.model";
import Country from "../../../database/models/country.model";
import City from "../../../database/models/city.model";
import UserPreference from "../../../database/models/user_preferences.model";

export default class UserProfileService {
  constructor() { }

  static getUserProfiles = async (options: {
    page?: number;
    limit?: number;
    search?: string;
    filters?: any;
    currentUserId?: number;
  }) => {
    try {
      const { page, limit, search, filters = {}, currentUserId } = options;
      console.log('filters: ', filters);
      const whereCondition: any = filters?.member_id
        ? {
          [Op.or]: [
            { member_id: { [Op.like]: `%${filters?.member_id.toLowerCase()}%` } },
          ],
        }
        : {};

      if (currentUserId) {
        whereCondition.id = { [Op.ne]: currentUserId };
        const currentUser = await User.findOne({
          where: { id: currentUserId },
        });
        if (currentUser?.gender === 'male') {
          whereCondition.gender = 'female';
        } else {
          whereCondition.gender = 'male';
        }
      }

      whereCondition.status = 'approved';
      whereCondition.is_hide_profile = false;
      if (filters?.last_name) whereCondition.last_name = filters?.last_name;

      // Sub-filters (joins)
      const basicWhere: any = {};
      const locationWhere: any = {};
      const educationWhere: any = {};
      const lifestyleWhere: any = {};
      const familyWhere: any = {};
      const astroWhere: any = {};
      const hobbiesWhere: any = {};

      // Fill where conditions
      if (filters?.religion) basicWhere.religion = filters.religion;
      if (filters?.caste) basicWhere.caste = filters.caste;
      if (filters?.gotra) basicWhere.gotra = filters.gotra;
      if (filters?.marital_status) basicWhere.marital_status = filters.marital_status;
      if (filters?.country_of_citizenship) basicWhere.country_of_citizenship = filters.country_of_citizenship;

      if (filters?.country_living_in) locationWhere.country_living_in = filters.country_living_in;
      if (filters?.city) locationWhere.city = filters.city;
      if (filters?.residency_status) locationWhere.residency_status = filters.residency_status;

      if (filters?.education) educationWhere.education = filters.education;
      if (filters?.profession) educationWhere.profession = filters.profession;
      if (filters?.employment_status) educationWhere.employment_status = filters.employment_status;

      if (filters?.age && (filters.age.start || filters.age.end)) {
        lifestyleWhere.age = {};
        if (filters.age.start) lifestyleWhere.age[Op.gte] = filters.age.start;
        if (filters.age.end) lifestyleWhere.age[Op.lte] = filters.age.end;
      }
      if (filters?.height_range && (filters.height_range.start || filters.height_range.end)) {
        lifestyleWhere.height_cm = {};
        if (filters.height_range.start) lifestyleWhere.height_cm[Op.gte] = filters.height_range.start;
        if (filters.height_range.end) lifestyleWhere.height_cm[Op.lte] = filters.height_range.end;
      }
      if (filters?.body_type) lifestyleWhere.body_type = filters.body_type;
      if (filters?.complexion) lifestyleWhere.complexion = filters.complexion;
      if (filters?.diet) lifestyleWhere.diet = filters.diet;
      if (filters?.smoking_habit) lifestyleWhere.smoking_habit = filters.smoking_habit;
      if (filters?.drinking_habit) lifestyleWhere.drinking_habit = filters.drinking_habit;
      if (filters?.any_disability) lifestyleWhere.any_disability = filters.any_disability;

      if (filters?.countryOfBirth) astroWhere.countryOfBirth = filters.countryOfBirth;
      if (filters?.birthCity) astroWhere.birthCity = filters.birthCity;

      if (filters?.family_type) familyWhere.family_type = filters.family_type;
      if (filters?.father_occupation) familyWhere.father_occupation = filters.father_occupation;
      if (filters?.mother_occupation) familyWhere.mother_occupation = filters.mother_occupation;
      if (filters?.number_of_siblings) familyWhere.number_of_siblings = filters.number_of_siblings;
      if (filters?.maternal_surname) familyWhere.maternal_surname = filters.maternal_surname;

      if (filters?.hobbies) hobbiesWhere.hobbies = filters.hobbies;
      if (filters?.interests) hobbiesWhere.interests = filters.interests;

      const queryOption: any = {
        where: whereCondition,
        distinct: true,
        // attributes: {
        //   include: [
        //     [
        //       Sequelize.literal("`lifestyle`.`age`"),
        //       "age",
        //     ],
        //     [
        //       Sequelize.literal("`lifestyle`.`height_cm`"),
        //       "height_cm",
        //     ],
        //   ],
        // },
        include: [
          {
            model: UserBasicDetails,
            as: "basicDetails",
            attributes: ["religion", "caste", "gotra", 'marital_status', 'country_of_citizenship'],
            where: Object.keys(basicWhere).length ? basicWhere : undefined,
            include: [
              {
                model: Country,
                as: "country",
                attributes: ['name'],
              },
            ],
          },
          {
            model: UserLocationDetails,
            as: "locationDetails",
            where: Object.keys(locationWhere).length ? locationWhere : undefined,
            attributes: ["city", "country_living_in"],
            include: [
              {
                model: Country,
                as: "country",
                attributes: ['name'],
              },
              {
                model: City,
                as: "cities",
                attributes: ['name'],
              },
            ],
          },
          {
            model: UserEducationCareer,
            as: "educationCareer",
            where: Object.keys(educationWhere).length ? educationWhere : undefined,
            attributes: ["education", "profession"]
          },
          {
            model: UserLifestyle,
            as: "lifestyle",
            where: Object.keys(lifestyleWhere).length ? lifestyleWhere : undefined,
            attributes: ["age", "height_cm"]
          },
          {
            model: UserFamilyDetails,
            as: "familyDetails",
            where: Object.keys(familyWhere).length ? familyWhere : undefined,
            attributes: ["family_type", "father_occupation", "mother_occupation"]
          },
          {
            model: UserHobbies,
            as: "hobbies",
            where: Object.keys(hobbiesWhere).length ? hobbiesWhere : undefined,
            attributes: ["hobbies", "interests"]
          },
          {
            model: UserGallery,
            as: "userGallery",
          },
          {
            model: UserShortlist,
            as: "shortlisted_user"
          }
        ],
        order: [["createdAt", "DESC"]],
      };

      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }
      // Count total items
      const totalItems: any = await User.count({
        where: whereCondition,
      });

      const user = await User.findAll(queryOption);
      if (page && limit) {
        return {
          totalItems: totalItems,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
          user: user,
        };
      } else {
        return user;
      }
    } catch (error: any) {
      console.log('error: ', error);
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  static createUserDetails = async (userBody: any, userId: number) => {
    const transaction = await sequelize.transaction();
    try {
      const {
        verfication,
        familyDetails,
        educationCareer,
        lifestyle,
        basicDetails,
        locationDetails,
        astroDetails,
        hobbies
      } = userBody;

      const upsertModel = async (model: any, data: any) => {
        if (!data) return;
        if (data.id) {
          await model.update(data, { where: { id: data.id }, transaction });
        } else {
          await model.create(data, { transaction });
        }
      };
      let message = "";

      if (basicDetails) {await upsertModel(UserBasicDetails, basicDetails); message = "Basic Details";};
      if (locationDetails) {await upsertModel(UserLocationDetails, locationDetails); message = "Location Details";};
      if (educationCareer) {await upsertModel(UserEducationCareer, educationCareer); message = "Education Details";};
      if (lifestyle) {await upsertModel(UserLifestyle, lifestyle); message = "Lifestyle Details";};
      if (astroDetails) {await upsertModel(UserAstroDetails, astroDetails); message = "Astro Details";};
      if (familyDetails) {await upsertModel(UserFamilyDetails, familyDetails); message = "Family Details";};
      if (hobbies) {await upsertModel(UserHobbies, hobbies); message = "Hobbies";}
      if (verfication) {await upsertModel(UserVerification, verfication); message = "Verification";};
      await transaction.commit();
      const user = await this.getUserProfileById(userId);
      return { user, message };
    } catch (error: any) {
      await transaction.rollback();
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  static getUserProfileById = async (userId: number) => {
    try {
      const user = await User.findOne({
        where: { id: userId },
        include: [
          {
            model: UserBasicDetails,
            as: "basicDetails",
            attributes: {
              include: [
                [
                  Sequelize.literal(
                    "`basicDetails->country`.`name`"
                  ),
                  "country_of_citizenship_name",
                ],
              ],
            },
            include: [
              {
                model: Country,
                as: "country",
                attributes: [],
              },
            ],
          },
          {
            model: UserLocationDetails,
            as: "locationDetails",
            attributes: {
              include: [
                [Sequelize.literal("`locationDetails->country`.`name`"), "country_living_in_name"],
                [Sequelize.literal("`locationDetails->cities`.`name`"), "city_name"],
              ],
            },
            include: [
              {
                model: Country,
                as: "country",
                attributes: [],
              },
              {
                model: City,
                as: "cities",
                attributes: [],
              },
            ],
          },
          {
            model: UserEducationCareer,
            as: "educationCareer",
          },
          {
            model: UserLifestyle,
            as: "lifestyle",
          },
          {
            model: UserAstroDetails,
            as: "astroDetails",
            attributes: {
              include: [
                [Sequelize.literal("`astroDetails->country`.`name`"), "countryOfBirth_name"],
                [Sequelize.literal("`astroDetails->cities`.`name`"), "birthCity_name"],
              ],
            },
            include: [
              {
                model: Country,
                as: "country",
                attributes: [],
              },
              {
                model: City,
                as: "cities",
                attributes: [],
              },
            ],
          },
          {
            model: UserFamilyDetails,
            as: "familyDetails",
          },
          {
            model: UserGallery,
            as: "userGallery",
          },
          {
            model: UserHobbies,
            as: "hobbies",
          },
          {
            model: UserVerification,
            as: "verification",
          },
          {
            model: UserPreference,
            as: "userPreference",
            attributes: ["id"]
          }
        ],
      });

      if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, "User not found");
      }

      return user;
    } catch (error: any) {
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error.message);
    }
  };

  static createUserProfile = async (userProfileBody: any) => {
    try {
      const userProfile: UserGallery = await UserGallery.create(
        userProfileBody
      );
      await UserVerification.update({ is_profile_completed: true }, { where: { user_id: userProfileBody.user_id } });
      return userProfile;
    } catch (error: any) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };

  static getUserSearchProfiles = async (options: {
    page?: number;
    limit?: number;
    search?: string;
    filters?: any;
  }) => {
    try {
      const { page, limit, search, filters = {} } = options;
      console.log('filters: ', filters);
      const whereCondition: any = search
        ? {
          [Op.or]: [
            { firstName: { [Op.like]: `%${search.toLowerCase()}%` } },
          ],
        }
        : {};


      whereCondition.status = 'approved';
      whereCondition.is_hide_profile = false;
      if (filters?.last_name) whereCondition.last_name = filters?.last_name;
      if (filters?.gender) whereCondition.gender = filters.gender;

      // Sub-filters (joins)
      const basicWhere: any = {};
      const locationWhere: any = {};
      const educationWhere: any = {};
      const lifestyleWhere: any = {};
      const familyWhere: any = {};
      const astroWhere: any = {};
      const hobbiesWhere: any = {};

      // Fill where conditions
      if (filters?.religion) basicWhere.religion = filters.religion;
      if (filters?.caste) basicWhere.caste = filters.caste;
      if (filters?.marital_status) basicWhere.marital_status = filters.marital_status;

      if (filters?.country_living_in) locationWhere.country_living_in = filters.country_living_in;
      if (filters?.age && (filters.age.start || filters.age.end)) {
        lifestyleWhere.age = {};
        if (filters.age.start) lifestyleWhere.age[Op.gte] = filters.age.start;
        if (filters.age.end) lifestyleWhere.age[Op.lte] = filters.age.end;
      }

      const queryOption: any = {
        where: whereCondition,
        distinct: true,
        // attributes: {
        //   include: [
        //     [
        //       Sequelize.literal("`lifestyle`.`age`"),
        //       "age",
        //     ],
        //     [
        //       Sequelize.literal("`lifestyle`.`height_cm`"),
        //       "height_cm",
        //     ],
        //   ],
        // },
        include: [
          {
            model: UserBasicDetails,
            as: "basicDetails",
            attributes: ["religion", "caste", "gotra", 'marital_status', 'country_of_citizenship'],
            where: Object.keys(basicWhere).length ? basicWhere : undefined,
            include: [
              {
                model: Country,
                as: "country",
                attributes: ['name'],
              },
            ],
          },
          {
            model: UserLocationDetails,
            as: "locationDetails",
            where: Object.keys(locationWhere).length ? locationWhere : undefined,
            attributes: ["city", "country_living_in"],
            include: [
              {
                model: Country,
                as: "country",
                attributes: ['name'],
              },
              {
                model: City,
                as: "cities",
                attributes: ['name'],
              },
            ],
          },
          {
            model: UserEducationCareer,
            as: "educationCareer",
            where: Object.keys(educationWhere).length ? educationWhere : undefined,
            attributes: ["education", "profession"]
          },
          {
            model: UserLifestyle,
            as: "lifestyle",
            where: Object.keys(lifestyleWhere).length ? lifestyleWhere : undefined,
            attributes: ["age", "height_cm"]
          },
          {
            model: UserFamilyDetails,
            as: "familyDetails",
            where: Object.keys(familyWhere).length ? familyWhere : undefined,
            attributes: ["family_type", "father_occupation", "mother_occupation"]
          },
          {
            model: UserHobbies,
            as: "hobbies",
            where: Object.keys(hobbiesWhere).length ? hobbiesWhere : undefined,
            attributes: ["hobbies", "interests"]
          },
          {
            model: UserShortlist,
            as: "shortlisted_user"
          }
        ],
        order: [["createdAt", "DESC"]],
      };

      if (page && limit) {
        const offset = (page - 1) * limit;
        queryOption.limit = limit;
        queryOption.offset = offset;
      }
      // Count total items
      const totalItems: any = await User.count({
        where: whereCondition,
      });

      const user = await User.findAll(queryOption);
      if (page && limit) {
        return {
          totalItems: totalItems,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
          user: user,
        };
      } else {
        return user;
      }
    } catch (error: any) {
      console.log('error: ', error);
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  };
}
