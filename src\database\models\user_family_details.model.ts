import {
    Table,
    Column,
    Model,
    DataType,
    <PERSON>Key,
    AutoIncrement,
    AllowNull,
    ForeignKey,
    BelongsTo,
    Index,
} from "sequelize-typescript";
import User from "./user.model"; 

interface UserFamilyDetailsI {
    id: number;
    user_id: number;
    family_type?: string;
    family_values?: string;
    family_affluence?: string;
    father_occupation?: string;
    mother_occupation?: string;
    number_of_siblings?: number;
    maternal_surname?: string;
    hisGotra?: string;
}

@Table({
    tableName: "user_family_details",
    timestamps: false,
})
class UserFamilyDetails extends Model<UserFamilyDetailsI> implements UserFamilyDetailsI {
    @PrimaryKey
    @AutoIncrement
    @Column
    id: number;

    @Index
    @ForeignKey(() => User)
    @AllowNull(false)
    @Column
    user_id: number;

    @BelongsTo(() => User, { foreignKey: "user_id", onDelete: "CASCADE", })
    user: User;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    family_type?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    family_values?: string;

    @AllowNull(true)
    @Column(DataType.STRING(50))
    family_affluence?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    father_occupation?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    mother_occupation?: string;

    @AllowNull(true)
    @Column(DataType.INTEGER)
    number_of_siblings?: number;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    maternal_surname?: string;

    @AllowNull(true)
    @Column(DataType.STRING(100))
    hisGotra?: string;
}

export default UserFamilyDetails;