"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const user_nearby_controller_1 = __importDefault(require("../app/user/user_nearby/user_nearby.controller"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
router.get("", auth_1.auth, user_nearby_controller_1.default.getAll);
exports.default = router;
