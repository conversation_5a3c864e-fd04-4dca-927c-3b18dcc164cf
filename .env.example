NODE_ENV=local
PORT=8000

# MasterDB
DATABASE_NAME=
DATABASE_USER=
DATABASE_PASSWORD=
DATABASE_HOST=
DATABASE_PORT=
DATABASE_DIALECT=

TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_VERIFY_SID=your_verify_service_sid


# JWT
# JWT secret key
JWT_SECRET=
# Number of minutes after which an access token expires
JWT_ACCESS_EXPIRATION_MINUTES=30
JWT_REFRESH_EXPIRATION_DAYS=30

#AWS
BUCKET_NAME=

# Socket.io
SOCKET_CORS_ORIGIN=*
SOCKET_PATH=/socket.io