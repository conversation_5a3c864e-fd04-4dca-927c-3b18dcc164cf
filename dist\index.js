"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("dotenv").config();
const server_1 = __importDefault(require("./src/server"));
const database_1 = require("./src/database/database");
const PORT = process.env.PORT || 3300;
// const PORT =  3300;
server_1.default.listen(PORT, () => {
    console.log(`Server is live at ${process.env.HOST}:${PORT}`);
    database_1.sequelize
        .authenticate()
        .then(() => __awaiter(void 0, void 0, void 0, function* () {
        console.log(`database is connected : ${process.env.DATABASE_NAME}`);
        try {
            if (process.env.NODE_ENV === 'local') {
                // await sequelize.sync({ force: true });
                // console.log(`${process.env.DATABASE_NAME} : All the tables was just (re)created!`);
                // await sequelize.sync({ alter: true });  
                console.log(`${process.env.DATABASE_NAME} : All models were synchronized successfully.`);
            }
            else {
                // await sequelize.sync({ alter: true });
                console.log(`${process.env.DATABASE_NAME} : All models were synchronized successfully.`);
            }
        }
        catch (error) {
            throw error;
        }
    }))
        .catch((error) => {
        console.log('database error: ', error.message);
    });
});
