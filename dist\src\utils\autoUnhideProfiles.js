"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runAutoUnhideProfiles = void 0;
const moment_1 = __importDefault(require("moment"));
const privacy_settings_model_1 = __importDefault(require("../database/models/privacy_settings.model"));
const user_service_1 = __importDefault(require("../app/user/user.service"));
const sequelize_1 = require("sequelize");
/**
 * Function to automatically unhide profiles whose visibility period has expired
 * This should be called by a cron job or scheduled task at midnight
 */
const runAutoUnhideProfiles = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Get current date at midnight
        const today = (0, moment_1.default)().startOf('day').toDate();
        // Find all privacy settings where profile_end_date has passed
        const expiredSettings = yield privacy_settings_model_1.default.findAll({
            where: {
                profile_end_date: {
                    [sequelize_1.Op.lt]: today
                },
                // Only consider profiles that are currently hidden
                user_id: {
                    [sequelize_1.Op.in]: sequelize_1.Sequelize.literal(`(SELECT id FROM users WHERE is_hide_profile = true)`)
                }
            }
        });
        let unhiddenCount = 0;
        // Update each user to unhide their profile
        for (const setting of expiredSettings) {
            yield user_service_1.default.updateUserById(setting.user_id, { is_hide_profile: false });
            yield setting.update({ profile_end_date: null, profile_start_date: null, profile_visibility: 0 });
            unhiddenCount++;
        }
        console.log(`Auto-unhidden ${unhiddenCount} expired profiles`);
        return unhiddenCount;
    }
    catch (error) {
        console.error('Error auto-unhiding profiles:', error);
        throw error;
    }
});
exports.runAutoUnhideProfiles = runAutoUnhideProfiles;
